<?php

namespace Sparefoot\MyFootService\Service;

/**
 * Class Login
 * These are utilities that needed to be dried from controllers
 * around Users, Authenticating them, and getting their sessions.
 *
 * This is where we interfere with the StorageAdapter and use
 * temp files instead of sessions when running from the command line.
 */
class Login
{
    /**
     * @param string $email
     * @param string $password
     * @param bool   $rememberMe
     * @param object $adapter    autentication adapter (so we can bypass passwords for tests)
     *
     * @return bool|\Genesis_Entity_UserAccess
     */
    public static function attempt($email, $password, $rememberMe = false, $adapter = null)
    {
        if (!$adapter) {
            $adapter = new \Genesis_Util_AuthAdapter($email, $password);
        }
        $user = $adapter->authenticate(\Genesis_Entity_UserAccess::APP_PITA);
        if (!$user) {
            return false;
        }
        if ($rememberMe) {
            setcookie('rememberme', serialize($user), time() + 60 * 60 * 24 * 365, '/');
        }

        self::getAuthInstance()->getStorage()->write($user);
        self::getUserSession()->setExpirationSeconds(3600);

        return $user;
    }

    /**
     * @return Zend_Session_Namespace
     *                                This was a dry-up of 4 places
     */
    public static function getUserSession()
    {
        return new Zend_Session_Namespace('Zend_Auth');
    }

    /**
     * @return zend_Auth
     *                   This was directly instantiated all over, moved here
     *                   so I could interfere
     */
    public static function getAuthInstance()
    {
        $auth = Zend_Auth::getInstance();
        $auth->setStorage(new Zend_Auth_Storage_Session());
        if (php_sapi_name() === 'cli') {
            $auth->setStorage(new AuthStorageFile());
        }

        return $auth;
    }
}

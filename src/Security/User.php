<?php

namespace Sparefoot\MyFootService\Security;

use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class User implements UserInterface, PasswordAuthenticatedUserInterface
{
    private $genesisUserAccess;
    private $email;
    private $roles = [];
    private $password;

    public function __construct(\Genesis_Entity_UserAccess $genesisUserAccess)
    {
        $this->genesisUserAccess = $genesisUserAccess;
        $this->email = $genesisUserAccess->getEmail();

        // Set roles based on the user's PIT<PERSON> role
        $pitaRole = $this->genesisUserAccess->getPitaRole();
        // $roles = ['ROLE_USER']; // Default role for all authenticated users
        $roles = [];
        switch ($pitaRole) {
            case \Genesis_Entity_UserAccess::ROLE_GOD:
                $roles[] = 'ROLE_GOD';
                break;
            case \Genesis_Entity_UserAccess::ROLE_FACILITYEDITOR:
                $roles[] = 'ROLE_FACILITYEDITOR';
                break;
            case \Genesis_Entity_UserAccess::ROLE_SEARCHANALYST:
                $roles[] = 'ROLE_SEARCHANALYST';
                break;
            default:
                $roles[] = $pitaRole;
                break;
        }
        $this->roles = array_unique($roles);
        $this->password = $genesisUserAccess->getPassword();
    }

    public function getGenesisUserAccess(): \Genesis_Entity_UserAccess
    {
        return $this->genesisUserAccess;
    }

    public function setGenesisUserAccess(\Genesis_Entity_UserAccess $genesisUserAccess): self
    {
        $this->genesisUserAccess = $genesisUserAccess;

        return $this;
    }

    public function getEmail(): ?string
    {
        return $this->email;
    }

    public function setEmail(string $email): self
    {
        $this->email = $email;

        return $this;
    }

    public function getUserIdentifier(): string
    {
        return (string) $this->email;
    }

    public function getUsername(): string
    {
        return $this->getUserIdentifier();
    }

    public function getRoles(): array
    {
        $roles = $this->roles;

        return array_unique($roles);
    }

    public function setRoles(array $roles): self
    {
        $this->roles = $roles;

        return $this;
    }

    public function getPassword(): string
    {
        return $this->password;
    }

    public function setPassword(string $password): self
    {
        $this->password = $password;

        return $this;
    }

    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
    }

    public function getSalt(): ?string
    {
        return null; // Not needed when using bcrypt or argon2i
    }

    /**
     * This method is not part of the UserInterface but is useful for your application.
     * Returns the PITA role of the user.
     *
     * @return string the PITA role of the user
     */
    // This method is not part of the UserInterface but is useful for your application
    // to get the user's PITA role directly from the GenesisUserAccess entity.
    public function getPitaRole(): string
    {
        return $this->genesisUserAccess->getPitaRole();
    }

    /**
     * This method is not part of the UserInterface but is useful for your application.
     * Returns the user ID of the user.
     *
     * @return int the user ID of the user
     */
    public function getId(): int
    {
        return $this->genesisUserAccess->getUserId();
    }

    public function getAccount(): mixed
    {
        return $this->genesisUserAccess->getAccount();
    }

    public function getLastName(): mixed
    {
        return $this->genesisUserAccess->getLastName();
    }

    public function getFirstName(): mixed
    {
        return $this->genesisUserAccess->getFirstName();
    }

    public function getFullName(): mixed
    {
        return $this->genesisUserAccess->getFullName();
    }

    public function getUserId(): int
    {
        return $this->genesisUserAccess->getUserId();
    }
}

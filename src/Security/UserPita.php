<?php

namespace Sparefoot\MyFootService\Security;

use Symfony\Component\Security\Core\User\PasswordAuthenticatedUserInterface;
use Symfony\Component\Security\Core\User\UserInterface;

class UserPita extends \Genesis_Entity_UserAccess implements UserInterface, PasswordAuthenticatedUserInterface, \Serializable
{
    private $genesisUserAccess;
    private $email;
    private $roles = [];

    public function __construct(\Genesis_Entity_UserAccess $genesisUserAccess)
    {
        $this->genesisUserAccess = $genesisUserAccess;
        $this->email = $genesisUserAccess->getEmail();
    }

    public function getPassword(): ?string
    {
        return $this->genesisUserAccess->getPassword();
    }

    public function getRoles(): array
    {
        $pitaRole = $this->genesisUserAccess->getPitaRole();
        $roles = ['ROLE_USER']; // Default role for all authenticated users

        switch ($pitaRole) {
            case \Genesis_Entity_UserAccess::ROLE_GOD:
                $roles[] = 'ROLE_GOD';
                break;
            case \Genesis_Entity_UserAccess::ROLE_FACILITYEDITOR:
                $roles[] = 'ROLE_FACILITYEDITOR';
                break;
            case \Genesis_Entity_UserAccess::ROLE_SEARCHANALYST:
                $roles[] = 'ROLE_SEARCHANALYST';
                break;
            default:
                $roles[] = $pitaRole;
                break;
        }

        return array_unique($roles);
    }

    public function eraseCredentials(): void
    {
        // If you store any temporary, sensitive data on the user, clear it here
    }

    public function getUserIdentifier(): string
    {
        return $this->genesisUserAccess->getEmail();
    }

    public function serialize()
    {
        return serialize([
            $this->email,
            $this->roles,
            $this->genesisUserAccess,
        ]);
    }

    public function unserialize($serialized)
    {
        list(
            $this->email,
            $this->roles,
            $this->genesisUserAccess,
        ) = unserialize($serialized, ['allowed_classes' => true]);
    }
}

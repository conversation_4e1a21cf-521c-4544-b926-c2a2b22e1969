<?php

namespace Sparefoot\MyFootService\Security;

use Symfony\Component\HttpFoundation\RedirectResponse;
use Symfony\Component\HttpFoundation\Request;
use Symfony\Component\HttpFoundation\Response;
use Symfony\Component\Routing\Generator\UrlGeneratorInterface;
use Symfony\Component\Security\Core\Authentication\Token\TokenInterface;
use Symfony\Component\Security\Core\Exception\AuthenticationException;
use Symfony\Component\Security\Core\Security;
use Symfony\Component\Security\Csrf\CsrfToken;
use Symfony\Component\Security\Csrf\CsrfTokenManagerInterface;
use Symfony\Component\Security\Http\Authenticator\AbstractAuthenticator;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\RememberMeBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Badge\UserBadge;
use Symfony\Component\Security\Http\Authenticator\Passport\Credentials\PasswordCredentials;
use Symfony\Component\Security\Http\Authenticator\Passport\Passport;

class CustomAuthenticator extends AbstractAuthenticator
{
    private UrlGeneratorInterface $urlGenerator;
    private $csrfTokenManager;

    private $roleRedirects = [
        \Genesis_Entity_UserAccess::ROLE_GOD => 'dashboard_bookings',
        \Genesis_Entity_UserAccess::ROLE_FACILITYEDITOR => 'inventory_index',
        \Genesis_Entity_UserAccess::ROLE_SEARCHANALYST => 'search_index',
    ];

    public function __construct(
        CsrfTokenManagerInterface $csrfTokenManager,
        UrlGeneratorInterface $urlGenerator,
    ) {
        $this->csrfTokenManager = $csrfTokenManager;
        $this->urlGenerator = $urlGenerator;
    }

    public function supports(Request $request): ?bool
    {
        return $request->attributes->get('_route') === 'login_check' && $request->isMethod('POST');
    }

    public function authenticate(Request $request): Passport
    {
        // if (isset($_ENV['APP_ENV']) && $_ENV['APP_ENV'] === 'local') {
        //     // In local environment, use the fake local authentication

        //     return $this->authenticateFakeLocal($request);
        // }

        $csrfToken = $request->request->get('_csrf_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('authenticate', $csrfToken))) {
            throw new AuthenticationException('Invalid CSRF token.');
        }

        $email = $request->request->get('_username');
        $password = $request->request->get('_password');
        if (empty($email)) {
            throw new AuthenticationException('Email cannot be empty');
        }
        $adapter = new \Genesis_Util_AuthAdapter($email, $password);

        $genesisUser = $adapter->authenticate(\Genesis_Entity_UserAccess::APP_PITA);

        if (!$genesisUser) {
            throw new AuthenticationException('Invalid credentials');
        }

        $request->getSession()->set(Security::LAST_USERNAME, $genesisUser->getEmail());

        $badges = [];
        if ($request->request->get('_remember_me')) {
            $badges[] = new RememberMeBadge();
        }

        return new Passport(
            new UserBadge($email),
            new PasswordCredentials($password),
            $badges
        );
    }

    public function onAuthenticationSuccess(Request $request, TokenInterface $token, string $firewallName): ?Response
    {
        $user = $token->getUser();
        if (!$user instanceof User) {
            throw new \LogicException('The user must be an instance of Sparefoot\MyFootService\Security\User.');
        }
        // Check if user has a role defined in our redirects map
        if ($user && isset($this->roleRedirects[$user->getPitaRole()])) {
            $redirectPath = $this->roleRedirects[$user->getPitaRole()];

            return new RedirectResponse($this->urlGenerator->generate($redirectPath));
        }

        // Default redirect if no specific role redirect is found
        return new RedirectResponse($this->urlGenerator->generate('inventory_index'));
    }

    public function onAuthenticationFailure(Request $request, AuthenticationException $exception): ?Response
    {
        if ($request->hasSession()) {
            $request->getSession()->set('security.authentication_error', $exception);
        }

        // Redirect to the login page with an error message
        return new RedirectResponse($this->urlGenerator->generate('login_index', [
            'error' => 'invalid_credentials',
        ]));
    }

    /**
     * This method is used for local environment authentication.
     * It simulates the authentication process without actually checking credentials.
     */
    public function authenticateFakeLocal(Request $request): void
    {
        /*
        $csrfToken = $request->request->get('_csrf_token');
        if (!$this->csrfTokenManager->isTokenValid(new CsrfToken('authenticate', $csrfToken))) {
            throw new AuthenticationException('Invalid CSRF token.');
        }

        $email = $request->request->get('_username');
        $password = $request->request->get('_password');
        if (empty($email)) {
            throw new AuthenticationException('Email cannot be empty');
        }

        $request->getSession()->set(Security::LAST_USERNAME, $email);

        $badges = [];
        if ($request->request->get('_remember_me')) {
            $badges[] = new RememberMeBadge();
        }

        return new Passport(
            new UserBadge($email),
            new PasswordCredentials($password),
            $badges
        );
        */
    }
}

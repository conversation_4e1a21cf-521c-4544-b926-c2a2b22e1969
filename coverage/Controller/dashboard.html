<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/Controller</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="index.html">Controller</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbstractGenericController.php.html#9">Sparefoot\PitaService\Controller\AbstractGenericController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#14">Sparefoot\PitaService\Controller\AbstractRestrictedController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#10">Sparefoot\PitaService\Controller\AbstractView\GenericView</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#10">Sparefoot\PitaService\Controller\AccountController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#11">Sparefoot\PitaService\Controller\AccountView\ListActionView</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#11">Sparefoot\PitaService\Controller\AffiliateController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#15">Sparefoot\PitaService\Controller\AnalyticsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#9">Sparefoot\PitaService\Controller\ApiController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#12">Sparefoot\PitaService\Controller\BillingController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#10">Sparefoot\PitaService\Controller\BookingsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#11">Sparefoot\PitaService\Controller\BuyoutController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#9">Sparefoot\PitaService\Controller\CdpController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchController.php.html#8">Sparefoot\PitaService\Controller\CentaursearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#9">Sparefoot\PitaService\Controller\CentaursearchapiController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#9">Sparefoot\PitaService\Controller\CitygridController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#13">Sparefoot\PitaService\Controller\CpaPercentRolloutController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutView/IndexActionView.php.html#11">Sparefoot\PitaService\Controller\CpaPercentRolloutView\IndexActionView</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#10">Sparefoot\PitaService\Controller\DashboardController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#10">Sparefoot\PitaService\Controller\DisastersController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#12">Sparefoot\PitaService\Controller\DisputesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#9">Sparefoot\PitaService\Controller\EmailController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ErrorController.php.html#11">Sparefoot\PitaService\Controller\ErrorController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#10">Sparefoot\PitaService\Controller\FacilitiesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#26">Sparefoot\PitaService\Controller\FacilityController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#10">Sparefoot\PitaService\Controller\FaqsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FederatedController.php.html#11">Sparefoot\PitaService\Controller\FederatedController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#11">Sparefoot\PitaService\Controller\FeedsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FiltersController.php.html#11">Sparefoot\PitaService\Controller\FiltersController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HeatmapController.php.html#10">Sparefoot\PitaService\Controller\HeatmapController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#11">Sparefoot\PitaService\Controller\IncentivesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#12">Sparefoot\PitaService\Controller\IndexController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexView/IndexActionView.php.html#10">Sparefoot\PitaService\Controller\IndexView\IndexActionView</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#13">Sparefoot\PitaService\Controller\InventoryController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#16">Sparefoot\PitaService\Controller\JobsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#12">Sparefoot\PitaService\Controller\LoginController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#9">Sparefoot\PitaService\Controller\MaintenanceModeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NetsuiteController.php.html#10">Sparefoot\PitaService\Controller\NetsuiteController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#9">Sparefoot\PitaService\Controller\OmnomController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#10">Sparefoot\PitaService\Controller\PaidmediaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#12">Sparefoot\PitaService\Controller\PayoutController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PhotosController.php.html#10">Sparefoot\PitaService\Controller\PhotosController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PingController.php.html#10">Sparefoot\PitaService\Controller\PingController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PitaSearchController.php.html#13">Sparefoot\PitaService\Controller\PitaSearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#16">Sparefoot\PitaService\Controller\ProxyController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#13">Sparefoot\PitaService\Controller\PublicController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#11">Sparefoot\PitaService\Controller\QuickClientController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#8">Sparefoot\PitaService\Controller\QuickformController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickjobController.php.html#10">Sparefoot\PitaService\Controller\QuickjobController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#15">Sparefoot\PitaService\Controller\QuickrepController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#16">Sparefoot\PitaService\Controller\QuicksoapController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#10">Sparefoot\PitaService\Controller\QuicktaggerController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#9">Sparefoot\PitaService\Controller\ReferralsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#14">Sparefoot\PitaService\Controller\ReportsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#10">Sparefoot\PitaService\Controller\ReviewsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#9">Sparefoot\PitaService\Controller\RewardsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#9">Sparefoot\PitaService\Controller\SalesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesforceController.php.html#11">Sparefoot\PitaService\Controller\SalesforceController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#9">Sparefoot\PitaService\Controller\SearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#10">Sparefoot\PitaService\Controller\ServiceareaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#9">Sparefoot\PitaService\Controller\SoftwarepartnerController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#9">Sparefoot\PitaService\Controller\SphinxsearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#15">Sparefoot\PitaService\Controller\StatementsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#13">Sparefoot\PitaService\Controller\TableauController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#10">Sparefoot\PitaService\Controller\TestController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#14">Sparefoot\PitaService\Controller\ToolsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TriController.php.html#9">Sparefoot\PitaService\Controller\TriController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#13">Sparefoot\PitaService\Controller\UserController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#12">Sparefoot\PitaService\Controller\UtilitiesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#9">Sparefoot\PitaService\Controller\YellowpagesController</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="InventoryController.php.html#13">Sparefoot\PitaService\Controller\InventoryController</a></td><td class="text-right">401322</td></tr>
       <tr><td><a href="AccountController.php.html#10">Sparefoot\PitaService\Controller\AccountController</a></td><td class="text-right">118680</td></tr>
       <tr><td><a href="UserController.php.html#13">Sparefoot\PitaService\Controller\UserController</a></td><td class="text-right">46440</td></tr>
       <tr><td><a href="ToolsController.php.html#14">Sparefoot\PitaService\Controller\ToolsController</a></td><td class="text-right">28392</td></tr>
       <tr><td><a href="FacilityController.php.html#26">Sparefoot\PitaService\Controller\FacilityController</a></td><td class="text-right">16002</td></tr>
       <tr><td><a href="BookingsController.php.html#10">Sparefoot\PitaService\Controller\BookingsController</a></td><td class="text-right">15750</td></tr>
       <tr><td><a href="ApiController.php.html#9">Sparefoot\PitaService\Controller\ApiController</a></td><td class="text-right">14762</td></tr>
       <tr><td><a href="AnalyticsController.php.html#15">Sparefoot\PitaService\Controller\AnalyticsController</a></td><td class="text-right">7482</td></tr>
       <tr><td><a href="JobsController.php.html#16">Sparefoot\PitaService\Controller\JobsController</a></td><td class="text-right">6806</td></tr>
       <tr><td><a href="DisputesController.php.html#12">Sparefoot\PitaService\Controller\DisputesController</a></td><td class="text-right">3906</td></tr>
       <tr><td><a href="TableauController.php.html#13">Sparefoot\PitaService\Controller\TableauController</a></td><td class="text-right">3192</td></tr>
       <tr><td><a href="BuyoutController.php.html#11">Sparefoot\PitaService\Controller\BuyoutController</a></td><td class="text-right">2652</td></tr>
       <tr><td><a href="FiltersController.php.html#11">Sparefoot\PitaService\Controller\FiltersController</a></td><td class="text-right">2450</td></tr>
       <tr><td><a href="StatementsController.php.html#15">Sparefoot\PitaService\Controller\StatementsController</a></td><td class="text-right">2352</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#9">Sparefoot\PitaService\Controller\AbstractGenericController</a></td><td class="text-right">2256</td></tr>
       <tr><td><a href="YellowpagesController.php.html#9">Sparefoot\PitaService\Controller\YellowpagesController</a></td><td class="text-right">1806</td></tr>
       <tr><td><a href="SalesforceController.php.html#11">Sparefoot\PitaService\Controller\SalesforceController</a></td><td class="text-right">1560</td></tr>
       <tr><td><a href="AffiliateController.php.html#11">Sparefoot\PitaService\Controller\AffiliateController</a></td><td class="text-right">1406</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#14">Sparefoot\PitaService\Controller\AbstractRestrictedController</a></td><td class="text-right">1190</td></tr>
       <tr><td><a href="QuickClientController.php.html#11">Sparefoot\PitaService\Controller\QuickClientController</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="FacilitiesController.php.html#10">Sparefoot\PitaService\Controller\FacilitiesController</a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="IncentivesController.php.html#11">Sparefoot\PitaService\Controller\IncentivesController</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="TestController.php.html#10">Sparefoot\PitaService\Controller\TestController</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="QuickrepController.php.html#15">Sparefoot\PitaService\Controller\QuickrepController</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="ReviewsController.php.html#10">Sparefoot\PitaService\Controller\ReviewsController</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="SearchController.php.html#9">Sparefoot\PitaService\Controller\SearchController</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#9">Sparefoot\PitaService\Controller\CentaursearchapiController</a></td><td class="text-right">552</td></tr>
       <tr><td><a href="OmnomController.php.html#9">Sparefoot\PitaService\Controller\OmnomController</a></td><td class="text-right">552</td></tr>
       <tr><td><a href="CdpController.php.html#9">Sparefoot\PitaService\Controller\CdpController</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="ProxyController.php.html#16">Sparefoot\PitaService\Controller\ProxyController</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="DashboardController.php.html#10">Sparefoot\PitaService\Controller\DashboardController</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="PaidmediaController.php.html#10">Sparefoot\PitaService\Controller\PaidmediaController</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="PublicController.php.html#13">Sparefoot\PitaService\Controller\PublicController</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="ReportsController.php.html#14">Sparefoot\PitaService\Controller\ReportsController</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#10">Sparefoot\PitaService\Controller\AbstractView\GenericView</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="PayoutController.php.html#12">Sparefoot\PitaService\Controller\PayoutController</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="PitaSearchController.php.html#13">Sparefoot\PitaService\Controller\PitaSearchController</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#10">Sparefoot\PitaService\Controller\QuicktaggerController</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="UtilitiesController.php.html#12">Sparefoot\PitaService\Controller\UtilitiesController</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="IndexController.php.html#12">Sparefoot\PitaService\Controller\IndexController</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#11">Sparefoot\PitaService\Controller\AccountView\ListActionView</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ServiceareaController.php.html#10">Sparefoot\PitaService\Controller\ServiceareaController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#9">Sparefoot\PitaService\Controller\SphinxsearchController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="BillingController.php.html#12">Sparefoot\PitaService\Controller\BillingController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="LoginController.php.html#12">Sparefoot\PitaService\Controller\LoginController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="DisastersController.php.html#10">Sparefoot\PitaService\Controller\DisastersController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="FeedsController.php.html#11">Sparefoot\PitaService\Controller\FeedsController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="NetsuiteController.php.html#10">Sparefoot\PitaService\Controller\NetsuiteController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="PhotosController.php.html#10">Sparefoot\PitaService\Controller\PhotosController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="RewardsController.php.html#9">Sparefoot\PitaService\Controller\RewardsController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="EmailController.php.html#9">Sparefoot\PitaService\Controller\EmailController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="FaqsController.php.html#10">Sparefoot\PitaService\Controller\FaqsController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ErrorController.php.html#11">Sparefoot\PitaService\Controller\ErrorController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SalesController.php.html#9">Sparefoot\PitaService\Controller\SalesController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="QuicksoapController.php.html#16">Sparefoot\PitaService\Controller\QuicksoapController</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ReferralsController.php.html#9">Sparefoot\PitaService\Controller\ReferralsController</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#9">Sparefoot\PitaService\Controller\SoftwarepartnerController</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#9">Sparefoot\PitaService\Controller\MaintenanceModeController</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="FederatedController.php.html#11">Sparefoot\PitaService\Controller\FederatedController</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="PingController.php.html#10">Sparefoot\PitaService\Controller\PingController</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbstractGenericController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::getMetrics">getMetrics</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#44"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::getFilters">getFilters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#49"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::getCustoms">getCustoms</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#60"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#96"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::_exportReport">_exportReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#113"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::_exportServerReport">_exportServerReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#140"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processQueryParams">processQueryParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#168"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::newBuilder">newBuilder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::newQuery">newQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#187"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processQuery">processQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#195"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processReport">processReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#222"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::getDefaultQuery">getDefaultQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#227"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::tableFromReport">tableFromReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#251"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::initFilterList">initFilterList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#278"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::populateFilter">populateFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#306"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::toCamelCase">toCamelCase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#55"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::init">init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::initGeneralData">initGeneralData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#109"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::setTitlePage">setTitlePage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#115"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::initHeadElement">initHeadElement</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#168"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::setScripts">setScripts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#173"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getScripts">getScripts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::scriptFooter">scriptFooter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#195"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::_createAcronym">_createAcronym</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#220"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::initAuthorization">initAuthorization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#287"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::authenticateUser">authenticateUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#299"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getLoggedUser">getLoggedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#304"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getGenesisUserAccess">getGenesisUserAccess</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#309"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getParam">getParam</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#319"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::_getParam">_getParam</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#329"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::accessDeniedNotUse">accessDeniedNotUse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#342"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::accessDenied">accessDenied</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#350"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getTwig">getTwig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#362"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::dispatchError">dispatchError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#379"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::dispatchSuccess">dispatchSuccess</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#401"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getControllerActionName">getControllerActionName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#12"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::get_class">get_class</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#17"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::implode">implode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#23"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisUtilVersionerVersion">genesisUtilVersionerVersion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#28"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::strtotime">strtotime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#34"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::newDateTime">newDateTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#39"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::formatEstimatedMoney">formatEstimatedMoney</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#45"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisUtilFormatterFormatDateDiff">genesisUtilFormatterFormatDateDiff</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#58"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::getMetricInfoGetter">getMetricInfoGetter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#67"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisConfigServerIsProduction">genesisConfigServerIsProduction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#73"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisEntitySiteCommission">genesisEntitySiteCommission</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#90"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisConst">genesisConst</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#107"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::dateFormat">dateFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#112"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::getEnv">getEnv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#14"><abbr title="Sparefoot\PitaService\Controller\AccountController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\AccountController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#31"><abbr title="Sparefoot\PitaService\Controller\AccountController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\AccountController::_export">_export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\AccountController::moveintegrationAction">moveintegrationAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#269"><abbr title="Sparefoot\PitaService\Controller\AccountController::moveacctmgmtusersAction">moveacctmgmtusersAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#303"><abbr title="Sparefoot\PitaService\Controller\AccountController::syncAction">syncAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#367"><abbr title="Sparefoot\PitaService\Controller\AccountController::_resetSync">_resetSync</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#389"><abbr title="Sparefoot\PitaService\Controller\AccountController::changebidtypeAction">changebidtypeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#414"><abbr title="Sparefoot\PitaService\Controller\AccountController::changesupportexistingltvreservationsAction">changesupportexistingltvreservationsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#440"><abbr title="Sparefoot\PitaService\Controller\AccountController::changebidsAction">changebidsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#490"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeaccountminimumbidAction">changeaccountminimumbidAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#520"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeminimumbidsAction">changeminimumbidsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#557"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeexclusiveAction">changeexclusiveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#584"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeprimarycontactAction">changeprimarycontactAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#627"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeofflinereservationscdpAction">changeofflinereservationscdpAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#657"><abbr title="Sparefoot\PitaService\Controller\AccountController::verifyAction">verifyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#747"><abbr title="Sparefoot\PitaService\Controller\AccountController::getintegrationdetailsAction">getintegrationdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#787"><abbr title="Sparefoot\PitaService\Controller\AccountController::unverifyAction">unverifyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#807"><abbr title="Sparefoot\PitaService\Controller\AccountController::editinfoAction">editinfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#840"><abbr title="Sparefoot\PitaService\Controller\AccountController::getacctdetailsAction">getacctdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#903"><abbr title="Sparefoot\PitaService\Controller\AccountController::removeacctuserAction">removeacctuserAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#929"><abbr title="Sparefoot\PitaService\Controller\AccountController::removeadminAction">removeadminAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#953"><abbr title="Sparefoot\PitaService\Controller\AccountController::addacctadminAction">addacctadminAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#986"><abbr title="Sparefoot\PitaService\Controller\AccountController::newacctAction">newacctAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1251"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeUser">_makeUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1274"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeAcctMgmtUser">_makeAcctMgmtUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1286"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeIntegration">_makeIntegration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1488"><abbr title="Sparefoot\PitaService\Controller\AccountController::deleteaccountAction">deleteaccountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1527"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeintegrationcredsAction">changeintegrationcredsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1813"><abbr title="Sparefoot\PitaService\Controller\AccountController::getintegrationcredsAction">getintegrationcredsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1902"><abbr title="Sparefoot\PitaService\Controller\AccountController::geteditintegrationdetailsAction">geteditintegrationdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1949"><abbr title="Sparefoot\PitaService\Controller\AccountController::saveeditintegrationdetailsAction">saveeditintegrationdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1999"><abbr title="Sparefoot\PitaService\Controller\AccountController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2024"><abbr title="Sparefoot\PitaService\Controller\AccountController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2051"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentitiesAction">getbillableentitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2124"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentitydetailsAction">getbillableentitydetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2172"><abbr title="Sparefoot\PitaService\Controller\AccountController::updateqbcustomerAction">updateqbcustomerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2268"><abbr title="Sparefoot\PitaService\Controller\AccountController::updatenscustomerAction">updatenscustomerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2399"><abbr title="Sparefoot\PitaService\Controller\AccountController::loadFacilityIdToFacilityMapForBillableEntity">loadFacilityIdToFacilityMapForBillableEntity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2411"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentityfacilitiesAction">getbillableentityfacilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2465"><abbr title="Sparefoot\PitaService\Controller\AccountController::updateadnetworkpriceAction">updateadnetworkpriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2499"><abbr title="Sparefoot\PitaService\Controller\AccountController::updatehostedwebsitepriceAction">updatehostedwebsitepriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2534"><abbr title="Sparefoot\PitaService\Controller\AccountController::changedocusigncompleteAction">changedocusigncompleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2559"><abbr title="Sparefoot\PitaService\Controller\AccountController::changetestaccountAction">changetestaccountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2579"><abbr title="Sparefoot\PitaService\Controller\AccountController::clearaccounttermsAction">clearaccounttermsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#20"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getBidTypeContinueResidualMessage">getBidTypeContinueResidualMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#37"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getAdminsHtml">getAdminsHtml</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#64"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::numCoporations">numCoporations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#76"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::numFacilities">numFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#88"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::bidCurrency">bidCurrency</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#105"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::labelText">labelText</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#132"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::formatDate">formatDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#146"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getPaymentTypes">getPaymentTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#54"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::generateapiAction">generateapiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#69"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_generateApiKey">_generateApiKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#94"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processCreate">_processCreate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#124"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::updateAction">updateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#158"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processUpdate">_processUpdate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::addsiteAction">addsiteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::updatesiteAction">updatesiteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#231"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::exclusionsAction">exclusionsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#266"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processSaveSite">_processSaveSite</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#300"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processAddExclusion">_processAddExclusion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#311"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processDelExclusion">_processDelExclusion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#31"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#71"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::sqlAction">sqlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#84"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::savequeryAction">savequeryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#94"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::loadqueryAction">loadqueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#156"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_export">_export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#313"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_getDefaultQuery">_getDefaultQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#328"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_processQuery">_processQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#428"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_processReport">_processReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#456"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_buildSql">_buildSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\ApiController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#36"><abbr title="Sparefoot\PitaService\Controller\ApiController::unitsAction">unitsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#153"><abbr title="Sparefoot\PitaService\Controller\ApiController::exportunitapiAction">exportunitapiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#419"><abbr title="Sparefoot\PitaService\Controller\ApiController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#473"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSiteLinkFacilityData">_pullSiteLinkFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#486"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullQuikstorFacilityData">_pullQuikstorFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#498"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDoorswapFacilityData">_pullDoorswapFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#520"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStoredgeFacilityData">_pullStoredgeFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#527"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershiftFacilityData">_pullCentershiftFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#539"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStorageMartFacilityData">_pullStorageMartFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#601"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStorageMartUnitData">_pullStorageMartUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#649"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershiftUnitData">_pullCentershiftUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#675"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullUncleBobsUnitData">_pullUncleBobsUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#693"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershift4UnitData">_pullCentershift4UnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#723"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullUsiUnitData">_pullUsiUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#735"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCubesmartUnitData">_pullCubesmartUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#755"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSelfStorageManagerUnitData">_pullSelfStorageManagerUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#779"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStoredgeUnitData">_pullStoredgeUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#791"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDoorswapUnitData">_pullDoorswapUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#819"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullEasyStorageSolutionsUnits">_pullEasyStorageSolutionsUnits</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#854"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullExtraspaceUnits">_pullExtraspaceUnits</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#881"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSiteLinkUnitData">_pullSiteLinkUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#906"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullQuickstorUnitData">_pullQuickstorUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#932"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDomicoUnitData">_pullDomicoUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\BillingController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\BillingController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#33"><abbr title="Sparefoot\PitaService\Controller\BillingController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#50"><abbr title="Sparefoot\PitaService\Controller\BillingController::exportemailsAction">exportemailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#80"><abbr title="Sparefoot\PitaService\Controller\BillingController::openAction">openAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#97"><abbr title="Sparefoot\PitaService\Controller\BillingController::closeAction">closeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#114"><abbr title="Sparefoot\PitaService\Controller\BillingController::deleteAction">deleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#130"><abbr title="Sparefoot\PitaService\Controller\BillingController::_runQuery">_runQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\BookingsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#30"><abbr title="Sparefoot\PitaService\Controller\BookingsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\BookingsController::confcodeAction">confcodeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#106"><abbr title="Sparefoot\PitaService\Controller\BookingsController::salesforceAction">salesforceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#162"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getqueryAction">getqueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#177"><abbr title="Sparefoot\PitaService\Controller\BookingsController::historyAction">historyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#195"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getbookinginfoAction">getbookinginfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#273"><abbr title="Sparefoot\PitaService\Controller\BookingsController::jsondetailsAction">jsondetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#312"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changemoveinAction">changemoveinAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#340"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changeunitpriceAction">changeunitpriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#359"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changemoveoutAction">changemoveoutAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#384"><abbr title="Sparefoot\PitaService\Controller\BookingsController::addduplicateAction">addduplicateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#444"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changebookingsAction">changebookingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#519"><abbr title="Sparefoot\PitaService\Controller\BookingsController::_changeBookingState">_changeBookingState</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#586"><abbr title="Sparefoot\PitaService\Controller\BookingsController::makebookingresidualAction">makebookingresidualAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#609"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#833"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getunitinfoAction">getunitinfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#869"><abbr title="Sparefoot\PitaService\Controller\BookingsController::previewemailsAction">previewemailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#909"><abbr title="Sparefoot\PitaService\Controller\BookingsController::resendemailsAction">resendemailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#946"><abbr title="Sparefoot\PitaService\Controller\BookingsController::moreActionsAction">moreActionsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#959"><abbr title="Sparefoot\PitaService\Controller\BookingsController::sitelinkledgerAction">sitelinkledgerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#41"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#49"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#68"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#118"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editAction">editAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#162"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::viewAction">viewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#183"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::invoiceAction">invoiceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#209"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::deleteAction">deleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#230"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::downloadAction">downloadAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#274"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editUpdateAction">editUpdateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#301"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editUpdateQuoteAction">editUpdateQuoteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#312"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editBookingsAction">editBookingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#334"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_updatePendingBookings">_updatePendingBookings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#373"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_updateExistingTenants">_updateExistingTenants</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\CdpController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\CdpController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#33"><abbr title="Sparefoot\PitaService\Controller\CdpController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchRunsAction">matchRunsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#73"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchAccountsAction">matchAccountsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#94"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchFacilitiesAction">matchFacilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#132"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchBookingsAction">matchBookingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#158"><abbr title="Sparefoot\PitaService\Controller\CdpController::killMatchRunAction">killMatchRunAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#184"><abbr title="Sparefoot\PitaService\Controller\CdpController::viewLogAction">viewLogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#211"><abbr title="Sparefoot\PitaService\Controller\CdpController::runCdpAction">runCdpAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchController.php.html#10"><abbr title="Sparefoot\PitaService\Controller\CentaursearchController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#41"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_getApiResponse">_getApiResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#56"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_getApiResponseTest">_getApiResponseTest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#93"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#109"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#115"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::testAction">testAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#207"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_initWeights">_initWeights</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#216"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\CitygridController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\CitygridController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\CitygridController::resyncAction">resyncAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#38"><abbr title="Sparefoot\PitaService\Controller\CitygridController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#45"><abbr title="Sparefoot\PitaService\Controller\CitygridController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#61"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutController::dashboardAction">dashboardAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutView/IndexActionView.php.html#14"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutView\IndexActionView::usortReports">usortReports</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#14"><abbr title="Sparefoot\PitaService\Controller\DashboardController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\DashboardController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\DashboardController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#45"><abbr title="Sparefoot\PitaService\Controller\DashboardController::_loadData">_loadData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#106"><abbr title="Sparefoot\PitaService\Controller\DashboardController::initDashboardLayout">initDashboardLayout</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#115"><abbr title="Sparefoot\PitaService\Controller\DashboardController::getAction">getAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#120"><abbr title="Sparefoot\PitaService\Controller\DashboardController::bookingsAction">bookingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#186"><abbr title="Sparefoot\PitaService\Controller\DashboardController::initDataForBookingsView">initDataForBookingsView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#191"><abbr title="Sparefoot\PitaService\Controller\DashboardController::initDailyBookingsProjection">initDailyBookingsProjection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\DisastersController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\DisastersController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\DisastersController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#64"><abbr title="Sparefoot\PitaService\Controller\DisastersController::includeContainedFacilities">includeContainedFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#114"><abbr title="Sparefoot\PitaService\Controller\DisastersController::addDisasterAction">addDisasterAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\DisastersController::editDisasterAction">editDisasterAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\DisputesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#51"><abbr title="Sparefoot\PitaService\Controller\DisputesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#65"><abbr title="Sparefoot\PitaService\Controller\DisputesController::opendisputesAction">opendisputesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#102"><abbr title="Sparefoot\PitaService\Controller\DisputesController::closeddisputesAction">closeddisputesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#139"><abbr title="Sparefoot\PitaService\Controller\DisputesController::reviewdisputeAction">reviewdisputeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#161"><abbr title="Sparefoot\PitaService\Controller\DisputesController::applyRuling">applyRuling</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#226"><abbr title="Sparefoot\PitaService\Controller\DisputesController::validateBookingIsReviewable">validateBookingIsReviewable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#251"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeRuling">updateDisputeRuling</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#280"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewRulingReason">updateDisputeReviewRulingReason</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#296"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewNotes">updateDisputeReviewNotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#312"><abbr title="Sparefoot\PitaService\Controller\DisputesController::deleteDisputeReviewNotes">deleteDisputeReviewNotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#321"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewAgent">updateDisputeReviewAgent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#345"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_exportOpenDisputes">_exportOpenDisputes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#358"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_exportClosedDisputes">_exportClosedDisputes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#371"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_createCsv">_createCsv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#387"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_getCsvHeaders">_getCsvHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#430"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_getCsvLine">_getCsvLine</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#488"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_cleanCsvLine">_cleanCsvLine</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#496"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_getDefaultQuery">_getDefaultQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#503"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_processQuery">_processQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#512"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_processOpenDisputesReport">_processOpenDisputesReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#523"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_processClosedDisputesReport">_processClosedDisputesReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\EmailController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\EmailController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#53"><abbr title="Sparefoot\PitaService\Controller\EmailController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\EmailController::ajaxemailsAction">ajaxemailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#92"><abbr title="Sparefoot\PitaService\Controller\EmailController::ajaxsendAction">ajaxsendAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ErrorController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\ErrorController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ErrorController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\ErrorController::errorAction">errorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ErrorController.php.html#65"><abbr title="Sparefoot\PitaService\Controller\ErrorController::hipchatAction">hipchatAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#33"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#120"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::bysourceAction">bysourceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#148"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::getqueryAction">getqueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#186"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::loadbyidAction">loadbyidAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#33"><abbr title="Sparefoot\PitaService\Controller\FacilityController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#79"><abbr title="Sparefoot\PitaService\Controller\FacilityController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#154"><abbr title="Sparefoot\PitaService\Controller\FacilityController::acquiretwilionumberAction">acquiretwilionumberAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#200"><abbr title="Sparefoot\PitaService\Controller\FacilityController::createJsonResponse">createJsonResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#213"><abbr title="Sparefoot\PitaService\Controller\FacilityController::releasetwilionumberAction">releasetwilionumberAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#250"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_fetchInventoryData">_fetchInventoryData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#287"><abbr title="Sparefoot\PitaService\Controller\FacilityController::jsonunitlistdetailsAction">jsonunitlistdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#339"><abbr title="Sparefoot\PitaService\Controller\FacilityController::jsonunitdetailsAction">jsonunitdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#408"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_loadFacilities">_loadFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#555"><abbr title="Sparefoot\PitaService\Controller\FacilityController::getqueryAction">getqueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#573"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_tableFromReport">_tableFromReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#659"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_initFilter">_initFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#707"><abbr title="Sparefoot\PitaService\Controller\FacilityController::directoryAction">directoryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#723"><abbr title="Sparefoot\PitaService\Controller\FacilityController::activateAction">activateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#752"><abbr title="Sparefoot\PitaService\Controller\FacilityController::deactivateAction">deactivateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#781"><abbr title="Sparefoot\PitaService\Controller\FacilityController::exportdirectoryrecordingsAction">exportdirectoryrecordingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#846"><abbr title="Sparefoot\PitaService\Controller\FacilityController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#863"><abbr title="Sparefoot\PitaService\Controller\FacilityController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\FaqsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#28"><abbr title="Sparefoot\PitaService\Controller\FaqsController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#33"><abbr title="Sparefoot\PitaService\Controller\FaqsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\FaqsController::getAction">getAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#67"><abbr title="Sparefoot\PitaService\Controller\FaqsController::saveAction">saveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FederatedController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\FederatedController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FederatedController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\FederatedController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FederatedController.php.html#41"><abbr title="Sparefoot\PitaService\Controller\FederatedController::awsAction">awsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#13"><abbr title="Sparefoot\PitaService\Controller\FeedsController::citysearchAction">citysearchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#54"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_prepHours">_prepHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_facilityDetailsUrl">_facilityDetailsUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#94"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_prepUrl">_prepUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#99"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_urlTitle">_urlTitle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FiltersController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\FiltersController::loadAction">loadAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HeatmapController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\HeatmapController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HeatmapController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\HeatmapController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#48"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#56"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::redemptionRequestsAction">redemptionRequestsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#90"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::updateStatusAction">updateStatusAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#103"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::parseIncentiveResponse">parseIncentiveResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#118"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getRedemptionRequests">getRedemptionRequests</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#131"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getRedemptionRequest">getRedemptionRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getOffer">getOffer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#152"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getIncentive">getIncentive</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#162"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::setStatus">setStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::addIncentiveAndBookingDataToView">addIncentiveAndBookingDataToView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#202"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::searchRequestsAction">searchRequestsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#237"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getRedemptionRequestsAction">getRedemptionRequestsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\IndexController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\IndexController::_getQuickRepContainer">_getQuickRepContainer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#31"><abbr title="Sparefoot\PitaService\Controller\IndexController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\IndexController::oldIndexAction">oldIndexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#66"><abbr title="Sparefoot\PitaService\Controller\IndexController::fatalerrorAction">fatalerrorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#76"><abbr title="Sparefoot\PitaService\Controller\IndexController::exceptionAction">exceptionAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#93"><abbr title="Sparefoot\PitaService\Controller\IndexController::addBookmarkAction">addBookmarkAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#130"><abbr title="Sparefoot\PitaService\Controller\IndexController::updateBookmarksAction">updateBookmarksAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#167"><abbr title="Sparefoot\PitaService\Controller\IndexController::_loadTimespanBookingData">_loadTimespanBookingData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#231"><abbr title="Sparefoot\PitaService\Controller\IndexController::auditAction">auditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexView/IndexActionView.php.html#12"><abbr title="Sparefoot\PitaService\Controller\IndexView\IndexActionView::usortReports">usortReports</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\InventoryController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#50"><abbr title="Sparefoot\PitaService\Controller\InventoryController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#86"><abbr title="Sparefoot\PitaService\Controller\InventoryController::mainAction">mainAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#98"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadpercentcompleteAction">loadpercentcompleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#129"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadfacilitiesAction">loadfacilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#277"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_getFacilitiesInDeletedAccounts">_getFacilitiesInDeletedAccounts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#320"><abbr title="Sparefoot\PitaService\Controller\InventoryController::accountlistAction">accountlistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#354"><abbr title="Sparefoot\PitaService\Controller\InventoryController::productlistAction">productlistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#407"><abbr title="Sparefoot\PitaService\Controller\InventoryController::toggleproductAction">toggleproductAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#480"><abbr title="Sparefoot\PitaService\Controller\InventoryController::togglehostedwebsitetypeAction">togglehostedwebsitetypeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#512"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateadnetworkpriceAction">updateadnetworkpriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#566"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatehostedwebsitepriceAction">updatehostedwebsitepriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#620"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatetenantconnectprecalldigitsAction">updatetenantconnectprecalldigitsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#646"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadinfoAction">loadinfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#775"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadresaleratesAction">loadresaleratesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#816"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateresalebucketAction">updateresalebucketAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#839"><abbr title="Sparefoot\PitaService\Controller\InventoryController::deleteresalebucketAction">deleteresalebucketAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#862"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addupdateresalebucketAction">addupdateresalebucketAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#894"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unithtmlAction">unithtmlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#934"><abbr title="Sparefoot\PitaService\Controller\InventoryController::approveAction">approveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#983"><abbr title="Sparefoot\PitaService\Controller\InventoryController::facilityapproveAction">facilityapproveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1014"><abbr title="Sparefoot\PitaService\Controller\InventoryController::jsondetailsAction">jsondetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1371"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatecustomclosureAction">updatecustomclosureAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1398"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removecustomclosureAction">removecustomclosureAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1418"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatedetailsAction">updatedetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1860"><abbr title="Sparefoot\PitaService\Controller\InventoryController::multiplefacilityupdateAction">multiplefacilityupdateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2051"><abbr title="Sparefoot\PitaService\Controller\InventoryController::billableentitylistAction">billableentitylistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2071"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_makeBillableEntityList">_makeBillableEntityList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2088"><abbr title="Sparefoot\PitaService\Controller\InventoryController::streetviewAction">streetviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2123"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatestreetviewAction">updatestreetviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2161"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadlogAction">loadlogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2185"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unitAction">unitAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2475"><abbr title="Sparefoot\PitaService\Controller\InventoryController::photoframeAction">photoframeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2536"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getyieldavgsAction">getyieldavgsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2555"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadyieldjsonAction">loadyieldjsonAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2575"><abbr title="Sparefoot\PitaService\Controller\InventoryController::saveyieldAction">saveyieldAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2622"><abbr title="Sparefoot\PitaService\Controller\InventoryController::visibilityAction">visibilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2639"><abbr title="Sparefoot\PitaService\Controller\InventoryController::amenitiescheckAction">amenitiescheckAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2728"><abbr title="Sparefoot\PitaService\Controller\InventoryController::visibilitycheckAction">visibilitycheckAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2791"><abbr title="Sparefoot\PitaService\Controller\InventoryController::acctfaclistAction">acctfaclistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2881"><abbr title="Sparefoot\PitaService\Controller\InventoryController::syncAction">syncAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2907"><abbr title="Sparefoot\PitaService\Controller\InventoryController::phidosyncAction">phidosyncAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2929"><abbr title="Sparefoot\PitaService\Controller\InventoryController::refreshsearchAction">refreshsearchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2935"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_refreshSearch">_refreshSearch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2991"><abbr title="Sparefoot\PitaService\Controller\InventoryController::resetlocationAction">resetlocationAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3017"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unitexportAction">unitexportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3054"><abbr title="Sparefoot\PitaService\Controller\InventoryController::logexportAction">logexportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3084"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_fetchLogData">_fetchLogData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3114"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_fetchInventoryData">_fetchInventoryData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3151"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatefacilityAction">updatefacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3189"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addfacilitycontactAction">addfacilitycontactAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3232"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removefacilitycontactAction">removefacilitycontactAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3264"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateinventoryAction">updateinventoryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3338"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getaccessAction">getaccessAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3421"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addaccessAction">addaccessAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3449"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removeaccessAction">removeaccessAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3475"><abbr title="Sparefoot\PitaService\Controller\InventoryController::movefacilityAction">movefacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3515"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getsitelinkfacilitylistAction">getsitelinkfacilitylistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3547"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getselfstoragemanagerfacilitylistAction">getselfstoragemanagerfacilitylistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3579"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getcentershiftfacilitylistAction">getcentershiftfacilitylistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3612"><abbr title="Sparefoot\PitaService\Controller\InventoryController::syncselectedfacsAction">syncselectedfacsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3676"><abbr title="Sparefoot\PitaService\Controller\InventoryController::copyFacilityDataAction">copyFacilityDataAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3905"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadDuplicateReviews">loadDuplicateReviews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3917"><abbr title="Sparefoot\PitaService\Controller\InventoryController::copyReviewToFacility">copyReviewToFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3957"><abbr title="Sparefoot\PitaService\Controller\InventoryController::integrationsByAccountAction">integrationsByAccountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3988"><abbr title="Sparefoot\PitaService\Controller\InventoryController::facilitiesByIntegrationAction">facilitiesByIntegrationAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#4018"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getfacilityAction">getfacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#4049"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essfacilitylookupAction">essfacilitylookupAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#4071"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essconvertfrommanualAction">essconvertfrommanualAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#4121"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essconverttomanualAction">essconverttomanualAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\JobsController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#32"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandAction">commandAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#50"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandAddAction">commandAddAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandLogAction">commandLogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#102"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandEditAction">commandEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#127"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandSaveAction">commandSaveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#154"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandDeleteAction">commandDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledAction">scheduledAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#238"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledEditAction">scheduledEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#266"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledAddAction">scheduledAddAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#297"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledDeleteAction">scheduledDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#317"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledSaveAction">scheduledSaveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#347"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocChooseAction">adhocChooseAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#364"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocLogAction">adhocLogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#390"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocAddAction">adhocAddAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#418"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocDeleteAction">adhocDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#438"><abbr title="Sparefoot\PitaService\Controller\JobsController::summaryAction">summaryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#513"><abbr title="Sparefoot\PitaService\Controller\JobsController::unlockAction">unlockAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#533"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerZombieAction">workerZombieAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#586"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerLogAction">workerLogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#612"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerInfoAction">workerInfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#651"><abbr title="Sparefoot\PitaService\Controller\JobsController::childAction">childAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#679"><abbr title="Sparefoot\PitaService\Controller\JobsController::childEditAction">childEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#703"><abbr title="Sparefoot\PitaService\Controller\JobsController::childAddAction">childAddAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#724"><abbr title="Sparefoot\PitaService\Controller\JobsController::childUpdateAction">childUpdateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#753"><abbr title="Sparefoot\PitaService\Controller\JobsController::childSaveAction">childSaveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#778"><abbr title="Sparefoot\PitaService\Controller\JobsController::childDeleteAction">childDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#794"><abbr title="Sparefoot\PitaService\Controller\JobsController::colDisplay">colDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#799"><abbr title="Sparefoot\PitaService\Controller\JobsController::finishCode">finishCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#813"><abbr title="Sparefoot\PitaService\Controller\JobsController::successCode">successCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#828"><abbr title="Sparefoot\PitaService\Controller\JobsController::timeDisplay">timeDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#859"><abbr title="Sparefoot\PitaService\Controller\JobsController::dateDisplay">dateDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#866"><abbr title="Sparefoot\PitaService\Controller\JobsController::_verifyChildAllowed">_verifyChildAllowed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\LoginController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\LoginController::check">check</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#66"><abbr title="Sparefoot\PitaService\Controller\LoginController::getCurrentUser">getCurrentUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#84"><abbr title="Sparefoot\PitaService\Controller\LoginController::logout">logout</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#91"><abbr title="Sparefoot\PitaService\Controller\LoginController::amILoggedIn">amILoggedIn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#97"><abbr title="Sparefoot\PitaService\Controller\LoginController::isUserGod">isUserGod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\MaintenanceModeController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#61"><abbr title="Sparefoot\PitaService\Controller\MaintenanceModeController::setFlagAction">setFlagAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#94"><abbr title="Sparefoot\PitaService\Controller\MaintenanceModeController::_sendMaintenanceModeEmail">_sendMaintenanceModeEmail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NetsuiteController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\NetsuiteController::accountAction">accountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NetsuiteController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\NetsuiteController::statusAction">statusAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#11"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\OmnomController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#51"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByJob">_queryStatusByJob</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#98"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByIntegration">_queryStatusByIntegration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#157"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByFacility">_queryStatusByFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#210"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getJobs">_getJobs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#230"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getIntegrations">_getIntegrations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#250"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getAccounts">_getAccounts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#12"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#32"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::paidmediaAction">paidmediaAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#48"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::managepaidmediaAction">managepaidmediaAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#62"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::modifypaidmediaAction">modifypaidmediaAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#95"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::ajaxcreatepaidmediaupdateAction">ajaxcreatepaidmediaupdateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#110"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::getRuleengineList">getRuleengineList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#135"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::getCampaignList">getCampaignList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#14"><abbr title="Sparefoot\PitaService\Controller\PayoutController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\PayoutController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#34"><abbr title="Sparefoot\PitaService\Controller\PayoutController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\PayoutController::previewAction">previewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#65"><abbr title="Sparefoot\PitaService\Controller\PayoutController::detailsAction">detailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#83"><abbr title="Sparefoot\PitaService\Controller\PayoutController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#105"><abbr title="Sparefoot\PitaService\Controller\PayoutController::pdfAction">pdfAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#123"><abbr title="Sparefoot\PitaService\Controller\PayoutController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#153"><abbr title="Sparefoot\PitaService\Controller\PayoutController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#181"><abbr title="Sparefoot\PitaService\Controller\PayoutController::_assignBookings">_assignBookings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\PayoutController::_preparePayoutQuery">_preparePayoutQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#211"><abbr title="Sparefoot\PitaService\Controller\PayoutController::_prepareDetails">_prepareDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PhotosController.php.html#12"><abbr title="Sparefoot\PitaService\Controller\PhotosController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PhotosController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\PhotosController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PhotosController.php.html#67"><abbr title="Sparefoot\PitaService\Controller\PhotosController::approveAction">approveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PingController.php.html#14"><abbr title="Sparefoot\PitaService\Controller\PingController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PingController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\PingController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PitaSearchController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PitaSearchController.php.html#31"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PitaSearchController.php.html#83"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::queryAction">queryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\ProxyController::loadAction">loadAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#68"><abbr title="Sparefoot\PitaService\Controller\ProxyController::AuthenticateProxyAuthorization">AuthenticateProxyAuthorization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#89"><abbr title="Sparefoot\PitaService\Controller\ProxyController::proxyRequest">proxyRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#132"><abbr title="Sparefoot\PitaService\Controller\ProxyController::infoAction">infoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#148"><abbr title="Sparefoot\PitaService\Controller\ProxyController::debugAction">debugAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\PublicController::searchServicesAction">searchServicesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\PublicController::bookUnitAction">bookUnitAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#122"><abbr title="Sparefoot\PitaService\Controller\PublicController::searchAction">searchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#147"><abbr title="Sparefoot\PitaService\Controller\PublicController::hourlyAlertsAction">hourlyAlertsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#13"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#33"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#48"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#83"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::paramsAction">paramsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#98"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::facilityAction">facilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#114"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#140"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::_facSort">_facSort</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#149"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::unitsAction">unitsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#176"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::unitAction">unitAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#191"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::executeAction">executeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickClientController.php.html#250"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#10"><abbr title="Sparefoot\PitaService\Controller\QuickformController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\QuickformController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#30"><abbr title="Sparefoot\PitaService\Controller\QuickformController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\QuickformController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickjobController.php.html#12"><abbr title="Sparefoot\PitaService\Controller\QuickjobController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickjobController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\QuickjobController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickjobController.php.html#32"><abbr title="Sparefoot\PitaService\Controller\QuickjobController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#59"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::renderdashAction">renderdashAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#71"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#83"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::updateinputsAction">updateinputsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#108"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::tableAction">tableAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#135"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::chartAction">chartAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#165"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::sqlAction">sqlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#185"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::exportAction">exportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#206"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::hourlyAlertsAction">hourlyAlertsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#34"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::wsdlAction">wsdlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#50"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::handleSoapRequest">handleSoapRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#77"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::generateWsdlResponse">generateWsdlResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#56"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#69"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::integrationAction">integrationAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#91"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::saveorderAction">saveorderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#112"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::savenewAction">savenewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#145"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::saveexistingAction">saveexistingAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#191"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::removeAction">removeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#11"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#41"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::toggleconvertedAction">toggleconvertedAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#60"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::toggleclaimedAction">toggleclaimedAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#79"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::savenotesAction">savenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\ReportsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#30"><abbr title="Sparefoot\PitaService\Controller\ReportsController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\ReportsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#63"><abbr title="Sparefoot\PitaService\Controller\ReportsController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#113"><abbr title="Sparefoot\PitaService\Controller\ReportsController::updateInputsAction">updateInputsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#138"><abbr title="Sparefoot\PitaService\Controller\ReportsController::tableAction">tableAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#166"><abbr title="Sparefoot\PitaService\Controller\ReportsController::sqlAction">sqlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#186"><abbr title="Sparefoot\PitaService\Controller\ReportsController::exportAction">exportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#62"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::getqueryAction">getqueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#80"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::deletereviewAction">deletereviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::getreviewAction">getreviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#134"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::savereviewAction">savereviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::approvalsAction">approvalsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#193"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::setStatusAction">setStatusAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#224"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::editReviewMessageAction">editReviewMessageAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\RewardsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\RewardsController::newkiindAction">newkiindAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\RewardsController::reviewkiindAction">reviewkiindAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#84"><abbr title="Sparefoot\PitaService\Controller\RewardsController::browsekiindAction">browsekiindAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#101"><abbr title="Sparefoot\PitaService\Controller\RewardsController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\SalesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#28"><abbr title="Sparefoot\PitaService\Controller\SalesController::searchAction">searchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#44"><abbr title="Sparefoot\PitaService\Controller\SalesController::_queryData">_queryData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\SalesController::_getSearchDataByZip">_getSearchDataByZip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#120"><abbr title="Sparefoot\PitaService\Controller\SalesController::_getSearchData">_getSearchData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#138"><abbr title="Sparefoot\PitaService\Controller\SalesController::_getClickData">_getClickData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#160"><abbr title="Sparefoot\PitaService\Controller\SalesController::_getReservationData">_getReservationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesforceController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\SalesforceController::addAccountAction">addAccountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesforceController.php.html#196"><abbr title="Sparefoot\PitaService\Controller\SalesforceController::_createManualFacilities">_createManualFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\SearchController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\SearchController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\SearchController::testAction">testAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#143"><abbr title="Sparefoot\PitaService\Controller\SearchController::distanceAction">distanceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#204"><abbr title="Sparefoot\PitaService\Controller\SearchController::_initWeights">_initWeights</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#224"><abbr title="Sparefoot\PitaService\Controller\SearchController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#55"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::getAction">getAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#87"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::saveAction">saveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#115"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::convexhullAction">convexhullAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::simplifyAction">simplifyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#167"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::getServiceAreaWktByFacilityId">getServiceAreaWktByFacilityId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#177"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::postLocationService">postLocationService</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\SoftwarepartnerController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\SoftwarepartnerController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::testAction">testAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#62"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::_initWeights">_initWeights</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#82"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#127"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::distanceAction">distanceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\StatementsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\StatementsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#52"><abbr title="Sparefoot\PitaService\Controller\StatementsController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\StatementsController::generateAction">generateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#124"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_export">_export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#157"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_exportInvoice">_exportInvoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#288"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_processReport">_processReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#308"><abbr title="Sparefoot\PitaService\Controller\StatementsController::cancelInvoiceAction">cancelInvoiceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#332"><abbr title="Sparefoot\PitaService\Controller\StatementsController::invoiceAction">invoiceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#397"><abbr title="Sparefoot\PitaService\Controller\StatementsController::pdfAction">pdfAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#420"><abbr title="Sparefoot\PitaService\Controller\StatementsController::emailpreviewAction">emailpreviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#465"><abbr title="Sparefoot\PitaService\Controller\StatementsController::getinvoiceAction">getinvoiceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\TableauController::setErrorMessage">setErrorMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\TableauController::setSuccessMessage">setSuccessMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\TableauController::initView">initView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#59"><abbr title="Sparefoot\PitaService\Controller\TableauController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\TableauController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#105"><abbr title="Sparefoot\PitaService\Controller\TableauController::addAction">addAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#118"><abbr title="Sparefoot\PitaService\Controller\TableauController::editAction">editAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#140"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveAction">saveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\TableauController::deleteAction">deleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#198"><abbr title="Sparefoot\PitaService\Controller\TableauController::manageKeysAction">manageKeysAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#211"><abbr title="Sparefoot\PitaService\Controller\TableauController::editKeyAction">editKeyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#233"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveKeyAction">saveKeyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#270"><abbr title="Sparefoot\PitaService\Controller\TableauController::deleteKeyAction">deleteKeyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#297"><abbr title="Sparefoot\PitaService\Controller\TableauController::massEditKeyAction">massEditKeyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#322"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveMassEditAction">saveMassEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#371"><abbr title="Sparefoot\PitaService\Controller\TableauController::manageTeamsAction">manageTeamsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#384"><abbr title="Sparefoot\PitaService\Controller\TableauController::addTeamAction">addTeamAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\TestController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#28"><abbr title="Sparefoot\PitaService\Controller\TestController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#38"><abbr title="Sparefoot\PitaService\Controller\TestController::testsAction">testsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#63"><abbr title="Sparefoot\PitaService\Controller\TestController::manageAction">manageAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#91"><abbr title="Sparefoot\PitaService\Controller\TestController::manageuiAction">manageuiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\TestController::stopAction">stopAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#131"><abbr title="Sparefoot\PitaService\Controller\TestController::stopuiAction">stopuiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#145"><abbr title="Sparefoot\PitaService\Controller\TestController::modifytestAction">modifytestAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\TestController::ajaxcreatesearchAction">ajaxcreatesearchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#206"><abbr title="Sparefoot\PitaService\Controller\TestController::ajaxcreateuiAction">ajaxcreateuiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#229"><abbr title="Sparefoot\PitaService\Controller\TestController::_saveWeight">_saveWeight</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#243"><abbr title="Sparefoot\PitaService\Controller\TestController::_saveTestVariation">_saveTestVariation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\ToolsController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\ToolsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#48"><abbr title="Sparefoot\PitaService\Controller\ToolsController::urlBuilderAction">urlBuilderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#60"><abbr title="Sparefoot\PitaService\Controller\ToolsController::perfectPhotosSelectorAction">perfectPhotosSelectorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#81"><abbr title="Sparefoot\PitaService\Controller\ToolsController::perfectPhotosSelectorPostAction">perfectPhotosSelectorPostAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#114"><abbr title="Sparefoot\PitaService\Controller\ToolsController::noEmailListAction">noEmailListAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#214"><abbr title="Sparefoot\PitaService\Controller\ToolsController::searchKeyChangerAction">searchKeyChangerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#341"><abbr title="Sparefoot\PitaService\Controller\ToolsController::twilioReportsAction">twilioReportsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#450"><abbr title="Sparefoot\PitaService\Controller\ToolsController::createTwilioNumberAction">createTwilioNumberAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#520"><abbr title="Sparefoot\PitaService\Controller\ToolsController::purchaseTwilioNumbersAction">purchaseTwilioNumbersAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#588"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentEditorAction">siteContentEditorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#681"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentCreatorAction">siteContentCreatorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#812"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentImportAction">siteContentImportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#901"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentCreatorImportAction">siteContentCreatorImportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1033"><abbr title="Sparefoot\PitaService\Controller\ToolsController::accountingMapAction">accountingMapAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1138"><abbr title="Sparefoot\PitaService\Controller\ToolsController::cabinetAction">cabinetAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1166"><abbr title="Sparefoot\PitaService\Controller\ToolsController::featureFlagsAction">featureFlagsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1208"><abbr title="Sparefoot\PitaService\Controller\ToolsController::handleFeatureFlagSave">handleFeatureFlagSave</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1254"><abbr title="Sparefoot\PitaService\Controller\ToolsController::addFeatureFlagAction">addFeatureFlagAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1272"><abbr title="Sparefoot\PitaService\Controller\ToolsController::deleteFeatureFlagAction">deleteFeatureFlagAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1290"><abbr title="Sparefoot\PitaService\Controller\ToolsController::pressPageAction">pressPageAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1317"><abbr title="Sparefoot\PitaService\Controller\ToolsController::pressPageEditorAction">pressPageEditorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1346"><abbr title="Sparefoot\PitaService\Controller\ToolsController::platformSearchAction">platformSearchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1356"><abbr title="Sparefoot\PitaService\Controller\ToolsController::platformQueryAction">platformQueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TriController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\TriController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TriController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\TriController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TriController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\TriController::fortuneAction">fortuneAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\UserController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\UserController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#65"><abbr title="Sparefoot\PitaService\Controller\UserController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#73"><abbr title="Sparefoot\PitaService\Controller\UserController::_listAction">_listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\UserController::myfootAction">myfootAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#126"><abbr title="Sparefoot\PitaService\Controller\UserController::pitaAction">pitaAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#133"><abbr title="Sparefoot\PitaService\Controller\UserController::portalAction">portalAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#141"><abbr title="Sparefoot\PitaService\Controller\UserController::loadusersAction">loadusersAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\UserController::editinfoAction">editinfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#248"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxcreateAction">ajaxcreateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#287"><abbr title="Sparefoot\PitaService\Controller\UserController::_createPitaUser">_createPitaUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#326"><abbr title="Sparefoot\PitaService\Controller\UserController::_createPortalUser">_createPortalUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#355"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxcreatemyfootAction">ajaxcreatemyfootAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#448"><abbr title="Sparefoot\PitaService\Controller\UserController::isEmailValid">isEmailValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#458"><abbr title="Sparefoot\PitaService\Controller\UserController::_restrictFacilityAccess">_restrictFacilityAccess</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#468"><abbr title="Sparefoot\PitaService\Controller\UserController::_addFacilityContact">_addFacilityContact</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#485"><abbr title="Sparefoot\PitaService\Controller\UserController::_buildUser">_buildUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#514"><abbr title="Sparefoot\PitaService\Controller\UserController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#582"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxpasswordAction">ajaxpasswordAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#612"><abbr title="Sparefoot\PitaService\Controller\UserController::passwordAction">passwordAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#644"><abbr title="Sparefoot\PitaService\Controller\UserController::changeattributesAction">changeattributesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#714"><abbr title="Sparefoot\PitaService\Controller\UserController::addstmtrecipientsAction">addstmtrecipientsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#748"><abbr title="Sparefoot\PitaService\Controller\UserController::removestmtrecipientsAction">removestmtrecipientsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#782"><abbr title="Sparefoot\PitaService\Controller\UserController::_export">_export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#858"><abbr title="Sparefoot\PitaService\Controller\UserController::_emailRecipientExport">_emailRecipientExport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#926"><abbr title="Sparefoot\PitaService\Controller\UserController::_getDefaultQuery">_getDefaultQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#933"><abbr title="Sparefoot\PitaService\Controller\UserController::_processQuery">_processQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#953"><abbr title="Sparefoot\PitaService\Controller\UserController::_processReport">_processReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#966"><abbr title="Sparefoot\PitaService\Controller\UserController::_processEmailRecipientReport">_processEmailRecipientReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1076"><abbr title="Sparefoot\PitaService\Controller\UserController::_addAcctMgmtUser">_addAcctMgmtUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1093"><abbr title="Sparefoot\PitaService\Controller\UserController::_acctMgmtRecipients">_acctMgmtRecipients</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1142"><abbr title="Sparefoot\PitaService\Controller\UserController::_facilityContactRecipients">_facilityContactRecipients</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1195"><abbr title="Sparefoot\PitaService\Controller\UserController::_getActiveAcctIds">_getActiveAcctIds</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1223"><abbr title="Sparefoot\PitaService\Controller\UserController::_getActiveListingAvailIds">_getActiveListingAvailIds</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1251"><abbr title="Sparefoot\PitaService\Controller\UserController::createusersAction">createusersAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1368"><abbr title="Sparefoot\PitaService\Controller\UserController::tokenAction">tokenAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#69"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::updateInputsAction">updateInputsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#103"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::citygridAction">citygridAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#148"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::citygridfacilityAction">citygridfacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::superpagesAction">superpagesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#233"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::superpagesfacilityAction">superpagesfacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#263"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::allowiypAction">allowiypAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#277"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::accountonAction">accountonAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#313"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::accountoffAction">accountoffAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#359"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::localdotcomAction">localdotcomAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#365"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::_initTrueDateRange">_initTrueDateRange</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#385"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::getTrueBeginDate">getTrueBeginDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#390"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::getTrueEndDate">getTrueEndDate</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="InventoryController.php.html#1014"><abbr title="Sparefoot\PitaService\Controller\InventoryController::jsondetailsAction">jsondetailsAction</abbr></a></td><td class="text-right">9312</td></tr>
       <tr><td><a href="InventoryController.php.html#1418"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatedetailsAction">updatedetailsAction</abbr></a></td><td class="text-right">5852</td></tr>
       <tr><td><a href="InventoryController.php.html#2185"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unitAction">unitAction</abbr></a></td><td class="text-right">2970</td></tr>
       <tr><td><a href="AnalyticsController.php.html#156"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_export">_export</abbr></a></td><td class="text-right">2550</td></tr>
       <tr><td><a href="FiltersController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\FiltersController::loadAction">loadAction</abbr></a></td><td class="text-right">2450</td></tr>
       <tr><td><a href="AccountController.php.html#1527"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeintegrationcredsAction">changeintegrationcredsAction</abbr></a></td><td class="text-right">2256</td></tr>
       <tr><td><a href="AccountController.php.html#986"><abbr title="Sparefoot\PitaService\Controller\AccountController::newacctAction">newacctAction</abbr></a></td><td class="text-right">1892</td></tr>
       <tr><td><a href="InventoryController.php.html#3676"><abbr title="Sparefoot\PitaService\Controller\InventoryController::copyFacilityDataAction">copyFacilityDataAction</abbr></a></td><td class="text-right">1560</td></tr>
       <tr><td><a href="InventoryController.php.html#1860"><abbr title="Sparefoot\PitaService\Controller\InventoryController::multiplefacilityupdateAction">multiplefacilityupdateAction</abbr></a></td><td class="text-right">1406</td></tr>
       <tr><td><a href="AccountController.php.html#1286"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeIntegration">_makeIntegration</abbr></a></td><td class="text-right">992</td></tr>
       <tr><td><a href="SalesforceController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\SalesforceController::addAccountAction">addAccountAction</abbr></a></td><td class="text-right">992</td></tr>
       <tr><td><a href="ApiController.php.html#153"><abbr title="Sparefoot\PitaService\Controller\ApiController::exportunitapiAction">exportunitapiAction</abbr></a></td><td class="text-right">812</td></tr>
       <tr><td><a href="InventoryController.php.html#129"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadfacilitiesAction">loadfacilitiesAction</abbr></a></td><td class="text-right">812</td></tr>
       <tr><td><a href="ApiController.php.html#36"><abbr title="Sparefoot\PitaService\Controller\ApiController::unitsAction">unitsAction</abbr></a></td><td class="text-right">600</td></tr>
       <tr><td><a href="FacilityController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_init">_init</abbr></a></td><td class="text-right">600</td></tr>
       <tr><td><a href="UserController.php.html#966"><abbr title="Sparefoot\PitaService\Controller\UserController::_processEmailRecipientReport">_processEmailRecipientReport</abbr></a></td><td class="text-right">600</td></tr>
       <tr><td><a href="FacilityController.php.html#408"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_loadFacilities">_loadFacilities</abbr></a></td><td class="text-right">552</td></tr>
       <tr><td><a href="InventoryController.php.html#2639"><abbr title="Sparefoot\PitaService\Controller\InventoryController::amenitiescheckAction">amenitiescheckAction</abbr></a></td><td class="text-right">462</td></tr>
       <tr><td><a href="BookingsController.php.html#519"><abbr title="Sparefoot\PitaService\Controller\BookingsController::_changeBookingState">_changeBookingState</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="AccountController.php.html#31"><abbr title="Sparefoot\PitaService\Controller\AccountController::listAction">listAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="AccountController.php.html#2268"><abbr title="Sparefoot\PitaService\Controller\AccountController::updatenscustomerAction">updatenscustomerAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="BookingsController.php.html#609"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="FacilitiesController.php.html#33"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::indexAction">indexAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="InventoryController.php.html#646"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadinfoAction">loadinfoAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="UserController.php.html#1251"><abbr title="Sparefoot\PitaService\Controller\UserController::createusersAction">createusersAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="AnalyticsController.php.html#328"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_processQuery">_processQuery</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="ToolsController.php.html#214"><abbr title="Sparefoot\PitaService\Controller\ToolsController::searchKeyChangerAction">searchKeyChangerAction</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="ToolsController.php.html#1356"><abbr title="Sparefoot\PitaService\Controller\ToolsController::platformQueryAction">platformQueryAction</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="UserController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\UserController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="BookingsController.php.html#195"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getbookinginfoAction">getbookinginfoAction</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="BookingsController.php.html#444"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changebookingsAction">changebookingsAction</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="FacilityController.php.html#573"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_tableFromReport">_tableFromReport</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="InventoryController.php.html#3338"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getaccessAction">getaccessAction</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ToolsController.php.html#114"><abbr title="Sparefoot\PitaService\Controller\ToolsController::noEmailListAction">noEmailListAction</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="InventoryController.php.html#407"><abbr title="Sparefoot\PitaService\Controller\InventoryController::toggleproductAction">toggleproductAction</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="InventoryController.php.html#2791"><abbr title="Sparefoot\PitaService\Controller\InventoryController::acctfaclistAction">acctfaclistAction</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="UserController.php.html#355"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxcreatemyfootAction">ajaxcreatemyfootAction</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="InventoryController.php.html#3264"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateinventoryAction">updateinventoryAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="SearchController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\SearchController::testAction">testAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="StatementsController.php.html#157"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_exportInvoice">_exportInvoice</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ToolsController.php.html#901"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentCreatorImportAction">siteContentCreatorImportAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ToolsController.php.html#1033"><abbr title="Sparefoot\PitaService\Controller\ToolsController::accountingMapAction">accountingMapAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="UserController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\UserController::editinfoAction">editinfoAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="AccountController.php.html#657"><abbr title="Sparefoot\PitaService\Controller\AccountController::verifyAction">verifyAction</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="FacilityController.php.html#79"><abbr title="Sparefoot\PitaService\Controller\FacilityController::indexAction">indexAction</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="InventoryController.php.html#354"><abbr title="Sparefoot\PitaService\Controller\InventoryController::productlistAction">productlistAction</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ToolsController.php.html#681"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentCreatorAction">siteContentCreatorAction</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="AccountController.php.html#747"><abbr title="Sparefoot\PitaService\Controller\AccountController::getintegrationdetailsAction">getintegrationdetailsAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="QuickClientController.php.html#191"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::executeAction">executeAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="TableauController.php.html#322"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveMassEditAction">saveMassEditAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ToolsController.php.html#341"><abbr title="Sparefoot\PitaService\Controller\ToolsController::twilioReportsAction">twilioReportsAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ToolsController.php.html#450"><abbr title="Sparefoot\PitaService\Controller\ToolsController::createTwilioNumberAction">createTwilioNumberAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="UserController.php.html#1142"><abbr title="Sparefoot\PitaService\Controller\UserController::_facilityContactRecipients">_facilityContactRecipients</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="AccountController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\AccountController::moveintegrationAction">moveintegrationAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AccountController.php.html#303"><abbr title="Sparefoot\PitaService\Controller\AccountController::syncAction">syncAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AccountController.php.html#2051"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentitiesAction">getbillableentitiesAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AccountController.php.html#2172"><abbr title="Sparefoot\PitaService\Controller\AccountController::updateqbcustomerAction">updateqbcustomerAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AccountController.php.html#2411"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentityfacilitiesAction">getbillableentityfacilitiesAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ApiController.php.html#419"><abbr title="Sparefoot\PitaService\Controller\ApiController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="InventoryController.php.html#2475"><abbr title="Sparefoot\PitaService\Controller\InventoryController::photoframeAction">photoframeAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ToolsController.php.html#1208"><abbr title="Sparefoot\PitaService\Controller\ToolsController::handleFeatureFlagSave">handleFeatureFlagSave</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="UserController.php.html#644"><abbr title="Sparefoot\PitaService\Controller\UserController::changeattributesAction">changeattributesAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="UserController.php.html#858"><abbr title="Sparefoot\PitaService\Controller\UserController::_emailRecipientExport">_emailRecipientExport</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="UtilitiesController.php.html#69"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::renderAction">renderAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#195"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processReport">processReport</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="AccountController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\AccountController::_export">_export</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="AccountController.php.html#1813"><abbr title="Sparefoot\PitaService\Controller\AccountController::getintegrationcredsAction">getintegrationcredsAction</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#115"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::testAction">testAction</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="DisputesController.php.html#161"><abbr title="Sparefoot\PitaService\Controller\DisputesController::applyRuling">applyRuling</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ToolsController.php.html#588"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentEditorAction">siteContentEditorAction</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="UserController.php.html#141"><abbr title="Sparefoot\PitaService\Controller\UserController::loadusersAction">loadusersAction</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#140"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processQueryParams">processQueryParams</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="BuyoutController.php.html#373"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_updateExistingTenants">_updateExistingTenants</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="FacilityController.php.html#339"><abbr title="Sparefoot\PitaService\Controller\FacilityController::jsonunitdetailsAction">jsonunitdetailsAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="IncentivesController.php.html#202"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::searchRequestsAction">searchRequestsAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="InventoryController.php.html#934"><abbr title="Sparefoot\PitaService\Controller\InventoryController::approveAction">approveAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="InventoryController.php.html#2935"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_refreshSearch">_refreshSearch</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="JobsController.php.html#828"><abbr title="Sparefoot\PitaService\Controller\JobsController::timeDisplay">timeDisplay</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PitaSearchController.php.html#31"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::indexAction">indexAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PitaSearchController.php.html#83"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::queryAction">queryAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PublicController.php.html#147"><abbr title="Sparefoot\PitaService\Controller\PublicController::hourlyAlertsAction">hourlyAlertsAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SalesforceController.php.html#196"><abbr title="Sparefoot\PitaService\Controller\SalesforceController::_createManualFacilities">_createManualFacilities</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ToolsController.php.html#1166"><abbr title="Sparefoot\PitaService\Controller\ToolsController::featureFlagsAction">featureFlagsAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#73"><abbr title="Sparefoot\PitaService\Controller\UserController::_listAction">_listAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#782"><abbr title="Sparefoot\PitaService\Controller\UserController::_export">_export</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#933"><abbr title="Sparefoot\PitaService\Controller\UserController::_processQuery">_processQuery</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#1093"><abbr title="Sparefoot\PitaService\Controller\UserController::_acctMgmtRecipients">_acctMgmtRecipients</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#278"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::populateFilter">populateFilter</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="AccountController.php.html#1902"><abbr title="Sparefoot\PitaService\Controller\AccountController::geteditintegrationdetailsAction">geteditintegrationdetailsAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="AccountController.php.html#1949"><abbr title="Sparefoot\PitaService\Controller\AccountController::saveeditintegrationdetailsAction">saveeditintegrationdetailsAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ApiController.php.html#819"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullEasyStorageSolutionsUnits">_pullEasyStorageSolutionsUnits</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ApiController.php.html#854"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullExtraspaceUnits">_pullExtraspaceUnits</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="BuyoutController.php.html#334"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_updatePendingBookings">_updatePendingBookings</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="DisputesController.php.html#226"><abbr title="Sparefoot\PitaService\Controller\DisputesController::validateBookingIsReviewable">validateBookingIsReviewable</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="FacilityController.php.html#250"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_fetchInventoryData">_fetchInventoryData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="FacilityController.php.html#659"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_initFilter">_initFilter</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="InventoryController.php.html#512"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateadnetworkpriceAction">updateadnetworkpriceAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="InventoryController.php.html#566"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatehostedwebsitepriceAction">updatehostedwebsitepriceAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="InventoryController.php.html#3114"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_fetchInventoryData">_fetchInventoryData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="LoginController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\LoginController::index">index</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ProxyController.php.html#89"><abbr title="Sparefoot\PitaService\Controller\ProxyController::proxyRequest">proxyRequest</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="QuickrepController.php.html#206"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::hourlyAlertsAction">hourlyAlertsAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#145"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::saveexistingAction">saveexistingAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ReportsController.php.html#63"><abbr title="Sparefoot\PitaService\Controller\ReportsController::renderAction">renderAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="TableauController.php.html#140"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveAction">saveAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ToolsController.php.html#812"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentImportAction">siteContentImportAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UserController.php.html#612"><abbr title="Sparefoot\PitaService\Controller\UserController::passwordAction">passwordAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="YellowpagesController.php.html#277"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::accountonAction">accountonAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="YellowpagesController.php.html#313"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::accountoffAction">accountoffAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="AccountController.php.html#440"><abbr title="Sparefoot\PitaService\Controller\AccountController::changebidsAction">changebidsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AccountController.php.html#840"><abbr title="Sparefoot\PitaService\Controller\AccountController::getacctdetailsAction">getacctdetailsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AccountController.php.html#1488"><abbr title="Sparefoot\PitaService\Controller\AccountController::deleteaccountAction">deleteaccountAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AccountController.php.html#2124"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentitydetailsAction">getbillableentitydetailsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AnalyticsController.php.html#428"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_processReport">_processReport</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BookingsController.php.html#273"><abbr title="Sparefoot\PitaService\Controller\BookingsController::jsondetailsAction">jsondetailsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BookingsController.php.html#384"><abbr title="Sparefoot\PitaService\Controller\BookingsController::addduplicateAction">addduplicateAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BookingsController.php.html#909"><abbr title="Sparefoot\PitaService\Controller\BookingsController::resendemailsAction">resendemailsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BookingsController.php.html#959"><abbr title="Sparefoot\PitaService\Controller\BookingsController::sitelinkledgerAction">sitelinkledgerAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BuyoutController.php.html#68"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::createAction">createAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BuyoutController.php.html#230"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::downloadAction">downloadAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#56"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_getApiResponseTest">_getApiResponseTest</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DashboardController.php.html#120"><abbr title="Sparefoot\PitaService\Controller\DashboardController::bookingsAction">bookingsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DisputesController.php.html#65"><abbr title="Sparefoot\PitaService\Controller\DisputesController::opendisputesAction">opendisputesAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DisputesController.php.html#102"><abbr title="Sparefoot\PitaService\Controller\DisputesController::closeddisputesAction">closeddisputesAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="FacilitiesController.php.html#186"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::loadbyidAction">loadbyidAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#3151"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatefacilityAction">updatefacilityAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#3189"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addfacilitycontactAction">addfacilitycontactAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#3475"><abbr title="Sparefoot\PitaService\Controller\InventoryController::movefacilityAction">movefacilityAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#3612"><abbr title="Sparefoot\PitaService\Controller\InventoryController::syncselectedfacsAction">syncselectedfacsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#4071"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essconvertfrommanualAction">essconvertfrommanualAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="NetsuiteController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\NetsuiteController::accountAction">accountAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="PhotosController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\PhotosController::indexAction">indexAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ReviewsController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::indexAction">indexAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SearchController.php.html#143"><abbr title="Sparefoot\PitaService\Controller\SearchController::distanceAction">distanceAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\SoftwarepartnerController::indexAction">indexAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#127"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::distanceAction">distanceAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="StatementsController.php.html#332"><abbr title="Sparefoot\PitaService\Controller\StatementsController::invoiceAction">invoiceAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="StatementsController.php.html#420"><abbr title="Sparefoot\PitaService\Controller\StatementsController::emailpreviewAction">emailpreviewAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="StatementsController.php.html#465"><abbr title="Sparefoot\PitaService\Controller\StatementsController::getinvoiceAction">getinvoiceAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="TestController.php.html#145"><abbr title="Sparefoot\PitaService\Controller\TestController::modifytestAction">modifytestAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AffiliateController.php.html#266"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processSaveSite">_processSaveSite</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ApiController.php.html#539"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStorageMartFacilityData">_pullStorageMartFacilityData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BookingsController.php.html#869"><abbr title="Sparefoot\PitaService\Controller\BookingsController::previewemailsAction">previewemailsAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BuyoutController.php.html#118"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editAction">editAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DashboardController.php.html#191"><abbr title="Sparefoot\PitaService\Controller\DashboardController::initDailyBookingsProjection">initDailyBookingsProjection</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ErrorController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\ErrorController::errorAction">errorAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="FacilityController.php.html#781"><abbr title="Sparefoot\PitaService\Controller\FacilityController::exportdirectoryrecordingsAction">exportdirectoryrecordingsAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="IncentivesController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::addIncentiveAndBookingDataToView">addIncentiveAndBookingDataToView</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InventoryController.php.html#277"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_getFacilitiesInDeletedAccounts">_getFacilitiesInDeletedAccounts</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InventoryController.php.html#320"><abbr title="Sparefoot\PitaService\Controller\InventoryController::accountlistAction">accountlistAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InventoryController.php.html#2575"><abbr title="Sparefoot\PitaService\Controller\InventoryController::saveyieldAction">saveyieldAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InventoryController.php.html#4121"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essconverttomanualAction">essconverttomanualAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JobsController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledAction">scheduledAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JobsController.php.html#612"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerInfoAction">workerInfoAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OmnomController.php.html#98"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByIntegration">_queryStatusByIntegration</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OmnomController.php.html#157"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByFacility">_queryStatusByFacility</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PaidmediaController.php.html#62"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::modifypaidmediaAction">modifypaidmediaAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PaidmediaController.php.html#110"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::getRuleengineList">getRuleengineList</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ProxyController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\ProxyController::loadAction">loadAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ProxyController.php.html#68"><abbr title="Sparefoot\PitaService\Controller\ProxyController::AuthenticateProxyAuthorization">AuthenticateProxyAuthorization</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PublicController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\PublicController::bookUnitAction">bookUnitAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="QuickClientController.php.html#250"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ReviewsController.php.html#134"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::savereviewAction">savereviewAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="RewardsController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\RewardsController::newkiindAction">newkiindAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="TableauController.php.html#233"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveKeyAction">saveKeyAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ToolsController.php.html#520"><abbr title="Sparefoot\PitaService\Controller\ToolsController::purchaseTwilioNumbersAction">purchaseTwilioNumbersAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UserController.php.html#248"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxcreateAction">ajaxcreateAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UserController.php.html#582"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxpasswordAction">ajaxpasswordAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YellowpagesController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::indexAction">indexAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#60"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::export">export</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#96"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::_exportReport">_exportReport</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::initGeneralData">initGeneralData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AccountController.php.html#520"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeminimumbidsAction">changeminimumbidsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AccountController.php.html#584"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeprimarycontactAction">changeprimarycontactAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#37"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getAdminsHtml">getAdminsHtml</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AffiliateController.php.html#124"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::updateAction">updateAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AffiliateController.php.html#231"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::exclusionsAction">exclusionsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AnalyticsController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::indexAction">indexAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ApiController.php.html#693"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershift4UnitData">_pullCentershift4UnitData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ApiController.php.html#932"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDomicoUnitData">_pullDomicoUnitData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BillingController.php.html#130"><abbr title="Sparefoot\PitaService\Controller\BillingController::_runQuery">_runQuery</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BookingsController.php.html#30"><abbr title="Sparefoot\PitaService\Controller\BookingsController::indexAction">indexAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BookingsController.php.html#833"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getunitinfoAction">getunitinfoAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BuyoutController.php.html#274"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editUpdateAction">editUpdateAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="CdpController.php.html#184"><abbr title="Sparefoot\PitaService\Controller\CdpController::viewLogAction">viewLogAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="EmailController.php.html#92"><abbr title="Sparefoot\PitaService\Controller\EmailController::ajaxsendAction">ajaxsendAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FacilitiesController.php.html#148"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::getqueryAction">getqueryAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FacilityController.php.html#154"><abbr title="Sparefoot\PitaService\Controller\FacilityController::acquiretwilionumberAction">acquiretwilionumberAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FacilityController.php.html#213"><abbr title="Sparefoot\PitaService\Controller\FacilityController::releasetwilionumberAction">releasetwilionumberAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FacilityController.php.html#287"><abbr title="Sparefoot\PitaService\Controller\FacilityController::jsonunitlistdetailsAction">jsonunitlistdetailsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FeedsController.php.html#99"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_urlTitle">_urlTitle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="IncentivesController.php.html#56"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::redemptionRequestsAction">redemptionRequestsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="IndexController.php.html#130"><abbr title="Sparefoot\PitaService\Controller\IndexController::updateBookmarksAction">updateBookmarksAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#480"><abbr title="Sparefoot\PitaService\Controller\InventoryController::togglehostedwebsitetypeAction">togglehostedwebsitetypeAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#894"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unithtmlAction">unithtmlAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#983"><abbr title="Sparefoot\PitaService\Controller\InventoryController::facilityapproveAction">facilityapproveAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#2123"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatestreetviewAction">updatestreetviewAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#3232"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removefacilitycontactAction">removefacilitycontactAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#317"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledSaveAction">scheduledSaveAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#724"><abbr title="Sparefoot\PitaService\Controller\JobsController::childUpdateAction">childUpdateAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#799"><abbr title="Sparefoot\PitaService\Controller\JobsController::finishCode">finishCode</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#813"><abbr title="Sparefoot\PitaService\Controller\JobsController::successCode">successCode</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#866"><abbr title="Sparefoot\PitaService\Controller\JobsController::_verifyChildAllowed">_verifyChildAllowed</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#94"><abbr title="Sparefoot\PitaService\Controller\MaintenanceModeController::_sendMaintenanceModeEmail">_sendMaintenanceModeEmail</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="NetsuiteController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\NetsuiteController::statusAction">statusAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuickClientController.php.html#114"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuickrepController.php.html#135"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::chartAction">chartAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ReviewsController.php.html#80"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::deletereviewAction">deletereviewAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="StatementsController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\StatementsController::generateAction">generateAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TableauController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\TableauController::indexAction">indexAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TableauController.php.html#270"><abbr title="Sparefoot\PitaService\Controller\TableauController::deleteKeyAction">deleteKeyAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TestController.php.html#243"><abbr title="Sparefoot\PitaService\Controller\TestController::_saveTestVariation">_saveTestVariation</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserController.php.html#287"><abbr title="Sparefoot\PitaService\Controller\UserController::_createPitaUser">_createPitaUser</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserController.php.html#485"><abbr title="Sparefoot\PitaService\Controller\UserController::_buildUser">_buildUser</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserController.php.html#714"><abbr title="Sparefoot\PitaService\Controller\UserController::addstmtrecipientsAction">addstmtrecipientsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserController.php.html#748"><abbr title="Sparefoot\PitaService\Controller\UserController::removestmtrecipientsAction">removestmtrecipientsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="YellowpagesController.php.html#103"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::citygridAction">citygridAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="YellowpagesController.php.html#233"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::superpagesfacilityAction">superpagesfacilityAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::scriptFooter">scriptFooter</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#220"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::initAuthorization">initAuthorization</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#287"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::authenticateUser">authenticateUser</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#90"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisConst">genesisConst</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#269"><abbr title="Sparefoot\PitaService\Controller\AccountController::moveacctmgmtusersAction">moveacctmgmtusersAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#627"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeofflinereservationscdpAction">changeofflinereservationscdpAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#953"><abbr title="Sparefoot\PitaService\Controller\AccountController::addacctadminAction">addacctadminAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#1251"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeUser">_makeUser</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#2465"><abbr title="Sparefoot\PitaService\Controller\AccountController::updateadnetworkpriceAction">updateadnetworkpriceAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#2499"><abbr title="Sparefoot\PitaService\Controller\AccountController::updatehostedwebsitepriceAction">updatehostedwebsitepriceAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#2579"><abbr title="Sparefoot\PitaService\Controller\AccountController::clearaccounttermsAction">clearaccounttermsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AffiliateController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::createAction">createAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AffiliateController.php.html#94"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processCreate">_processCreate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AffiliateController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::addsiteAction">addsiteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AffiliateController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::updatesiteAction">updatesiteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AnalyticsController.php.html#94"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::loadqueryAction">loadqueryAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#601"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStorageMartUnitData">_pullStorageMartUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#649"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershiftUnitData">_pullCentershiftUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#735"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCubesmartUnitData">_pullCubesmartUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#755"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSelfStorageManagerUnitData">_pullSelfStorageManagerUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#881"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSiteLinkUnitData">_pullSiteLinkUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#906"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullQuickstorUnitData">_pullQuickstorUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BookingsController.php.html#177"><abbr title="Sparefoot\PitaService\Controller\BookingsController::historyAction">historyAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BookingsController.php.html#359"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changemoveoutAction">changemoveoutAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BookingsController.php.html#586"><abbr title="Sparefoot\PitaService\Controller\BookingsController::makebookingresidualAction">makebookingresidualAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BuyoutController.php.html#183"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::invoiceAction">invoiceAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BuyoutController.php.html#209"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::deleteAction">deleteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BuyoutController.php.html#312"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editBookingsAction">editBookingsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CdpController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchRunsAction">matchRunsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CdpController.php.html#158"><abbr title="Sparefoot\PitaService\Controller\CdpController::killMatchRunAction">killMatchRunAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#93"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_init">_init</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisastersController.php.html#64"><abbr title="Sparefoot\PitaService\Controller\DisastersController::includeContainedFacilities">includeContainedFacilities</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisastersController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\DisastersController::editDisasterAction">editDisasterAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_init">_init</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#251"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeRuling">updateDisputeRuling</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#280"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewRulingReason">updateDisputeReviewRulingReason</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#296"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewNotes">updateDisputeReviewNotes</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#321"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewAgent">updateDisputeReviewAgent</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#371"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_createCsv">_createCsv</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FacilityController.php.html#723"><abbr title="Sparefoot\PitaService\Controller\FacilityController::activateAction">activateAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FacilityController.php.html#752"><abbr title="Sparefoot\PitaService\Controller\FacilityController::deactivateAction">deactivateAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FaqsController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\FaqsController::getAction">getAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FaqsController.php.html#67"><abbr title="Sparefoot\PitaService\Controller\FaqsController::saveAction">saveAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FeedsController.php.html#54"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_prepHours">_prepHours</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="IncentivesController.php.html#103"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::parseIncentiveResponse">parseIncentiveResponse</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="IndexController.php.html#93"><abbr title="Sparefoot\PitaService\Controller\IndexController::addBookmarkAction">addBookmarkAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#98"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadpercentcompleteAction">loadpercentcompleteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#775"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadresaleratesAction">loadresaleratesAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#862"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addupdateresalebucketAction">addupdateresalebucketAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#1371"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatecustomclosureAction">updatecustomclosureAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3017"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unitexportAction">unitexportAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3515"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getsitelinkfacilitylistAction">getsitelinkfacilitylistAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3547"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getselfstoragemanagerfacilitylistAction">getselfstoragemanagerfacilitylistAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3579"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getcentershiftfacilitylistAction">getcentershiftfacilitylistAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3957"><abbr title="Sparefoot\PitaService\Controller\InventoryController::integrationsByAccountAction">integrationsByAccountAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3988"><abbr title="Sparefoot\PitaService\Controller\InventoryController::facilitiesByIntegrationAction">facilitiesByIntegrationAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#4018"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getfacilityAction">getfacilityAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#4049"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essfacilitylookupAction">essfacilitylookupAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#127"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandSaveAction">commandSaveAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#238"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledEditAction">scheduledEditAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#266"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledAddAction">scheduledAddAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#438"><abbr title="Sparefoot\PitaService\Controller\JobsController::summaryAction">summaryAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#533"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerZombieAction">workerZombieAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OmnomController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\OmnomController::indexAction">indexAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OmnomController.php.html#51"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByJob">_queryStatusByJob</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PaidmediaController.php.html#135"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::getCampaignList">getCampaignList</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PayoutController.php.html#153"><abbr title="Sparefoot\PitaService\Controller\PayoutController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PhotosController.php.html#67"><abbr title="Sparefoot\PitaService\Controller\PhotosController::approveAction">approveAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PingController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\PingController::index">index</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PublicController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\PublicController::searchServicesAction">searchServicesAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuickClientController.php.html#48"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::renderAction">renderAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuickClientController.php.html#149"><abbr title="Sparefoot\PitaService\Controller\QuickClientController::unitsAction">unitsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuickrepController.php.html#108"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::tableAction">tableAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#112"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::savenewAction">savenewAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ReportsController.php.html#138"><abbr title="Sparefoot\PitaService\Controller\ReportsController::tableAction">tableAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ReviewsController.php.html#193"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::setStatusAction">setStatusAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceareaController.php.html#55"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::getAction">getAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#82"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StatementsController.php.html#308"><abbr title="Sparefoot\PitaService\Controller\StatementsController::cancelInvoiceAction">cancelInvoiceAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StatementsController.php.html#397"><abbr title="Sparefoot\PitaService\Controller\StatementsController::pdfAction">pdfAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\TableauController::initView">initView</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#118"><abbr title="Sparefoot\PitaService\Controller\TableauController::editAction">editAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\TableauController::deleteAction">deleteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#211"><abbr title="Sparefoot\PitaService\Controller\TableauController::editKeyAction">editKeyAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#297"><abbr title="Sparefoot\PitaService\Controller\TableauController::massEditKeyAction">massEditKeyAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TestController.php.html#63"><abbr title="Sparefoot\PitaService\Controller\TestController::manageAction">manageAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TestController.php.html#91"><abbr title="Sparefoot\PitaService\Controller\TestController::manageuiAction">manageuiAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ToolsController.php.html#1138"><abbr title="Sparefoot\PitaService\Controller\ToolsController::cabinetAction">cabinetAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ToolsController.php.html#1290"><abbr title="Sparefoot\PitaService\Controller\ToolsController::pressPageAction">pressPageAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ToolsController.php.html#1317"><abbr title="Sparefoot\PitaService\Controller\ToolsController::pressPageEditorAction">pressPageEditorAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserController.php.html#326"><abbr title="Sparefoot\PitaService\Controller\UserController::_createPortalUser">_createPortalUser</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserController.php.html#468"><abbr title="Sparefoot\PitaService\Controller\UserController::_addFacilityContact">_addFacilityContact</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UtilitiesController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::updateInputsAction">updateInputsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YellowpagesController.php.html#148"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::citygridfacilityAction">citygridfacilityAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YellowpagesController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::superpagesAction">superpagesAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YellowpagesController.php.html#365"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::_initTrueDateRange">_initTrueDateRange</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#113"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::_exportServerReport">_exportServerReport</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::newQuery">newQuery</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#227"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::tableFromReport">tableFromReport</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#55"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::init">init</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#309"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getParam">getParam</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#319"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::_getParam">_getParam</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#350"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getTwig">getTwig</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#58"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::getMetricInfoGetter">getMetricInfoGetter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#112"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::getEnv">getEnv</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#367"><abbr title="Sparefoot\PitaService\Controller\AccountController::_resetSync">_resetSync</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#490"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeaccountminimumbidAction">changeaccountminimumbidAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#557"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeexclusiveAction">changeexclusiveAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#787"><abbr title="Sparefoot\PitaService\Controller\AccountController::unverifyAction">unverifyAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#807"><abbr title="Sparefoot\PitaService\Controller\AccountController::editinfoAction">editinfoAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#903"><abbr title="Sparefoot\PitaService\Controller\AccountController::removeacctuserAction">removeacctuserAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#929"><abbr title="Sparefoot\PitaService\Controller\AccountController::removeadminAction">removeadminAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#1999"><abbr title="Sparefoot\PitaService\Controller\AccountController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#2024"><abbr title="Sparefoot\PitaService\Controller\AccountController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#2399"><abbr title="Sparefoot\PitaService\Controller\AccountController::loadFacilityIdToFacilityMapForBillableEntity">loadFacilityIdToFacilityMapForBillableEntity</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#2534"><abbr title="Sparefoot\PitaService\Controller\AccountController::changedocusigncompleteAction">changedocusigncompleteAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#2559"><abbr title="Sparefoot\PitaService\Controller\AccountController::changetestaccountAction">changetestaccountAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#20"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getBidTypeContinueResidualMessage">getBidTypeContinueResidualMessage</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#88"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::bidCurrency">bidCurrency</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#105"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::labelText">labelText</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#132"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::formatDate">formatDate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AffiliateController.php.html#54"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::generateapiAction">generateapiAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AffiliateController.php.html#158"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processUpdate">_processUpdate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AffiliateController.php.html#311"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processDelExclusion">_processDelExclusion</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AnalyticsController.php.html#71"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::sqlAction">sqlAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApiController.php.html#498"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDoorswapFacilityData">_pullDoorswapFacilityData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApiController.php.html#675"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullUncleBobsUnitData">_pullUncleBobsUnitData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApiController.php.html#723"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullUsiUnitData">_pullUsiUnitData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApiController.php.html#791"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDoorswapUnitData">_pullDoorswapUnitData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BillingController.php.html#50"><abbr title="Sparefoot\PitaService\Controller\BillingController::exportemailsAction">exportemailsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BillingController.php.html#114"><abbr title="Sparefoot\PitaService\Controller\BillingController::deleteAction">deleteAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BookingsController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\BookingsController::confcodeAction">confcodeAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BookingsController.php.html#106"><abbr title="Sparefoot\PitaService\Controller\BookingsController::salesforceAction">salesforceAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BookingsController.php.html#312"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changemoveinAction">changemoveinAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BookingsController.php.html#340"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changeunitpriceAction">changeunitpriceAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BuyoutController.php.html#162"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::viewAction">viewAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CdpController.php.html#73"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchAccountsAction">matchAccountsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CdpController.php.html#132"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchBookingsAction">matchBookingsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CdpController.php.html#211"><abbr title="Sparefoot\PitaService\Controller\CdpController::runCdpAction">runCdpAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisastersController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\DisastersController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisastersController.php.html#114"><abbr title="Sparefoot\PitaService\Controller\DisastersController::addDisasterAction">addDisasterAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisputesController.php.html#312"><abbr title="Sparefoot\PitaService\Controller\DisputesController::deleteDisputeReviewNotes">deleteDisputeReviewNotes</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisputesController.php.html#430"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_getCsvLine">_getCsvLine</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisputesController.php.html#488"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_cleanCsvLine">_cleanCsvLine</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ErrorController.php.html#65"><abbr title="Sparefoot\PitaService\Controller\ErrorController::hipchatAction">hipchatAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FacilitiesController.php.html#120"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::bysourceAction">bysourceAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FacilityController.php.html#863"><abbr title="Sparefoot\PitaService\Controller\FacilityController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FederatedController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\FederatedController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FeedsController.php.html#13"><abbr title="Sparefoot\PitaService\Controller\FeedsController::citysearchAction">citysearchAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FeedsController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_facilityDetailsUrl">_facilityDetailsUrl</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="IncentivesController.php.html#90"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::updateStatusAction">updateStatusAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="IndexController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\IndexController::oldIndexAction">oldIndexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#620"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatetenantconnectprecalldigitsAction">updatetenantconnectprecalldigitsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#816"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateresalebucketAction">updateresalebucketAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#839"><abbr title="Sparefoot\PitaService\Controller\InventoryController::deleteresalebucketAction">deleteresalebucketAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#1398"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removecustomclosureAction">removecustomclosureAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2051"><abbr title="Sparefoot\PitaService\Controller\InventoryController::billableentitylistAction">billableentitylistAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2071"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_makeBillableEntityList">_makeBillableEntityList</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2088"><abbr title="Sparefoot\PitaService\Controller\InventoryController::streetviewAction">streetviewAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2161"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadlogAction">loadlogAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2555"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadyieldjsonAction">loadyieldjsonAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2728"><abbr title="Sparefoot\PitaService\Controller\InventoryController::visibilitycheckAction">visibilitycheckAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2881"><abbr title="Sparefoot\PitaService\Controller\InventoryController::syncAction">syncAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2907"><abbr title="Sparefoot\PitaService\Controller\InventoryController::phidosyncAction">phidosyncAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2991"><abbr title="Sparefoot\PitaService\Controller\InventoryController::resetlocationAction">resetlocationAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#3054"><abbr title="Sparefoot\PitaService\Controller\InventoryController::logexportAction">logexportAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#3421"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addaccessAction">addaccessAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#3449"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removeaccessAction">removeaccessAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#3917"><abbr title="Sparefoot\PitaService\Controller\InventoryController::copyReviewToFacility">copyReviewToFacility</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#50"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandAddAction">commandAddAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandLogAction">commandLogAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#102"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandEditAction">commandEditAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#154"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandDeleteAction">commandDeleteAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#364"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocLogAction">adhocLogAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#390"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocAddAction">adhocAddAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#586"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerLogAction">workerLogAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#651"><abbr title="Sparefoot\PitaService\Controller\JobsController::childAction">childAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#859"><abbr title="Sparefoot\PitaService\Controller\JobsController::dateDisplay">dateDisplay</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="LoginController.php.html#66"><abbr title="Sparefoot\PitaService\Controller\LoginController::getCurrentUser">getCurrentUser</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OmnomController.php.html#210"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getJobs">_getJobs</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OmnomController.php.html#230"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getIntegrations">_getIntegrations</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OmnomController.php.html#250"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getAccounts">_getAccounts</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PayoutController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\PayoutController::previewAction">previewAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PayoutController.php.html#123"><abbr title="Sparefoot\PitaService\Controller\PayoutController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PayoutController.php.html#181"><abbr title="Sparefoot\PitaService\Controller\PayoutController::_assignBookings">_assignBookings</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ProxyController.php.html#148"><abbr title="Sparefoot\PitaService\Controller\ProxyController::debugAction">debugAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PublicController.php.html#122"><abbr title="Sparefoot\PitaService\Controller\PublicController::searchAction">searchAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuickrepController.php.html#83"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::updateinputsAction">updateinputsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuickrepController.php.html#165"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::sqlAction">sqlAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicksoapController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::indexAction">indexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicksoapController.php.html#34"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::wsdlAction">wsdlAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicksoapController.php.html#77"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::generateWsdlResponse">generateWsdlResponse</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#91"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::saveorderAction">saveorderAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#191"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::removeAction">removeAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReferralsController.php.html#41"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::toggleconvertedAction">toggleconvertedAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReferralsController.php.html#60"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::toggleclaimedAction">toggleclaimedAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReportsController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\ReportsController::indexAction">indexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReportsController.php.html#166"><abbr title="Sparefoot\PitaService\Controller\ReportsController::sqlAction">sqlAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReviewsController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::getreviewAction">getreviewAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReviewsController.php.html#224"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::editReviewMessageAction">editReviewMessageAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RewardsController.php.html#84"><abbr title="Sparefoot\PitaService\Controller\RewardsController::browsekiindAction">browsekiindAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SalesController.php.html#28"><abbr title="Sparefoot\PitaService\Controller\SalesController::searchAction">searchAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SearchController.php.html#224"><abbr title="Sparefoot\PitaService\Controller\SearchController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceareaController.php.html#87"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::saveAction">saveAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceareaController.php.html#115"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::convexhullAction">convexhullAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceareaController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::simplifyAction">simplifyAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceareaController.php.html#167"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::getServiceAreaWktByFacilityId">getServiceAreaWktByFacilityId</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::testAction">testAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StatementsController.php.html#52"><abbr title="Sparefoot\PitaService\Controller\StatementsController::createAction">createAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StatementsController.php.html#124"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_export">_export</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TableauController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\TableauController::setErrorMessage">setErrorMessage</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TableauController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\TableauController::setSuccessMessage">setSuccessMessage</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TableauController.php.html#384"><abbr title="Sparefoot\PitaService\Controller\TableauController::addTeamAction">addTeamAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TestController.php.html#38"><abbr title="Sparefoot\PitaService\Controller\TestController::testsAction">testsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TestController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\TestController::ajaxcreatesearchAction">ajaxcreatesearchAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TestController.php.html#206"><abbr title="Sparefoot\PitaService\Controller\TestController::ajaxcreateuiAction">ajaxcreateuiAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\ToolsController::indexAction">indexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#60"><abbr title="Sparefoot\PitaService\Controller\ToolsController::perfectPhotosSelectorAction">perfectPhotosSelectorAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#81"><abbr title="Sparefoot\PitaService\Controller\ToolsController::perfectPhotosSelectorPostAction">perfectPhotosSelectorPostAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#1254"><abbr title="Sparefoot\PitaService\Controller\ToolsController::addFeatureFlagAction">addFeatureFlagAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#1272"><abbr title="Sparefoot\PitaService\Controller\ToolsController::deleteFeatureFlagAction">deleteFeatureFlagAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#448"><abbr title="Sparefoot\PitaService\Controller\UserController::isEmailValid">isEmailValid</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#458"><abbr title="Sparefoot\PitaService\Controller\UserController::_restrictFacilityAccess">_restrictFacilityAccess</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#1076"><abbr title="Sparefoot\PitaService\Controller\UserController::_addAcctMgmtUser">_addAcctMgmtUser</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#1195"><abbr title="Sparefoot\PitaService\Controller\UserController::_getActiveAcctIds">_getActiveAcctIds</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#1223"><abbr title="Sparefoot\PitaService\Controller\UserController::_getActiveListingAvailIds">_getActiveListingAvailIds</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UtilitiesController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UtilitiesController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::indexAction">indexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="YellowpagesController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([69,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([720,0,0,0,0,0,0,0,0,0,0,1], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,47,"<a href=\"AbstractGenericController.php.html#9\">Sparefoot\\PitaService\\Controller\\AbstractGenericController<\/a>"],[0,34,"<a href=\"AbstractRestrictedController.php.html#14\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController<\/a>"],[0,17,"<a href=\"AbstractView\/GenericView.php.html#10\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView<\/a>"],[0,344,"<a href=\"AccountController.php.html#10\">Sparefoot\\PitaService\\Controller\\AccountController<\/a>"],[0,15,"<a href=\"AccountView\/ListActionView.php.html#11\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView<\/a>"],[0,37,"<a href=\"AffiliateController.php.html#11\">Sparefoot\\PitaService\\Controller\\AffiliateController<\/a>"],[0,86,"<a href=\"AnalyticsController.php.html#15\">Sparefoot\\PitaService\\Controller\\AnalyticsController<\/a>"],[0,121,"<a href=\"ApiController.php.html#9\">Sparefoot\\PitaService\\Controller\\ApiController<\/a>"],[0,13,"<a href=\"BillingController.php.html#12\">Sparefoot\\PitaService\\Controller\\BillingController<\/a>"],[0,125,"<a href=\"BookingsController.php.html#10\">Sparefoot\\PitaService\\Controller\\BookingsController<\/a>"],[0,51,"<a href=\"BuyoutController.php.html#11\">Sparefoot\\PitaService\\Controller\\BuyoutController<\/a>"],[0,20,"<a href=\"CdpController.php.html#9\">Sparefoot\\PitaService\\Controller\\CdpController<\/a>"],[0,1,"<a href=\"CentaursearchController.php.html#8\">Sparefoot\\PitaService\\Controller\\CentaursearchController<\/a>"],[0,23,"<a href=\"CentaursearchapiController.php.html#9\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController<\/a>"],[0,5,"<a href=\"CitygridController.php.html#9\">Sparefoot\\PitaService\\Controller\\CitygridController<\/a>"],[0,4,"<a href=\"CpaPercentRolloutController.php.html#13\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController<\/a>"],[0,1,"<a href=\"CpaPercentRolloutView\/IndexActionView.php.html#11\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutView\\IndexActionView<\/a>"],[0,18,"<a href=\"DashboardController.php.html#10\">Sparefoot\\PitaService\\Controller\\DashboardController<\/a>"],[0,12,"<a href=\"DisastersController.php.html#10\">Sparefoot\\PitaService\\Controller\\DisastersController<\/a>"],[0,62,"<a href=\"DisputesController.php.html#12\">Sparefoot\\PitaService\\Controller\\DisputesController<\/a>"],[0,9,"<a href=\"EmailController.php.html#9\">Sparefoot\\PitaService\\Controller\\EmailController<\/a>"],[0,8,"<a href=\"ErrorController.php.html#11\">Sparefoot\\PitaService\\Controller\\ErrorController<\/a>"],[0,32,"<a href=\"FacilitiesController.php.html#10\">Sparefoot\\PitaService\\Controller\\FacilitiesController<\/a>"],[0,126,"<a href=\"FacilityController.php.html#26\">Sparefoot\\PitaService\\Controller\\FacilityController<\/a>"],[0,9,"<a href=\"FaqsController.php.html#10\">Sparefoot\\PitaService\\Controller\\FaqsController<\/a>"],[0,4,"<a href=\"FederatedController.php.html#11\">Sparefoot\\PitaService\\Controller\\FederatedController<\/a>"],[0,12,"<a href=\"FeedsController.php.html#11\">Sparefoot\\PitaService\\Controller\\FeedsController<\/a>"],[0,49,"<a href=\"FiltersController.php.html#11\">Sparefoot\\PitaService\\Controller\\FiltersController<\/a>"],[0,2,"<a href=\"HeatmapController.php.html#10\">Sparefoot\\PitaService\\Controller\\HeatmapController<\/a>"],[0,31,"<a href=\"IncentivesController.php.html#11\">Sparefoot\\PitaService\\Controller\\IncentivesController<\/a>"],[0,16,"<a href=\"IndexController.php.html#12\">Sparefoot\\PitaService\\Controller\\IndexController<\/a>"],[0,1,"<a href=\"IndexView\/IndexActionView.php.html#10\">Sparefoot\\PitaService\\Controller\\IndexView\\IndexActionView<\/a>"],[0,633,"<a href=\"InventoryController.php.html#13\">Sparefoot\\PitaService\\Controller\\InventoryController<\/a>"],[0,82,"<a href=\"JobsController.php.html#16\">Sparefoot\\PitaService\\Controller\\JobsController<\/a>"],[0,13,"<a href=\"LoginController.php.html#12\">Sparefoot\\PitaService\\Controller\\LoginController<\/a>"],[0,6,"<a href=\"MaintenanceModeController.php.html#9\">Sparefoot\\PitaService\\Controller\\MaintenanceModeController<\/a>"],[0,10,"<a href=\"NetsuiteController.php.html#10\">Sparefoot\\PitaService\\Controller\\NetsuiteController<\/a>"],[0,23,"<a href=\"OmnomController.php.html#9\">Sparefoot\\PitaService\\Controller\\OmnomController<\/a>"],[0,18,"<a href=\"PaidmediaController.php.html#10\">Sparefoot\\PitaService\\Controller\\PaidmediaController<\/a>"],[0,17,"<a href=\"PayoutController.php.html#12\">Sparefoot\\PitaService\\Controller\\PayoutController<\/a>"],[0,10,"<a href=\"PhotosController.php.html#10\">Sparefoot\\PitaService\\Controller\\PhotosController<\/a>"],[0,4,"<a href=\"PingController.php.html#10\">Sparefoot\\PitaService\\Controller\\PingController<\/a>"],[0,17,"<a href=\"PitaSearchController.php.html#13\">Sparefoot\\PitaService\\Controller\\PitaSearchController<\/a>"],[0,20,"<a href=\"ProxyController.php.html#16\">Sparefoot\\PitaService\\Controller\\ProxyController<\/a>"],[0,18,"<a href=\"PublicController.php.html#13\">Sparefoot\\PitaService\\Controller\\PublicController<\/a>"],[0,33,"<a href=\"QuickClientController.php.html#11\">Sparefoot\\PitaService\\Controller\\QuickClientController<\/a>"],[0,4,"<a href=\"QuickformController.php.html#8\">Sparefoot\\PitaService\\Controller\\QuickformController<\/a>"],[0,3,"<a href=\"QuickjobController.php.html#10\">Sparefoot\\PitaService\\Controller\\QuickjobController<\/a>"],[0,25,"<a href=\"QuickrepController.php.html#15\">Sparefoot\\PitaService\\Controller\\QuickrepController<\/a>"],[0,7,"<a href=\"QuicksoapController.php.html#16\">Sparefoot\\PitaService\\Controller\\QuicksoapController<\/a>"],[0,17,"<a href=\"QuicktaggerController.php.html#10\">Sparefoot\\PitaService\\Controller\\QuicktaggerController<\/a>"],[0,7,"<a href=\"ReferralsController.php.html#9\">Sparefoot\\PitaService\\Controller\\ReferralsController<\/a>"],[0,18,"<a href=\"ReportsController.php.html#14\">Sparefoot\\PitaService\\Controller\\ReportsController<\/a>"],[0,24,"<a href=\"ReviewsController.php.html#10\">Sparefoot\\PitaService\\Controller\\ReviewsController<\/a>"],[0,10,"<a href=\"RewardsController.php.html#9\">Sparefoot\\PitaService\\Controller\\RewardsController<\/a>"],[0,8,"<a href=\"SalesController.php.html#9\">Sparefoot\\PitaService\\Controller\\SalesController<\/a>"],[0,39,"<a href=\"SalesforceController.php.html#11\">Sparefoot\\PitaService\\Controller\\SalesforceController<\/a>"],[0,24,"<a href=\"SearchController.php.html#9\">Sparefoot\\PitaService\\Controller\\SearchController<\/a>"],[0,14,"<a href=\"ServiceareaController.php.html#10\">Sparefoot\\PitaService\\Controller\\ServiceareaController<\/a>"],[0,7,"<a href=\"SoftwarepartnerController.php.html#9\">Sparefoot\\PitaService\\Controller\\SoftwarepartnerController<\/a>"],[0,14,"<a href=\"SphinxsearchController.php.html#9\">Sparefoot\\PitaService\\Controller\\SphinxsearchController<\/a>"],[0,48,"<a href=\"StatementsController.php.html#15\">Sparefoot\\PitaService\\Controller\\StatementsController<\/a>"],[0,56,"<a href=\"TableauController.php.html#13\">Sparefoot\\PitaService\\Controller\\TableauController<\/a>"],[0,27,"<a href=\"TestController.php.html#10\">Sparefoot\\PitaService\\Controller\\TestController<\/a>"],[0,168,"<a href=\"ToolsController.php.html#14\">Sparefoot\\PitaService\\Controller\\ToolsController<\/a>"],[0,3,"<a href=\"TriController.php.html#9\">Sparefoot\\PitaService\\Controller\\TriController<\/a>"],[0,215,"<a href=\"UserController.php.html#13\">Sparefoot\\PitaService\\Controller\\UserController<\/a>"],[0,17,"<a href=\"UtilitiesController.php.html#12\">Sparefoot\\PitaService\\Controller\\UtilitiesController<\/a>"],[0,42,"<a href=\"YellowpagesController.php.html#9\">Sparefoot\\PitaService\\Controller\\YellowpagesController<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AbstractGenericController.php.html#18\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::__construct<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#39\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::getMetrics<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#44\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::getFilters<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#49\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::getCustoms<\/a>"],[0,4,"<a href=\"AbstractGenericController.php.html#60\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::export<\/a>"],[0,4,"<a href=\"AbstractGenericController.php.html#96\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::_exportReport<\/a>"],[0,2,"<a href=\"AbstractGenericController.php.html#113\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::_exportServerReport<\/a>"],[0,8,"<a href=\"AbstractGenericController.php.html#140\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::processQueryParams<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#168\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::newBuilder<\/a>"],[0,2,"<a href=\"AbstractGenericController.php.html#175\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::newQuery<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#187\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::processQuery<\/a>"],[0,9,"<a href=\"AbstractGenericController.php.html#195\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::processReport<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#222\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::getDefaultQuery<\/a>"],[0,2,"<a href=\"AbstractGenericController.php.html#227\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::tableFromReport<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#251\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::initFilterList<\/a>"],[0,7,"<a href=\"AbstractGenericController.php.html#278\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::populateFilter<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#306\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::toCamelCase<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#22\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::__construct<\/a>"],[0,2,"<a href=\"AbstractRestrictedController.php.html#55\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::init<\/a>"],[0,4,"<a href=\"AbstractRestrictedController.php.html#72\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::initGeneralData<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#109\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::setTitlePage<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#115\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::initHeadElement<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#168\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::setScripts<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#173\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getScripts<\/a>"],[0,3,"<a href=\"AbstractRestrictedController.php.html#178\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::scriptFooter<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#195\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::_createAcronym<\/a>"],[0,3,"<a href=\"AbstractRestrictedController.php.html#220\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::initAuthorization<\/a>"],[0,3,"<a href=\"AbstractRestrictedController.php.html#287\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::authenticateUser<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#299\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getLoggedUser<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#304\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getGenesisUserAccess<\/a>"],[0,2,"<a href=\"AbstractRestrictedController.php.html#309\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getParam<\/a>"],[0,2,"<a href=\"AbstractRestrictedController.php.html#319\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::_getParam<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#329\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::accessDeniedNotUse<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#342\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::accessDenied<\/a>"],[0,2,"<a href=\"AbstractRestrictedController.php.html#350\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getTwig<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#362\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::dispatchError<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#379\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::dispatchSuccess<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#401\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getControllerActionName<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#12\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::get_class<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#17\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::implode<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#23\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisUtilVersionerVersion<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#28\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::strtotime<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#34\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::newDateTime<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#39\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::formatEstimatedMoney<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#45\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisUtilFormatterFormatDateDiff<\/a>"],[0,2,"<a href=\"AbstractView\/GenericView.php.html#58\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::getMetricInfoGetter<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#67\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisConfigServerIsProduction<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#73\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisEntitySiteCommission<\/a>"],[0,3,"<a href=\"AbstractView\/GenericView.php.html#90\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisConst<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#107\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::dateFormat<\/a>"],[0,2,"<a href=\"AbstractView\/GenericView.php.html#112\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::getEnv<\/a>"],[0,1,"<a href=\"AccountController.php.html#14\">Sparefoot\\PitaService\\Controller\\AccountController::_init<\/a>"],[0,1,"<a href=\"AccountController.php.html#20\">Sparefoot\\PitaService\\Controller\\AccountController::indexAction<\/a>"],[0,18,"<a href=\"AccountController.php.html#31\">Sparefoot\\PitaService\\Controller\\AccountController::listAction<\/a>"],[0,9,"<a href=\"AccountController.php.html#119\">Sparefoot\\PitaService\\Controller\\AccountController::_export<\/a>"],[0,10,"<a href=\"AccountController.php.html#178\">Sparefoot\\PitaService\\Controller\\AccountController::moveintegrationAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#269\">Sparefoot\\PitaService\\Controller\\AccountController::moveacctmgmtusersAction<\/a>"],[0,10,"<a href=\"AccountController.php.html#303\">Sparefoot\\PitaService\\Controller\\AccountController::syncAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#367\">Sparefoot\\PitaService\\Controller\\AccountController::_resetSync<\/a>"],[0,1,"<a href=\"AccountController.php.html#389\">Sparefoot\\PitaService\\Controller\\AccountController::changebidtypeAction<\/a>"],[0,1,"<a href=\"AccountController.php.html#414\">Sparefoot\\PitaService\\Controller\\AccountController::changesupportexistingltvreservationsAction<\/a>"],[0,6,"<a href=\"AccountController.php.html#440\">Sparefoot\\PitaService\\Controller\\AccountController::changebidsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#490\">Sparefoot\\PitaService\\Controller\\AccountController::changeaccountminimumbidAction<\/a>"],[0,4,"<a href=\"AccountController.php.html#520\">Sparefoot\\PitaService\\Controller\\AccountController::changeminimumbidsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#557\">Sparefoot\\PitaService\\Controller\\AccountController::changeexclusiveAction<\/a>"],[0,4,"<a href=\"AccountController.php.html#584\">Sparefoot\\PitaService\\Controller\\AccountController::changeprimarycontactAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#627\">Sparefoot\\PitaService\\Controller\\AccountController::changeofflinereservationscdpAction<\/a>"],[0,12,"<a href=\"AccountController.php.html#657\">Sparefoot\\PitaService\\Controller\\AccountController::verifyAction<\/a>"],[0,11,"<a href=\"AccountController.php.html#747\">Sparefoot\\PitaService\\Controller\\AccountController::getintegrationdetailsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#787\">Sparefoot\\PitaService\\Controller\\AccountController::unverifyAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#807\">Sparefoot\\PitaService\\Controller\\AccountController::editinfoAction<\/a>"],[0,6,"<a href=\"AccountController.php.html#840\">Sparefoot\\PitaService\\Controller\\AccountController::getacctdetailsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#903\">Sparefoot\\PitaService\\Controller\\AccountController::removeacctuserAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#929\">Sparefoot\\PitaService\\Controller\\AccountController::removeadminAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#953\">Sparefoot\\PitaService\\Controller\\AccountController::addacctadminAction<\/a>"],[0,43,"<a href=\"AccountController.php.html#986\">Sparefoot\\PitaService\\Controller\\AccountController::newacctAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#1251\">Sparefoot\\PitaService\\Controller\\AccountController::_makeUser<\/a>"],[0,1,"<a href=\"AccountController.php.html#1274\">Sparefoot\\PitaService\\Controller\\AccountController::_makeAcctMgmtUser<\/a>"],[0,31,"<a href=\"AccountController.php.html#1286\">Sparefoot\\PitaService\\Controller\\AccountController::_makeIntegration<\/a>"],[0,6,"<a href=\"AccountController.php.html#1488\">Sparefoot\\PitaService\\Controller\\AccountController::deleteaccountAction<\/a>"],[0,47,"<a href=\"AccountController.php.html#1527\">Sparefoot\\PitaService\\Controller\\AccountController::changeintegrationcredsAction<\/a>"],[0,9,"<a href=\"AccountController.php.html#1813\">Sparefoot\\PitaService\\Controller\\AccountController::getintegrationcredsAction<\/a>"],[0,7,"<a href=\"AccountController.php.html#1902\">Sparefoot\\PitaService\\Controller\\AccountController::geteditintegrationdetailsAction<\/a>"],[0,7,"<a href=\"AccountController.php.html#1949\">Sparefoot\\PitaService\\Controller\\AccountController::saveeditintegrationdetailsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#1999\">Sparefoot\\PitaService\\Controller\\AccountController::getnotesAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#2024\">Sparefoot\\PitaService\\Controller\\AccountController::changenotesAction<\/a>"],[0,10,"<a href=\"AccountController.php.html#2051\">Sparefoot\\PitaService\\Controller\\AccountController::getbillableentitiesAction<\/a>"],[0,6,"<a href=\"AccountController.php.html#2124\">Sparefoot\\PitaService\\Controller\\AccountController::getbillableentitydetailsAction<\/a>"],[0,10,"<a href=\"AccountController.php.html#2172\">Sparefoot\\PitaService\\Controller\\AccountController::updateqbcustomerAction<\/a>"],[0,18,"<a href=\"AccountController.php.html#2268\">Sparefoot\\PitaService\\Controller\\AccountController::updatenscustomerAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#2399\">Sparefoot\\PitaService\\Controller\\AccountController::loadFacilityIdToFacilityMapForBillableEntity<\/a>"],[0,10,"<a href=\"AccountController.php.html#2411\">Sparefoot\\PitaService\\Controller\\AccountController::getbillableentityfacilitiesAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#2465\">Sparefoot\\PitaService\\Controller\\AccountController::updateadnetworkpriceAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#2499\">Sparefoot\\PitaService\\Controller\\AccountController::updatehostedwebsitepriceAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#2534\">Sparefoot\\PitaService\\Controller\\AccountController::changedocusigncompleteAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#2559\">Sparefoot\\PitaService\\Controller\\AccountController::changetestaccountAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#2579\">Sparefoot\\PitaService\\Controller\\AccountController::clearaccounttermsAction<\/a>"],[0,2,"<a href=\"AccountView\/ListActionView.php.html#20\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::getBidTypeContinueResidualMessage<\/a>"],[0,4,"<a href=\"AccountView\/ListActionView.php.html#37\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::getAdminsHtml<\/a>"],[0,1,"<a href=\"AccountView\/ListActionView.php.html#64\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::numCoporations<\/a>"],[0,1,"<a href=\"AccountView\/ListActionView.php.html#76\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::numFacilities<\/a>"],[0,2,"<a href=\"AccountView\/ListActionView.php.html#88\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::bidCurrency<\/a>"],[0,2,"<a href=\"AccountView\/ListActionView.php.html#105\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::labelText<\/a>"],[0,2,"<a href=\"AccountView\/ListActionView.php.html#132\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::formatDate<\/a>"],[0,1,"<a href=\"AccountView\/ListActionView.php.html#146\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::getPaymentTypes<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#19\">Sparefoot\\PitaService\\Controller\\AffiliateController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#27\">Sparefoot\\PitaService\\Controller\\AffiliateController::_init<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#35\">Sparefoot\\PitaService\\Controller\\AffiliateController::indexAction<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#42\">Sparefoot\\PitaService\\Controller\\AffiliateController::listAction<\/a>"],[0,2,"<a href=\"AffiliateController.php.html#54\">Sparefoot\\PitaService\\Controller\\AffiliateController::generateapiAction<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#69\">Sparefoot\\PitaService\\Controller\\AffiliateController::_generateApiKey<\/a>"],[0,3,"<a href=\"AffiliateController.php.html#74\">Sparefoot\\PitaService\\Controller\\AffiliateController::createAction<\/a>"],[0,3,"<a href=\"AffiliateController.php.html#94\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processCreate<\/a>"],[0,4,"<a href=\"AffiliateController.php.html#124\">Sparefoot\\PitaService\\Controller\\AffiliateController::updateAction<\/a>"],[0,2,"<a href=\"AffiliateController.php.html#158\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processUpdate<\/a>"],[0,3,"<a href=\"AffiliateController.php.html#178\">Sparefoot\\PitaService\\Controller\\AffiliateController::addsiteAction<\/a>"],[0,3,"<a href=\"AffiliateController.php.html#199\">Sparefoot\\PitaService\\Controller\\AffiliateController::updatesiteAction<\/a>"],[0,4,"<a href=\"AffiliateController.php.html#231\">Sparefoot\\PitaService\\Controller\\AffiliateController::exclusionsAction<\/a>"],[0,5,"<a href=\"AffiliateController.php.html#266\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processSaveSite<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#300\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processAddExclusion<\/a>"],[0,2,"<a href=\"AffiliateController.php.html#311\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processDelExclusion<\/a>"],[0,1,"<a href=\"AnalyticsController.php.html#23\">Sparefoot\\PitaService\\Controller\\AnalyticsController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"AnalyticsController.php.html#31\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_init<\/a>"],[0,4,"<a href=\"AnalyticsController.php.html#40\">Sparefoot\\PitaService\\Controller\\AnalyticsController::indexAction<\/a>"],[0,2,"<a href=\"AnalyticsController.php.html#71\">Sparefoot\\PitaService\\Controller\\AnalyticsController::sqlAction<\/a>"],[0,1,"<a href=\"AnalyticsController.php.html#84\">Sparefoot\\PitaService\\Controller\\AnalyticsController::savequeryAction<\/a>"],[0,3,"<a href=\"AnalyticsController.php.html#94\">Sparefoot\\PitaService\\Controller\\AnalyticsController::loadqueryAction<\/a>"],[0,50,"<a href=\"AnalyticsController.php.html#156\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_export<\/a>"],[0,1,"<a href=\"AnalyticsController.php.html#313\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_getDefaultQuery<\/a>"],[0,16,"<a href=\"AnalyticsController.php.html#328\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_processQuery<\/a>"],[0,6,"<a href=\"AnalyticsController.php.html#428\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_processReport<\/a>"],[0,1,"<a href=\"AnalyticsController.php.html#456\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_buildSql<\/a>"],[0,1,"<a href=\"ApiController.php.html#16\">Sparefoot\\PitaService\\Controller\\ApiController::_init<\/a>"],[0,24,"<a href=\"ApiController.php.html#36\">Sparefoot\\PitaService\\Controller\\ApiController::unitsAction<\/a>"],[0,28,"<a href=\"ApiController.php.html#153\">Sparefoot\\PitaService\\Controller\\ApiController::exportunitapiAction<\/a>"],[0,10,"<a href=\"ApiController.php.html#419\">Sparefoot\\PitaService\\Controller\\ApiController::facilitiesAction<\/a>"],[0,1,"<a href=\"ApiController.php.html#473\">Sparefoot\\PitaService\\Controller\\ApiController::_pullSiteLinkFacilityData<\/a>"],[0,1,"<a href=\"ApiController.php.html#486\">Sparefoot\\PitaService\\Controller\\ApiController::_pullQuikstorFacilityData<\/a>"],[0,2,"<a href=\"ApiController.php.html#498\">Sparefoot\\PitaService\\Controller\\ApiController::_pullDoorswapFacilityData<\/a>"],[0,1,"<a href=\"ApiController.php.html#520\">Sparefoot\\PitaService\\Controller\\ApiController::_pullStoredgeFacilityData<\/a>"],[0,1,"<a href=\"ApiController.php.html#527\">Sparefoot\\PitaService\\Controller\\ApiController::_pullCentershiftFacilityData<\/a>"],[0,5,"<a href=\"ApiController.php.html#539\">Sparefoot\\PitaService\\Controller\\ApiController::_pullStorageMartFacilityData<\/a>"],[0,3,"<a href=\"ApiController.php.html#601\">Sparefoot\\PitaService\\Controller\\ApiController::_pullStorageMartUnitData<\/a>"],[0,3,"<a href=\"ApiController.php.html#649\">Sparefoot\\PitaService\\Controller\\ApiController::_pullCentershiftUnitData<\/a>"],[0,2,"<a href=\"ApiController.php.html#675\">Sparefoot\\PitaService\\Controller\\ApiController::_pullUncleBobsUnitData<\/a>"],[0,4,"<a href=\"ApiController.php.html#693\">Sparefoot\\PitaService\\Controller\\ApiController::_pullCentershift4UnitData<\/a>"],[0,2,"<a href=\"ApiController.php.html#723\">Sparefoot\\PitaService\\Controller\\ApiController::_pullUsiUnitData<\/a>"],[0,3,"<a href=\"ApiController.php.html#735\">Sparefoot\\PitaService\\Controller\\ApiController::_pullCubesmartUnitData<\/a>"],[0,3,"<a href=\"ApiController.php.html#755\">Sparefoot\\PitaService\\Controller\\ApiController::_pullSelfStorageManagerUnitData<\/a>"],[0,1,"<a href=\"ApiController.php.html#779\">Sparefoot\\PitaService\\Controller\\ApiController::_pullStoredgeUnitData<\/a>"],[0,2,"<a href=\"ApiController.php.html#791\">Sparefoot\\PitaService\\Controller\\ApiController::_pullDoorswapUnitData<\/a>"],[0,7,"<a href=\"ApiController.php.html#819\">Sparefoot\\PitaService\\Controller\\ApiController::_pullEasyStorageSolutionsUnits<\/a>"],[0,7,"<a href=\"ApiController.php.html#854\">Sparefoot\\PitaService\\Controller\\ApiController::_pullExtraspaceUnits<\/a>"],[0,3,"<a href=\"ApiController.php.html#881\">Sparefoot\\PitaService\\Controller\\ApiController::_pullSiteLinkUnitData<\/a>"],[0,3,"<a href=\"ApiController.php.html#906\">Sparefoot\\PitaService\\Controller\\ApiController::_pullQuickstorUnitData<\/a>"],[0,4,"<a href=\"ApiController.php.html#932\">Sparefoot\\PitaService\\Controller\\ApiController::_pullDomicoUnitData<\/a>"],[0,1,"<a href=\"BillingController.php.html#20\">Sparefoot\\PitaService\\Controller\\BillingController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"BillingController.php.html#27\">Sparefoot\\PitaService\\Controller\\BillingController::_init<\/a>"],[0,1,"<a href=\"BillingController.php.html#33\">Sparefoot\\PitaService\\Controller\\BillingController::indexAction<\/a>"],[0,2,"<a href=\"BillingController.php.html#50\">Sparefoot\\PitaService\\Controller\\BillingController::exportemailsAction<\/a>"],[0,1,"<a href=\"BillingController.php.html#80\">Sparefoot\\PitaService\\Controller\\BillingController::openAction<\/a>"],[0,1,"<a href=\"BillingController.php.html#97\">Sparefoot\\PitaService\\Controller\\BillingController::closeAction<\/a>"],[0,2,"<a href=\"BillingController.php.html#114\">Sparefoot\\PitaService\\Controller\\BillingController::deleteAction<\/a>"],[0,4,"<a href=\"BillingController.php.html#130\">Sparefoot\\PitaService\\Controller\\BillingController::_runQuery<\/a>"],[0,1,"<a href=\"BookingsController.php.html#18\">Sparefoot\\PitaService\\Controller\\BookingsController::initBeforeControllerAction<\/a>"],[0,4,"<a href=\"BookingsController.php.html#30\">Sparefoot\\PitaService\\Controller\\BookingsController::indexAction<\/a>"],[0,2,"<a href=\"BookingsController.php.html#85\">Sparefoot\\PitaService\\Controller\\BookingsController::confcodeAction<\/a>"],[0,2,"<a href=\"BookingsController.php.html#106\">Sparefoot\\PitaService\\Controller\\BookingsController::salesforceAction<\/a>"],[0,1,"<a href=\"BookingsController.php.html#162\">Sparefoot\\PitaService\\Controller\\BookingsController::getqueryAction<\/a>"],[0,3,"<a href=\"BookingsController.php.html#177\">Sparefoot\\PitaService\\Controller\\BookingsController::historyAction<\/a>"],[0,15,"<a href=\"BookingsController.php.html#195\">Sparefoot\\PitaService\\Controller\\BookingsController::getbookinginfoAction<\/a>"],[0,6,"<a href=\"BookingsController.php.html#273\">Sparefoot\\PitaService\\Controller\\BookingsController::jsondetailsAction<\/a>"],[0,2,"<a href=\"BookingsController.php.html#312\">Sparefoot\\PitaService\\Controller\\BookingsController::changemoveinAction<\/a>"],[0,2,"<a href=\"BookingsController.php.html#340\">Sparefoot\\PitaService\\Controller\\BookingsController::changeunitpriceAction<\/a>"],[0,3,"<a href=\"BookingsController.php.html#359\">Sparefoot\\PitaService\\Controller\\BookingsController::changemoveoutAction<\/a>"],[0,6,"<a href=\"BookingsController.php.html#384\">Sparefoot\\PitaService\\Controller\\BookingsController::addduplicateAction<\/a>"],[0,15,"<a href=\"BookingsController.php.html#444\">Sparefoot\\PitaService\\Controller\\BookingsController::changebookingsAction<\/a>"],[0,20,"<a href=\"BookingsController.php.html#519\">Sparefoot\\PitaService\\Controller\\BookingsController::_changeBookingState<\/a>"],[0,3,"<a href=\"BookingsController.php.html#586\">Sparefoot\\PitaService\\Controller\\BookingsController::makebookingresidualAction<\/a>"],[0,18,"<a href=\"BookingsController.php.html#609\">Sparefoot\\PitaService\\Controller\\BookingsController::changenotesAction<\/a>"],[0,4,"<a href=\"BookingsController.php.html#833\">Sparefoot\\PitaService\\Controller\\BookingsController::getunitinfoAction<\/a>"],[0,5,"<a href=\"BookingsController.php.html#869\">Sparefoot\\PitaService\\Controller\\BookingsController::previewemailsAction<\/a>"],[0,6,"<a href=\"BookingsController.php.html#909\">Sparefoot\\PitaService\\Controller\\BookingsController::resendemailsAction<\/a>"],[0,1,"<a href=\"BookingsController.php.html#946\">Sparefoot\\PitaService\\Controller\\BookingsController::moreActionsAction<\/a>"],[0,6,"<a href=\"BookingsController.php.html#959\">Sparefoot\\PitaService\\Controller\\BookingsController::sitelinkledgerAction<\/a>"],[0,1,"<a href=\"BuyoutController.php.html#24\">Sparefoot\\PitaService\\Controller\\BuyoutController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"BuyoutController.php.html#41\">Sparefoot\\PitaService\\Controller\\BuyoutController::_init<\/a>"],[0,1,"<a href=\"BuyoutController.php.html#49\">Sparefoot\\PitaService\\Controller\\BuyoutController::indexAction<\/a>"],[0,6,"<a href=\"BuyoutController.php.html#68\">Sparefoot\\PitaService\\Controller\\BuyoutController::createAction<\/a>"],[0,5,"<a href=\"BuyoutController.php.html#118\">Sparefoot\\PitaService\\Controller\\BuyoutController::editAction<\/a>"],[0,2,"<a href=\"BuyoutController.php.html#162\">Sparefoot\\PitaService\\Controller\\BuyoutController::viewAction<\/a>"],[0,3,"<a href=\"BuyoutController.php.html#183\">Sparefoot\\PitaService\\Controller\\BuyoutController::invoiceAction<\/a>"],[0,3,"<a href=\"BuyoutController.php.html#209\">Sparefoot\\PitaService\\Controller\\BuyoutController::deleteAction<\/a>"],[0,6,"<a href=\"BuyoutController.php.html#230\">Sparefoot\\PitaService\\Controller\\BuyoutController::downloadAction<\/a>"],[0,4,"<a href=\"BuyoutController.php.html#274\">Sparefoot\\PitaService\\Controller\\BuyoutController::editUpdateAction<\/a>"],[0,1,"<a href=\"BuyoutController.php.html#301\">Sparefoot\\PitaService\\Controller\\BuyoutController::editUpdateQuoteAction<\/a>"],[0,3,"<a href=\"BuyoutController.php.html#312\">Sparefoot\\PitaService\\Controller\\BuyoutController::editBookingsAction<\/a>"],[0,7,"<a href=\"BuyoutController.php.html#334\">Sparefoot\\PitaService\\Controller\\BuyoutController::_updatePendingBookings<\/a>"],[0,8,"<a href=\"BuyoutController.php.html#373\">Sparefoot\\PitaService\\Controller\\BuyoutController::_updateExistingTenants<\/a>"],[0,1,"<a href=\"CdpController.php.html#17\">Sparefoot\\PitaService\\Controller\\CdpController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"CdpController.php.html#24\">Sparefoot\\PitaService\\Controller\\CdpController::_init<\/a>"],[0,1,"<a href=\"CdpController.php.html#33\">Sparefoot\\PitaService\\Controller\\CdpController::indexAction<\/a>"],[0,3,"<a href=\"CdpController.php.html#42\">Sparefoot\\PitaService\\Controller\\CdpController::matchRunsAction<\/a>"],[0,2,"<a href=\"CdpController.php.html#73\">Sparefoot\\PitaService\\Controller\\CdpController::matchAccountsAction<\/a>"],[0,1,"<a href=\"CdpController.php.html#94\">Sparefoot\\PitaService\\Controller\\CdpController::matchFacilitiesAction<\/a>"],[0,2,"<a href=\"CdpController.php.html#132\">Sparefoot\\PitaService\\Controller\\CdpController::matchBookingsAction<\/a>"],[0,3,"<a href=\"CdpController.php.html#158\">Sparefoot\\PitaService\\Controller\\CdpController::killMatchRunAction<\/a>"],[0,4,"<a href=\"CdpController.php.html#184\">Sparefoot\\PitaService\\Controller\\CdpController::viewLogAction<\/a>"],[0,2,"<a href=\"CdpController.php.html#211\">Sparefoot\\PitaService\\Controller\\CdpController::runCdpAction<\/a>"],[0,1,"<a href=\"CentaursearchController.php.html#10\">Sparefoot\\PitaService\\Controller\\CentaursearchController::index<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#17\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#41\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_getApiResponse<\/a>"],[0,6,"<a href=\"CentaursearchapiController.php.html#56\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_getApiResponseTest<\/a>"],[0,3,"<a href=\"CentaursearchapiController.php.html#93\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_init<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#109\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::indexAction<\/a>"],[0,9,"<a href=\"CentaursearchapiController.php.html#115\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::testAction<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#207\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_initWeights<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#216\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_conductSearch<\/a>"],[0,1,"<a href=\"CitygridController.php.html#17\">Sparefoot\\PitaService\\Controller\\CitygridController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"CitygridController.php.html#24\">Sparefoot\\PitaService\\Controller\\CitygridController::_init<\/a>"],[0,1,"<a href=\"CitygridController.php.html#29\">Sparefoot\\PitaService\\Controller\\CitygridController::resyncAction<\/a>"],[0,1,"<a href=\"CitygridController.php.html#38\">Sparefoot\\PitaService\\Controller\\CitygridController::indexAction<\/a>"],[0,1,"<a href=\"CitygridController.php.html#45\">Sparefoot\\PitaService\\Controller\\CitygridController::listAction<\/a>"],[0,1,"<a href=\"CpaPercentRolloutController.php.html#21\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"CpaPercentRolloutController.php.html#29\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController::_init<\/a>"],[0,1,"<a href=\"CpaPercentRolloutController.php.html#40\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController::indexAction<\/a>"],[0,1,"<a href=\"CpaPercentRolloutController.php.html#61\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController::dashboardAction<\/a>"],[0,1,"<a href=\"CpaPercentRolloutView\/IndexActionView.php.html#14\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutView\\IndexActionView::usortReports<\/a>"],[0,1,"<a href=\"DashboardController.php.html#14\">Sparefoot\\PitaService\\Controller\\DashboardController::_init<\/a>"],[0,1,"<a href=\"DashboardController.php.html#21\">Sparefoot\\PitaService\\Controller\\DashboardController::index<\/a>"],[0,1,"<a href=\"DashboardController.php.html#39\">Sparefoot\\PitaService\\Controller\\DashboardController::indexAction<\/a>"],[0,1,"<a href=\"DashboardController.php.html#45\">Sparefoot\\PitaService\\Controller\\DashboardController::_loadData<\/a>"],[0,1,"<a href=\"DashboardController.php.html#106\">Sparefoot\\PitaService\\Controller\\DashboardController::initDashboardLayout<\/a>"],[0,1,"<a href=\"DashboardController.php.html#115\">Sparefoot\\PitaService\\Controller\\DashboardController::getAction<\/a>"],[0,6,"<a href=\"DashboardController.php.html#120\">Sparefoot\\PitaService\\Controller\\DashboardController::bookingsAction<\/a>"],[0,1,"<a href=\"DashboardController.php.html#186\">Sparefoot\\PitaService\\Controller\\DashboardController::initDataForBookingsView<\/a>"],[0,5,"<a href=\"DashboardController.php.html#191\">Sparefoot\\PitaService\\Controller\\DashboardController::initDailyBookingsProjection<\/a>"],[0,1,"<a href=\"DisastersController.php.html#17\">Sparefoot\\PitaService\\Controller\\DisastersController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"DisastersController.php.html#29\">Sparefoot\\PitaService\\Controller\\DisastersController::indexAction<\/a>"],[0,2,"<a href=\"DisastersController.php.html#47\">Sparefoot\\PitaService\\Controller\\DisastersController::facilitiesAction<\/a>"],[0,3,"<a href=\"DisastersController.php.html#64\">Sparefoot\\PitaService\\Controller\\DisastersController::includeContainedFacilities<\/a>"],[0,2,"<a href=\"DisastersController.php.html#114\">Sparefoot\\PitaService\\Controller\\DisastersController::addDisasterAction<\/a>"],[0,3,"<a href=\"DisastersController.php.html#142\">Sparefoot\\PitaService\\Controller\\DisastersController::editDisasterAction<\/a>"],[0,1,"<a href=\"DisputesController.php.html#21\">Sparefoot\\PitaService\\Controller\\DisputesController::initBeforeControllerAction<\/a>"],[0,3,"<a href=\"DisputesController.php.html#27\">Sparefoot\\PitaService\\Controller\\DisputesController::_init<\/a>"],[0,1,"<a href=\"DisputesController.php.html#51\">Sparefoot\\PitaService\\Controller\\DisputesController::indexAction<\/a>"],[0,6,"<a href=\"DisputesController.php.html#65\">Sparefoot\\PitaService\\Controller\\DisputesController::opendisputesAction<\/a>"],[0,6,"<a href=\"DisputesController.php.html#102\">Sparefoot\\PitaService\\Controller\\DisputesController::closeddisputesAction<\/a>"],[0,1,"<a href=\"DisputesController.php.html#139\">Sparefoot\\PitaService\\Controller\\DisputesController::reviewdisputeAction<\/a>"],[0,9,"<a href=\"DisputesController.php.html#161\">Sparefoot\\PitaService\\Controller\\DisputesController::applyRuling<\/a>"],[0,7,"<a href=\"DisputesController.php.html#226\">Sparefoot\\PitaService\\Controller\\DisputesController::validateBookingIsReviewable<\/a>"],[0,3,"<a href=\"DisputesController.php.html#251\">Sparefoot\\PitaService\\Controller\\DisputesController::updateDisputeRuling<\/a>"],[0,3,"<a href=\"DisputesController.php.html#280\">Sparefoot\\PitaService\\Controller\\DisputesController::updateDisputeReviewRulingReason<\/a>"],[0,3,"<a href=\"DisputesController.php.html#296\">Sparefoot\\PitaService\\Controller\\DisputesController::updateDisputeReviewNotes<\/a>"],[0,2,"<a href=\"DisputesController.php.html#312\">Sparefoot\\PitaService\\Controller\\DisputesController::deleteDisputeReviewNotes<\/a>"],[0,3,"<a href=\"DisputesController.php.html#321\">Sparefoot\\PitaService\\Controller\\DisputesController::updateDisputeReviewAgent<\/a>"],[0,1,"<a href=\"DisputesController.php.html#345\">Sparefoot\\PitaService\\Controller\\DisputesController::_exportOpenDisputes<\/a>"],[0,1,"<a href=\"DisputesController.php.html#358\">Sparefoot\\PitaService\\Controller\\DisputesController::_exportClosedDisputes<\/a>"],[0,3,"<a href=\"DisputesController.php.html#371\">Sparefoot\\PitaService\\Controller\\DisputesController::_createCsv<\/a>"],[0,1,"<a href=\"DisputesController.php.html#387\">Sparefoot\\PitaService\\Controller\\DisputesController::_getCsvHeaders<\/a>"],[0,2,"<a href=\"DisputesController.php.html#430\">Sparefoot\\PitaService\\Controller\\DisputesController::_getCsvLine<\/a>"],[0,2,"<a href=\"DisputesController.php.html#488\">Sparefoot\\PitaService\\Controller\\DisputesController::_cleanCsvLine<\/a>"],[0,1,"<a href=\"DisputesController.php.html#496\">Sparefoot\\PitaService\\Controller\\DisputesController::_getDefaultQuery<\/a>"],[0,1,"<a href=\"DisputesController.php.html#503\">Sparefoot\\PitaService\\Controller\\DisputesController::_processQuery<\/a>"],[0,1,"<a href=\"DisputesController.php.html#512\">Sparefoot\\PitaService\\Controller\\DisputesController::_processOpenDisputesReport<\/a>"],[0,1,"<a href=\"DisputesController.php.html#523\">Sparefoot\\PitaService\\Controller\\DisputesController::_processClosedDisputesReport<\/a>"],[0,1,"<a href=\"EmailController.php.html#16\">Sparefoot\\PitaService\\Controller\\EmailController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"EmailController.php.html#22\">Sparefoot\\PitaService\\Controller\\EmailController::_init<\/a>"],[0,1,"<a href=\"EmailController.php.html#53\">Sparefoot\\PitaService\\Controller\\EmailController::indexAction<\/a>"],[0,1,"<a href=\"EmailController.php.html#74\">Sparefoot\\PitaService\\Controller\\EmailController::ajaxemailsAction<\/a>"],[0,4,"<a href=\"EmailController.php.html#92\">Sparefoot\\PitaService\\Controller\\EmailController::ajaxsendAction<\/a>"],[100,1,"<a href=\"EmailController.php.html#120\">Sparefoot\\PitaService\\Controller\\EmailController::_getGreenArrowAddresses<\/a>"],[0,1,"<a href=\"ErrorController.php.html#23\">Sparefoot\\PitaService\\Controller\\ErrorController::initBeforeControllerAction<\/a>"],[0,5,"<a href=\"ErrorController.php.html#35\">Sparefoot\\PitaService\\Controller\\ErrorController::errorAction<\/a>"],[0,2,"<a href=\"ErrorController.php.html#65\">Sparefoot\\PitaService\\Controller\\ErrorController::hipchatAction<\/a>"],[0,1,"<a href=\"FacilitiesController.php.html#17\">Sparefoot\\PitaService\\Controller\\FacilitiesController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"FacilitiesController.php.html#23\">Sparefoot\\PitaService\\Controller\\FacilitiesController::_init<\/a>"],[0,18,"<a href=\"FacilitiesController.php.html#33\">Sparefoot\\PitaService\\Controller\\FacilitiesController::indexAction<\/a>"],[0,2,"<a href=\"FacilitiesController.php.html#120\">Sparefoot\\PitaService\\Controller\\FacilitiesController::bysourceAction<\/a>"],[0,4,"<a href=\"FacilitiesController.php.html#148\">Sparefoot\\PitaService\\Controller\\FacilitiesController::getqueryAction<\/a>"],[0,6,"<a href=\"FacilitiesController.php.html#186\">Sparefoot\\PitaService\\Controller\\FacilitiesController::loadbyidAction<\/a>"],[0,1,"<a href=\"FacilityController.php.html#33\">Sparefoot\\PitaService\\Controller\\FacilityController::initBeforeControllerAction<\/a>"],[0,24,"<a href=\"FacilityController.php.html#40\">Sparefoot\\PitaService\\Controller\\FacilityController::_init<\/a>"],[0,12,"<a href=\"FacilityController.php.html#79\">Sparefoot\\PitaService\\Controller\\FacilityController::indexAction<\/a>"],[0,4,"<a href=\"FacilityController.php.html#154\">Sparefoot\\PitaService\\Controller\\FacilityController::acquiretwilionumberAction<\/a>"],[0,1,"<a href=\"FacilityController.php.html#200\">Sparefoot\\PitaService\\Controller\\FacilityController::createJsonResponse<\/a>"],[0,4,"<a href=\"FacilityController.php.html#213\">Sparefoot\\PitaService\\Controller\\FacilityController::releasetwilionumberAction<\/a>"],[0,7,"<a href=\"FacilityController.php.html#250\">Sparefoot\\PitaService\\Controller\\FacilityController::_fetchInventoryData<\/a>"],[0,4,"<a href=\"FacilityController.php.html#287\">Sparefoot\\PitaService\\Controller\\FacilityController::jsonunitlistdetailsAction<\/a>"],[0,8,"<a href=\"FacilityController.php.html#339\">Sparefoot\\PitaService\\Controller\\FacilityController::jsonunitdetailsAction<\/a>"],[0,23,"<a href=\"FacilityController.php.html#408\">Sparefoot\\PitaService\\Controller\\FacilityController::_loadFacilities<\/a>"],[0,1,"<a href=\"FacilityController.php.html#555\">Sparefoot\\PitaService\\Controller\\FacilityController::getqueryAction<\/a>"],[0,15,"<a href=\"FacilityController.php.html#573\">Sparefoot\\PitaService\\Controller\\FacilityController::_tableFromReport<\/a>"],[0,7,"<a href=\"FacilityController.php.html#659\">Sparefoot\\PitaService\\Controller\\FacilityController::_initFilter<\/a>"],[0,1,"<a href=\"FacilityController.php.html#707\">Sparefoot\\PitaService\\Controller\\FacilityController::directoryAction<\/a>"],[0,3,"<a href=\"FacilityController.php.html#723\">Sparefoot\\PitaService\\Controller\\FacilityController::activateAction<\/a>"],[0,3,"<a href=\"FacilityController.php.html#752\">Sparefoot\\PitaService\\Controller\\FacilityController::deactivateAction<\/a>"],[0,5,"<a href=\"FacilityController.php.html#781\">Sparefoot\\PitaService\\Controller\\FacilityController::exportdirectoryrecordingsAction<\/a>"],[0,1,"<a href=\"FacilityController.php.html#846\">Sparefoot\\PitaService\\Controller\\FacilityController::getnotesAction<\/a>"],[0,2,"<a href=\"FacilityController.php.html#863\">Sparefoot\\PitaService\\Controller\\FacilityController::changenotesAction<\/a>"],[0,1,"<a href=\"FaqsController.php.html#20\">Sparefoot\\PitaService\\Controller\\FaqsController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"FaqsController.php.html#28\">Sparefoot\\PitaService\\Controller\\FaqsController::_init<\/a>"],[0,1,"<a href=\"FaqsController.php.html#33\">Sparefoot\\PitaService\\Controller\\FaqsController::indexAction<\/a>"],[0,3,"<a href=\"FaqsController.php.html#42\">Sparefoot\\PitaService\\Controller\\FaqsController::getAction<\/a>"],[0,3,"<a href=\"FaqsController.php.html#67\">Sparefoot\\PitaService\\Controller\\FaqsController::saveAction<\/a>"],[0,1,"<a href=\"FederatedController.php.html#18\">Sparefoot\\PitaService\\Controller\\FederatedController::initBeforeControllerAction<\/a>"],[0,2,"<a href=\"FederatedController.php.html#24\">Sparefoot\\PitaService\\Controller\\FederatedController::index<\/a>"],[0,1,"<a href=\"FederatedController.php.html#41\">Sparefoot\\PitaService\\Controller\\FederatedController::awsAction<\/a>"],[0,2,"<a href=\"FeedsController.php.html#13\">Sparefoot\\PitaService\\Controller\\FeedsController::citysearchAction<\/a>"],[0,3,"<a href=\"FeedsController.php.html#54\">Sparefoot\\PitaService\\Controller\\FeedsController::_prepHours<\/a>"],[0,2,"<a href=\"FeedsController.php.html#74\">Sparefoot\\PitaService\\Controller\\FeedsController::_facilityDetailsUrl<\/a>"],[0,1,"<a href=\"FeedsController.php.html#94\">Sparefoot\\PitaService\\Controller\\FeedsController::_prepUrl<\/a>"],[0,4,"<a href=\"FeedsController.php.html#99\">Sparefoot\\PitaService\\Controller\\FeedsController::_urlTitle<\/a>"],[0,49,"<a href=\"FiltersController.php.html#20\">Sparefoot\\PitaService\\Controller\\FiltersController::loadAction<\/a>"],[0,1,"<a href=\"HeatmapController.php.html#17\">Sparefoot\\PitaService\\Controller\\HeatmapController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"HeatmapController.php.html#24\">Sparefoot\\PitaService\\Controller\\HeatmapController::index<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#29\">Sparefoot\\PitaService\\Controller\\IncentivesController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#37\">Sparefoot\\PitaService\\Controller\\IncentivesController::_init<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#48\">Sparefoot\\PitaService\\Controller\\IncentivesController::indexAction<\/a>"],[0,4,"<a href=\"IncentivesController.php.html#56\">Sparefoot\\PitaService\\Controller\\IncentivesController::redemptionRequestsAction<\/a>"],[0,2,"<a href=\"IncentivesController.php.html#90\">Sparefoot\\PitaService\\Controller\\IncentivesController::updateStatusAction<\/a>"],[0,3,"<a href=\"IncentivesController.php.html#103\">Sparefoot\\PitaService\\Controller\\IncentivesController::parseIncentiveResponse<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#118\">Sparefoot\\PitaService\\Controller\\IncentivesController::getRedemptionRequests<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#131\">Sparefoot\\PitaService\\Controller\\IncentivesController::getRedemptionRequest<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#142\">Sparefoot\\PitaService\\Controller\\IncentivesController::getOffer<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#152\">Sparefoot\\PitaService\\Controller\\IncentivesController::getIncentive<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#162\">Sparefoot\\PitaService\\Controller\\IncentivesController::setStatus<\/a>"],[0,5,"<a href=\"IncentivesController.php.html#175\">Sparefoot\\PitaService\\Controller\\IncentivesController::addIncentiveAndBookingDataToView<\/a>"],[0,8,"<a href=\"IncentivesController.php.html#202\">Sparefoot\\PitaService\\Controller\\IncentivesController::searchRequestsAction<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#237\">Sparefoot\\PitaService\\Controller\\IncentivesController::getRedemptionRequestsAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#19\">Sparefoot\\PitaService\\Controller\\IndexController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#26\">Sparefoot\\PitaService\\Controller\\IndexController::_getQuickRepContainer<\/a>"],[0,1,"<a href=\"IndexController.php.html#31\">Sparefoot\\PitaService\\Controller\\IndexController::indexAction<\/a>"],[0,2,"<a href=\"IndexController.php.html#39\">Sparefoot\\PitaService\\Controller\\IndexController::oldIndexAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#66\">Sparefoot\\PitaService\\Controller\\IndexController::fatalerrorAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#76\">Sparefoot\\PitaService\\Controller\\IndexController::exceptionAction<\/a>"],[0,3,"<a href=\"IndexController.php.html#93\">Sparefoot\\PitaService\\Controller\\IndexController::addBookmarkAction<\/a>"],[0,4,"<a href=\"IndexController.php.html#130\">Sparefoot\\PitaService\\Controller\\IndexController::updateBookmarksAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#167\">Sparefoot\\PitaService\\Controller\\IndexController::_loadTimespanBookingData<\/a>"],[0,1,"<a href=\"IndexController.php.html#231\">Sparefoot\\PitaService\\Controller\\IndexController::auditAction<\/a>"],[0,1,"<a href=\"IndexView\/IndexActionView.php.html#12\">Sparefoot\\PitaService\\Controller\\IndexView\\IndexActionView::usortReports<\/a>"],[0,1,"<a href=\"InventoryController.php.html#15\">Sparefoot\\PitaService\\Controller\\InventoryController::__construct<\/a>"],[0,1,"<a href=\"InventoryController.php.html#40\">Sparefoot\\PitaService\\Controller\\InventoryController::_init<\/a>"],[0,1,"<a href=\"InventoryController.php.html#50\">Sparefoot\\PitaService\\Controller\\InventoryController::indexAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#86\">Sparefoot\\PitaService\\Controller\\InventoryController::mainAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#98\">Sparefoot\\PitaService\\Controller\\InventoryController::loadpercentcompleteAction<\/a>"],[0,28,"<a href=\"InventoryController.php.html#129\">Sparefoot\\PitaService\\Controller\\InventoryController::loadfacilitiesAction<\/a>"],[0,5,"<a href=\"InventoryController.php.html#277\">Sparefoot\\PitaService\\Controller\\InventoryController::_getFacilitiesInDeletedAccounts<\/a>"],[0,5,"<a href=\"InventoryController.php.html#320\">Sparefoot\\PitaService\\Controller\\InventoryController::accountlistAction<\/a>"],[0,12,"<a href=\"InventoryController.php.html#354\">Sparefoot\\PitaService\\Controller\\InventoryController::productlistAction<\/a>"],[0,14,"<a href=\"InventoryController.php.html#407\">Sparefoot\\PitaService\\Controller\\InventoryController::toggleproductAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#480\">Sparefoot\\PitaService\\Controller\\InventoryController::togglehostedwebsitetypeAction<\/a>"],[0,7,"<a href=\"InventoryController.php.html#512\">Sparefoot\\PitaService\\Controller\\InventoryController::updateadnetworkpriceAction<\/a>"],[0,7,"<a href=\"InventoryController.php.html#566\">Sparefoot\\PitaService\\Controller\\InventoryController::updatehostedwebsitepriceAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#620\">Sparefoot\\PitaService\\Controller\\InventoryController::updatetenantconnectprecalldigitsAction<\/a>"],[0,18,"<a href=\"InventoryController.php.html#646\">Sparefoot\\PitaService\\Controller\\InventoryController::loadinfoAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#775\">Sparefoot\\PitaService\\Controller\\InventoryController::loadresaleratesAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#816\">Sparefoot\\PitaService\\Controller\\InventoryController::updateresalebucketAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#839\">Sparefoot\\PitaService\\Controller\\InventoryController::deleteresalebucketAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#862\">Sparefoot\\PitaService\\Controller\\InventoryController::addupdateresalebucketAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#894\">Sparefoot\\PitaService\\Controller\\InventoryController::unithtmlAction<\/a>"],[0,8,"<a href=\"InventoryController.php.html#934\">Sparefoot\\PitaService\\Controller\\InventoryController::approveAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#983\">Sparefoot\\PitaService\\Controller\\InventoryController::facilityapproveAction<\/a>"],[0,96,"<a href=\"InventoryController.php.html#1014\">Sparefoot\\PitaService\\Controller\\InventoryController::jsondetailsAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#1371\">Sparefoot\\PitaService\\Controller\\InventoryController::updatecustomclosureAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#1398\">Sparefoot\\PitaService\\Controller\\InventoryController::removecustomclosureAction<\/a>"],[0,76,"<a href=\"InventoryController.php.html#1418\">Sparefoot\\PitaService\\Controller\\InventoryController::updatedetailsAction<\/a>"],[0,37,"<a href=\"InventoryController.php.html#1860\">Sparefoot\\PitaService\\Controller\\InventoryController::multiplefacilityupdateAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2051\">Sparefoot\\PitaService\\Controller\\InventoryController::billableentitylistAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2071\">Sparefoot\\PitaService\\Controller\\InventoryController::_makeBillableEntityList<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2088\">Sparefoot\\PitaService\\Controller\\InventoryController::streetviewAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#2123\">Sparefoot\\PitaService\\Controller\\InventoryController::updatestreetviewAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2161\">Sparefoot\\PitaService\\Controller\\InventoryController::loadlogAction<\/a>"],[0,54,"<a href=\"InventoryController.php.html#2185\">Sparefoot\\PitaService\\Controller\\InventoryController::unitAction<\/a>"],[0,10,"<a href=\"InventoryController.php.html#2475\">Sparefoot\\PitaService\\Controller\\InventoryController::photoframeAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#2536\">Sparefoot\\PitaService\\Controller\\InventoryController::getyieldavgsAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2555\">Sparefoot\\PitaService\\Controller\\InventoryController::loadyieldjsonAction<\/a>"],[0,5,"<a href=\"InventoryController.php.html#2575\">Sparefoot\\PitaService\\Controller\\InventoryController::saveyieldAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#2622\">Sparefoot\\PitaService\\Controller\\InventoryController::visibilityAction<\/a>"],[0,21,"<a href=\"InventoryController.php.html#2639\">Sparefoot\\PitaService\\Controller\\InventoryController::amenitiescheckAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2728\">Sparefoot\\PitaService\\Controller\\InventoryController::visibilitycheckAction<\/a>"],[0,14,"<a href=\"InventoryController.php.html#2791\">Sparefoot\\PitaService\\Controller\\InventoryController::acctfaclistAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2881\">Sparefoot\\PitaService\\Controller\\InventoryController::syncAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2907\">Sparefoot\\PitaService\\Controller\\InventoryController::phidosyncAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#2929\">Sparefoot\\PitaService\\Controller\\InventoryController::refreshsearchAction<\/a>"],[0,8,"<a href=\"InventoryController.php.html#2935\">Sparefoot\\PitaService\\Controller\\InventoryController::_refreshSearch<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2991\">Sparefoot\\PitaService\\Controller\\InventoryController::resetlocationAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3017\">Sparefoot\\PitaService\\Controller\\InventoryController::unitexportAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#3054\">Sparefoot\\PitaService\\Controller\\InventoryController::logexportAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#3084\">Sparefoot\\PitaService\\Controller\\InventoryController::_fetchLogData<\/a>"],[0,7,"<a href=\"InventoryController.php.html#3114\">Sparefoot\\PitaService\\Controller\\InventoryController::_fetchInventoryData<\/a>"],[0,6,"<a href=\"InventoryController.php.html#3151\">Sparefoot\\PitaService\\Controller\\InventoryController::updatefacilityAction<\/a>"],[0,6,"<a href=\"InventoryController.php.html#3189\">Sparefoot\\PitaService\\Controller\\InventoryController::addfacilitycontactAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#3232\">Sparefoot\\PitaService\\Controller\\InventoryController::removefacilitycontactAction<\/a>"],[0,13,"<a href=\"InventoryController.php.html#3264\">Sparefoot\\PitaService\\Controller\\InventoryController::updateinventoryAction<\/a>"],[0,15,"<a href=\"InventoryController.php.html#3338\">Sparefoot\\PitaService\\Controller\\InventoryController::getaccessAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#3421\">Sparefoot\\PitaService\\Controller\\InventoryController::addaccessAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#3449\">Sparefoot\\PitaService\\Controller\\InventoryController::removeaccessAction<\/a>"],[0,6,"<a href=\"InventoryController.php.html#3475\">Sparefoot\\PitaService\\Controller\\InventoryController::movefacilityAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3515\">Sparefoot\\PitaService\\Controller\\InventoryController::getsitelinkfacilitylistAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3547\">Sparefoot\\PitaService\\Controller\\InventoryController::getselfstoragemanagerfacilitylistAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3579\">Sparefoot\\PitaService\\Controller\\InventoryController::getcentershiftfacilitylistAction<\/a>"],[0,6,"<a href=\"InventoryController.php.html#3612\">Sparefoot\\PitaService\\Controller\\InventoryController::syncselectedfacsAction<\/a>"],[0,39,"<a href=\"InventoryController.php.html#3676\">Sparefoot\\PitaService\\Controller\\InventoryController::copyFacilityDataAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#3905\">Sparefoot\\PitaService\\Controller\\InventoryController::loadDuplicateReviews<\/a>"],[0,2,"<a href=\"InventoryController.php.html#3917\">Sparefoot\\PitaService\\Controller\\InventoryController::copyReviewToFacility<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3957\">Sparefoot\\PitaService\\Controller\\InventoryController::integrationsByAccountAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3988\">Sparefoot\\PitaService\\Controller\\InventoryController::facilitiesByIntegrationAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#4018\">Sparefoot\\PitaService\\Controller\\InventoryController::getfacilityAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#4049\">Sparefoot\\PitaService\\Controller\\InventoryController::essfacilitylookupAction<\/a>"],[0,6,"<a href=\"InventoryController.php.html#4071\">Sparefoot\\PitaService\\Controller\\InventoryController::essconvertfrommanualAction<\/a>"],[0,5,"<a href=\"InventoryController.php.html#4121\">Sparefoot\\PitaService\\Controller\\InventoryController::essconverttomanualAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#20\">Sparefoot\\PitaService\\Controller\\JobsController::_init<\/a>"],[0,1,"<a href=\"JobsController.php.html#32\">Sparefoot\\PitaService\\Controller\\JobsController::commandAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#50\">Sparefoot\\PitaService\\Controller\\JobsController::commandAddAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#74\">Sparefoot\\PitaService\\Controller\\JobsController::commandLogAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#102\">Sparefoot\\PitaService\\Controller\\JobsController::commandEditAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#127\">Sparefoot\\PitaService\\Controller\\JobsController::commandSaveAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#154\">Sparefoot\\PitaService\\Controller\\JobsController::commandDeleteAction<\/a>"],[0,5,"<a href=\"JobsController.php.html#178\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#238\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledEditAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#266\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledAddAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#297\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledDeleteAction<\/a>"],[0,4,"<a href=\"JobsController.php.html#317\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledSaveAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#347\">Sparefoot\\PitaService\\Controller\\JobsController::adhocChooseAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#364\">Sparefoot\\PitaService\\Controller\\JobsController::adhocLogAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#390\">Sparefoot\\PitaService\\Controller\\JobsController::adhocAddAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#418\">Sparefoot\\PitaService\\Controller\\JobsController::adhocDeleteAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#438\">Sparefoot\\PitaService\\Controller\\JobsController::summaryAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#513\">Sparefoot\\PitaService\\Controller\\JobsController::unlockAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#533\">Sparefoot\\PitaService\\Controller\\JobsController::workerZombieAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#586\">Sparefoot\\PitaService\\Controller\\JobsController::workerLogAction<\/a>"],[0,5,"<a href=\"JobsController.php.html#612\">Sparefoot\\PitaService\\Controller\\JobsController::workerInfoAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#651\">Sparefoot\\PitaService\\Controller\\JobsController::childAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#679\">Sparefoot\\PitaService\\Controller\\JobsController::childEditAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#703\">Sparefoot\\PitaService\\Controller\\JobsController::childAddAction<\/a>"],[0,4,"<a href=\"JobsController.php.html#724\">Sparefoot\\PitaService\\Controller\\JobsController::childUpdateAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#753\">Sparefoot\\PitaService\\Controller\\JobsController::childSaveAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#778\">Sparefoot\\PitaService\\Controller\\JobsController::childDeleteAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#794\">Sparefoot\\PitaService\\Controller\\JobsController::colDisplay<\/a>"],[0,4,"<a href=\"JobsController.php.html#799\">Sparefoot\\PitaService\\Controller\\JobsController::finishCode<\/a>"],[0,4,"<a href=\"JobsController.php.html#813\">Sparefoot\\PitaService\\Controller\\JobsController::successCode<\/a>"],[0,8,"<a href=\"JobsController.php.html#828\">Sparefoot\\PitaService\\Controller\\JobsController::timeDisplay<\/a>"],[0,2,"<a href=\"JobsController.php.html#859\">Sparefoot\\PitaService\\Controller\\JobsController::dateDisplay<\/a>"],[0,4,"<a href=\"JobsController.php.html#866\">Sparefoot\\PitaService\\Controller\\JobsController::_verifyChildAllowed<\/a>"],[0,7,"<a href=\"LoginController.php.html#16\">Sparefoot\\PitaService\\Controller\\LoginController::index<\/a>"],[0,1,"<a href=\"LoginController.php.html#57\">Sparefoot\\PitaService\\Controller\\LoginController::check<\/a>"],[0,2,"<a href=\"LoginController.php.html#66\">Sparefoot\\PitaService\\Controller\\LoginController::getCurrentUser<\/a>"],[0,1,"<a href=\"LoginController.php.html#84\">Sparefoot\\PitaService\\Controller\\LoginController::logout<\/a>"],[0,1,"<a href=\"LoginController.php.html#91\">Sparefoot\\PitaService\\Controller\\LoginController::amILoggedIn<\/a>"],[0,1,"<a href=\"LoginController.php.html#97\">Sparefoot\\PitaService\\Controller\\LoginController::isUserGod<\/a>"],[0,1,"<a href=\"MaintenanceModeController.php.html#15\">Sparefoot\\PitaService\\Controller\\MaintenanceModeController::indexAction<\/a>"],[0,1,"<a href=\"MaintenanceModeController.php.html#61\">Sparefoot\\PitaService\\Controller\\MaintenanceModeController::setFlagAction<\/a>"],[0,4,"<a href=\"MaintenanceModeController.php.html#94\">Sparefoot\\PitaService\\Controller\\MaintenanceModeController::_sendMaintenanceModeEmail<\/a>"],[0,6,"<a href=\"NetsuiteController.php.html#18\">Sparefoot\\PitaService\\Controller\\NetsuiteController::accountAction<\/a>"],[0,4,"<a href=\"NetsuiteController.php.html#72\">Sparefoot\\PitaService\\Controller\\NetsuiteController::statusAction<\/a>"],[0,1,"<a href=\"OmnomController.php.html#11\">Sparefoot\\PitaService\\Controller\\OmnomController::_init<\/a>"],[0,3,"<a href=\"OmnomController.php.html#21\">Sparefoot\\PitaService\\Controller\\OmnomController::indexAction<\/a>"],[0,3,"<a href=\"OmnomController.php.html#51\">Sparefoot\\PitaService\\Controller\\OmnomController::_queryStatusByJob<\/a>"],[0,5,"<a href=\"OmnomController.php.html#98\">Sparefoot\\PitaService\\Controller\\OmnomController::_queryStatusByIntegration<\/a>"],[0,5,"<a href=\"OmnomController.php.html#157\">Sparefoot\\PitaService\\Controller\\OmnomController::_queryStatusByFacility<\/a>"],[0,2,"<a href=\"OmnomController.php.html#210\">Sparefoot\\PitaService\\Controller\\OmnomController::_getJobs<\/a>"],[0,2,"<a href=\"OmnomController.php.html#230\">Sparefoot\\PitaService\\Controller\\OmnomController::_getIntegrations<\/a>"],[0,2,"<a href=\"OmnomController.php.html#250\">Sparefoot\\PitaService\\Controller\\OmnomController::_getAccounts<\/a>"],[0,1,"<a href=\"PaidmediaController.php.html#12\">Sparefoot\\PitaService\\Controller\\PaidmediaController::__construct<\/a>"],[0,1,"<a href=\"PaidmediaController.php.html#22\">Sparefoot\\PitaService\\Controller\\PaidmediaController::indexAction<\/a>"],[0,1,"<a href=\"PaidmediaController.php.html#32\">Sparefoot\\PitaService\\Controller\\PaidmediaController::paidmediaAction<\/a>"],[0,1,"<a href=\"PaidmediaController.php.html#48\">Sparefoot\\PitaService\\Controller\\PaidmediaController::managepaidmediaAction<\/a>"],[0,5,"<a href=\"PaidmediaController.php.html#62\">Sparefoot\\PitaService\\Controller\\PaidmediaController::modifypaidmediaAction<\/a>"],[0,1,"<a href=\"PaidmediaController.php.html#95\">Sparefoot\\PitaService\\Controller\\PaidmediaController::ajaxcreatepaidmediaupdateAction<\/a>"],[0,5,"<a href=\"PaidmediaController.php.html#110\">Sparefoot\\PitaService\\Controller\\PaidmediaController::getRuleengineList<\/a>"],[0,3,"<a href=\"PaidmediaController.php.html#135\">Sparefoot\\PitaService\\Controller\\PaidmediaController::getCampaignList<\/a>"],[0,1,"<a href=\"PayoutController.php.html#14\">Sparefoot\\PitaService\\Controller\\PayoutController::__construct<\/a>"],[0,1,"<a href=\"PayoutController.php.html#24\">Sparefoot\\PitaService\\Controller\\PayoutController::indexAction<\/a>"],[0,1,"<a href=\"PayoutController.php.html#34\">Sparefoot\\PitaService\\Controller\\PayoutController::listAction<\/a>"],[0,2,"<a href=\"PayoutController.php.html#47\">Sparefoot\\PitaService\\Controller\\PayoutController::previewAction<\/a>"],[0,1,"<a href=\"PayoutController.php.html#65\">Sparefoot\\PitaService\\Controller\\PayoutController::detailsAction<\/a>"],[0,1,"<a href=\"PayoutController.php.html#83\">Sparefoot\\PitaService\\Controller\\PayoutController::createAction<\/a>"],[0,1,"<a href=\"PayoutController.php.html#105\">Sparefoot\\PitaService\\Controller\\PayoutController::pdfAction<\/a>"],[0,2,"<a href=\"PayoutController.php.html#123\">Sparefoot\\PitaService\\Controller\\PayoutController::getnotesAction<\/a>"],[0,3,"<a href=\"PayoutController.php.html#153\">Sparefoot\\PitaService\\Controller\\PayoutController::changenotesAction<\/a>"],[0,2,"<a href=\"PayoutController.php.html#181\">Sparefoot\\PitaService\\Controller\\PayoutController::_assignBookings<\/a>"],[0,1,"<a href=\"PayoutController.php.html#199\">Sparefoot\\PitaService\\Controller\\PayoutController::_preparePayoutQuery<\/a>"],[0,1,"<a href=\"PayoutController.php.html#211\">Sparefoot\\PitaService\\Controller\\PayoutController::_prepareDetails<\/a>"],[0,1,"<a href=\"PhotosController.php.html#12\">Sparefoot\\PitaService\\Controller\\PhotosController::__construct<\/a>"],[0,6,"<a href=\"PhotosController.php.html#22\">Sparefoot\\PitaService\\Controller\\PhotosController::indexAction<\/a>"],[0,3,"<a href=\"PhotosController.php.html#67\">Sparefoot\\PitaService\\Controller\\PhotosController::approveAction<\/a>"],[0,1,"<a href=\"PingController.php.html#14\">Sparefoot\\PitaService\\Controller\\PingController::__construct<\/a>"],[0,3,"<a href=\"PingController.php.html#24\">Sparefoot\\PitaService\\Controller\\PingController::index<\/a>"],[0,1,"<a href=\"PitaSearchController.php.html#20\">Sparefoot\\PitaService\\Controller\\PitaSearchController::initBeforeControllerAction<\/a>"],[0,8,"<a href=\"PitaSearchController.php.html#31\">Sparefoot\\PitaService\\Controller\\PitaSearchController::indexAction<\/a>"],[0,8,"<a href=\"PitaSearchController.php.html#83\">Sparefoot\\PitaService\\Controller\\PitaSearchController::queryAction<\/a>"],[0,5,"<a href=\"ProxyController.php.html#26\">Sparefoot\\PitaService\\Controller\\ProxyController::loadAction<\/a>"],[0,5,"<a href=\"ProxyController.php.html#68\">Sparefoot\\PitaService\\Controller\\ProxyController::AuthenticateProxyAuthorization<\/a>"],[0,7,"<a href=\"ProxyController.php.html#89\">Sparefoot\\PitaService\\Controller\\ProxyController::proxyRequest<\/a>"],[0,1,"<a href=\"ProxyController.php.html#132\">Sparefoot\\PitaService\\Controller\\ProxyController::infoAction<\/a>"],[0,2,"<a href=\"ProxyController.php.html#148\">Sparefoot\\PitaService\\Controller\\ProxyController::debugAction<\/a>"],[0,3,"<a href=\"PublicController.php.html#23\">Sparefoot\\PitaService\\Controller\\PublicController::searchServicesAction<\/a>"],[0,5,"<a href=\"PublicController.php.html#58\">Sparefoot\\PitaService\\Controller\\PublicController::bookUnitAction<\/a>"],[0,2,"<a href=\"PublicController.php.html#122\">Sparefoot\\PitaService\\Controller\\PublicController::searchAction<\/a>"],[0,8,"<a href=\"PublicController.php.html#147\">Sparefoot\\PitaService\\Controller\\PublicController::hourlyAlertsAction<\/a>"],[0,1,"<a href=\"QuickClientController.php.html#13\">Sparefoot\\PitaService\\Controller\\QuickClientController::__construct<\/a>"],[0,1,"<a href=\"QuickClientController.php.html#23\">Sparefoot\\PitaService\\Controller\\QuickClientController::indexAction<\/a>"],[0,1,"<a href=\"QuickClientController.php.html#33\">Sparefoot\\PitaService\\Controller\\QuickClientController::listAction<\/a>"],[0,3,"<a href=\"QuickClientController.php.html#48\">Sparefoot\\PitaService\\Controller\\QuickClientController::renderAction<\/a>"],[0,1,"<a href=\"QuickClientController.php.html#83\">Sparefoot\\PitaService\\Controller\\QuickClientController::paramsAction<\/a>"],[0,1,"<a href=\"QuickClientController.php.html#98\">Sparefoot\\PitaService\\Controller\\QuickClientController::facilityAction<\/a>"],[0,4,"<a href=\"QuickClientController.php.html#114\">Sparefoot\\PitaService\\Controller\\QuickClientController::facilitiesAction<\/a>"],[0,1,"<a href=\"QuickClientController.php.html#140\">Sparefoot\\PitaService\\Controller\\QuickClientController::_facSort<\/a>"],[0,3,"<a href=\"QuickClientController.php.html#149\">Sparefoot\\PitaService\\Controller\\QuickClientController::unitsAction<\/a>"],[0,1,"<a href=\"QuickClientController.php.html#176\">Sparefoot\\PitaService\\Controller\\QuickClientController::unitAction<\/a>"],[0,11,"<a href=\"QuickClientController.php.html#191\">Sparefoot\\PitaService\\Controller\\QuickClientController::executeAction<\/a>"],[0,5,"<a href=\"QuickClientController.php.html#250\">Sparefoot\\PitaService\\Controller\\QuickClientController::toArray<\/a>"],[0,1,"<a href=\"QuickformController.php.html#10\">Sparefoot\\PitaService\\Controller\\QuickformController::__construct<\/a>"],[0,1,"<a href=\"QuickformController.php.html#20\">Sparefoot\\PitaService\\Controller\\QuickformController::index<\/a>"],[0,1,"<a href=\"QuickformController.php.html#30\">Sparefoot\\PitaService\\Controller\\QuickformController::listAction<\/a>"],[0,1,"<a href=\"QuickformController.php.html#42\">Sparefoot\\PitaService\\Controller\\QuickformController::renderAction<\/a>"],[0,1,"<a href=\"QuickjobController.php.html#12\">Sparefoot\\PitaService\\Controller\\QuickjobController::__construct<\/a>"],[0,1,"<a href=\"QuickjobController.php.html#22\">Sparefoot\\PitaService\\Controller\\QuickjobController::index<\/a>"],[0,1,"<a href=\"QuickjobController.php.html#32\">Sparefoot\\PitaService\\Controller\\QuickjobController::renderAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#19\">Sparefoot\\PitaService\\Controller\\QuickrepController::__construct<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#24\">Sparefoot\\PitaService\\Controller\\QuickrepController::_init<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#35\">Sparefoot\\PitaService\\Controller\\QuickrepController::indexAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#47\">Sparefoot\\PitaService\\Controller\\QuickrepController::listAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#59\">Sparefoot\\PitaService\\Controller\\QuickrepController::renderdashAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#71\">Sparefoot\\PitaService\\Controller\\QuickrepController::renderAction<\/a>"],[0,2,"<a href=\"QuickrepController.php.html#83\">Sparefoot\\PitaService\\Controller\\QuickrepController::updateinputsAction<\/a>"],[0,3,"<a href=\"QuickrepController.php.html#108\">Sparefoot\\PitaService\\Controller\\QuickrepController::tableAction<\/a>"],[0,4,"<a href=\"QuickrepController.php.html#135\">Sparefoot\\PitaService\\Controller\\QuickrepController::chartAction<\/a>"],[0,2,"<a href=\"QuickrepController.php.html#165\">Sparefoot\\PitaService\\Controller\\QuickrepController::sqlAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#185\">Sparefoot\\PitaService\\Controller\\QuickrepController::exportAction<\/a>"],[0,7,"<a href=\"QuickrepController.php.html#206\">Sparefoot\\PitaService\\Controller\\QuickrepController::hourlyAlertsAction<\/a>"],[0,2,"<a href=\"QuicksoapController.php.html#22\">Sparefoot\\PitaService\\Controller\\QuicksoapController::indexAction<\/a>"],[0,2,"<a href=\"QuicksoapController.php.html#34\">Sparefoot\\PitaService\\Controller\\QuicksoapController::wsdlAction<\/a>"],[0,1,"<a href=\"QuicksoapController.php.html#50\">Sparefoot\\PitaService\\Controller\\QuicksoapController::handleSoapRequest<\/a>"],[0,2,"<a href=\"QuicksoapController.php.html#77\">Sparefoot\\PitaService\\Controller\\QuicksoapController::generateWsdlResponse<\/a>"],[0,1,"<a href=\"QuicktaggerController.php.html#15\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::__construct<\/a>"],[0,1,"<a href=\"QuicktaggerController.php.html#56\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::indexAction<\/a>"],[0,1,"<a href=\"QuicktaggerController.php.html#69\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::integrationAction<\/a>"],[0,2,"<a href=\"QuicktaggerController.php.html#91\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::saveorderAction<\/a>"],[0,3,"<a href=\"QuicktaggerController.php.html#112\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::savenewAction<\/a>"],[0,7,"<a href=\"QuicktaggerController.php.html#145\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::saveexistingAction<\/a>"],[0,2,"<a href=\"QuicktaggerController.php.html#191\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::removeAction<\/a>"],[0,1,"<a href=\"ReferralsController.php.html#11\">Sparefoot\\PitaService\\Controller\\ReferralsController::__construct<\/a>"],[0,1,"<a href=\"ReferralsController.php.html#21\">Sparefoot\\PitaService\\Controller\\ReferralsController::indexAction<\/a>"],[0,2,"<a href=\"ReferralsController.php.html#41\">Sparefoot\\PitaService\\Controller\\ReferralsController::toggleconvertedAction<\/a>"],[0,2,"<a href=\"ReferralsController.php.html#60\">Sparefoot\\PitaService\\Controller\\ReferralsController::toggleclaimedAction<\/a>"],[0,1,"<a href=\"ReferralsController.php.html#79\">Sparefoot\\PitaService\\Controller\\ReferralsController::savenotesAction<\/a>"],[0,1,"<a href=\"ReportsController.php.html#23\">Sparefoot\\PitaService\\Controller\\ReportsController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"ReportsController.php.html#30\">Sparefoot\\PitaService\\Controller\\ReportsController::_init<\/a>"],[0,2,"<a href=\"ReportsController.php.html#39\">Sparefoot\\PitaService\\Controller\\ReportsController::indexAction<\/a>"],[0,7,"<a href=\"ReportsController.php.html#63\">Sparefoot\\PitaService\\Controller\\ReportsController::renderAction<\/a>"],[0,1,"<a href=\"ReportsController.php.html#113\">Sparefoot\\PitaService\\Controller\\ReportsController::updateInputsAction<\/a>"],[0,3,"<a href=\"ReportsController.php.html#138\">Sparefoot\\PitaService\\Controller\\ReportsController::tableAction<\/a>"],[0,2,"<a href=\"ReportsController.php.html#166\">Sparefoot\\PitaService\\Controller\\ReportsController::sqlAction<\/a>"],[0,1,"<a href=\"ReportsController.php.html#186\">Sparefoot\\PitaService\\Controller\\ReportsController::exportAction<\/a>"],[0,6,"<a href=\"ReviewsController.php.html#16\">Sparefoot\\PitaService\\Controller\\ReviewsController::indexAction<\/a>"],[0,1,"<a href=\"ReviewsController.php.html#62\">Sparefoot\\PitaService\\Controller\\ReviewsController::getqueryAction<\/a>"],[0,4,"<a href=\"ReviewsController.php.html#80\">Sparefoot\\PitaService\\Controller\\ReviewsController::deletereviewAction<\/a>"],[0,2,"<a href=\"ReviewsController.php.html#104\">Sparefoot\\PitaService\\Controller\\ReviewsController::getreviewAction<\/a>"],[0,5,"<a href=\"ReviewsController.php.html#134\">Sparefoot\\PitaService\\Controller\\ReviewsController::savereviewAction<\/a>"],[0,1,"<a href=\"ReviewsController.php.html#178\">Sparefoot\\PitaService\\Controller\\ReviewsController::approvalsAction<\/a>"],[0,3,"<a href=\"ReviewsController.php.html#193\">Sparefoot\\PitaService\\Controller\\ReviewsController::setStatusAction<\/a>"],[0,2,"<a href=\"ReviewsController.php.html#224\">Sparefoot\\PitaService\\Controller\\ReviewsController::editReviewMessageAction<\/a>"],[0,1,"<a href=\"RewardsController.php.html#15\">Sparefoot\\PitaService\\Controller\\RewardsController::indexAction<\/a>"],[0,5,"<a href=\"RewardsController.php.html#27\">Sparefoot\\PitaService\\Controller\\RewardsController::newkiindAction<\/a>"],[0,1,"<a href=\"RewardsController.php.html#72\">Sparefoot\\PitaService\\Controller\\RewardsController::reviewkiindAction<\/a>"],[0,2,"<a href=\"RewardsController.php.html#84\">Sparefoot\\PitaService\\Controller\\RewardsController::browsekiindAction<\/a>"],[0,1,"<a href=\"RewardsController.php.html#101\">Sparefoot\\PitaService\\Controller\\RewardsController::listAction<\/a>"],[0,1,"<a href=\"SalesController.php.html#17\">Sparefoot\\PitaService\\Controller\\SalesController::indexAction<\/a>"],[0,2,"<a href=\"SalesController.php.html#28\">Sparefoot\\PitaService\\Controller\\SalesController::searchAction<\/a>"],[0,1,"<a href=\"SalesController.php.html#44\">Sparefoot\\PitaService\\Controller\\SalesController::_queryData<\/a>"],[0,1,"<a href=\"SalesController.php.html#57\">Sparefoot\\PitaService\\Controller\\SalesController::_getSearchDataByZip<\/a>"],[0,1,"<a href=\"SalesController.php.html#120\">Sparefoot\\PitaService\\Controller\\SalesController::_getSearchData<\/a>"],[0,1,"<a href=\"SalesController.php.html#138\">Sparefoot\\PitaService\\Controller\\SalesController::_getClickData<\/a>"],[0,1,"<a href=\"SalesController.php.html#160\">Sparefoot\\PitaService\\Controller\\SalesController::_getReservationData<\/a>"],[0,31,"<a href=\"SalesforceController.php.html#19\">Sparefoot\\PitaService\\Controller\\SalesforceController::addAccountAction<\/a>"],[0,8,"<a href=\"SalesforceController.php.html#196\">Sparefoot\\PitaService\\Controller\\SalesforceController::_createManualFacilities<\/a>"],[0,1,"<a href=\"SearchController.php.html#15\">Sparefoot\\PitaService\\Controller\\SearchController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"SearchController.php.html#27\">Sparefoot\\PitaService\\Controller\\SearchController::indexAction<\/a>"],[0,13,"<a href=\"SearchController.php.html#37\">Sparefoot\\PitaService\\Controller\\SearchController::testAction<\/a>"],[0,6,"<a href=\"SearchController.php.html#143\">Sparefoot\\PitaService\\Controller\\SearchController::distanceAction<\/a>"],[0,1,"<a href=\"SearchController.php.html#204\">Sparefoot\\PitaService\\Controller\\SearchController::_initWeights<\/a>"],[0,2,"<a href=\"SearchController.php.html#224\">Sparefoot\\PitaService\\Controller\\SearchController::_conductSearch<\/a>"],[0,1,"<a href=\"ServiceareaController.php.html#16\">Sparefoot\\PitaService\\Controller\\ServiceareaController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"ServiceareaController.php.html#27\">Sparefoot\\PitaService\\Controller\\ServiceareaController::indexAction<\/a>"],[0,3,"<a href=\"ServiceareaController.php.html#55\">Sparefoot\\PitaService\\Controller\\ServiceareaController::getAction<\/a>"],[0,2,"<a href=\"ServiceareaController.php.html#87\">Sparefoot\\PitaService\\Controller\\ServiceareaController::saveAction<\/a>"],[0,2,"<a href=\"ServiceareaController.php.html#115\">Sparefoot\\PitaService\\Controller\\ServiceareaController::convexhullAction<\/a>"],[0,2,"<a href=\"ServiceareaController.php.html#142\">Sparefoot\\PitaService\\Controller\\ServiceareaController::simplifyAction<\/a>"],[0,2,"<a href=\"ServiceareaController.php.html#167\">Sparefoot\\PitaService\\Controller\\ServiceareaController::getServiceAreaWktByFacilityId<\/a>"],[0,1,"<a href=\"ServiceareaController.php.html#177\">Sparefoot\\PitaService\\Controller\\ServiceareaController::postLocationService<\/a>"],[0,1,"<a href=\"SoftwarepartnerController.php.html#15\">Sparefoot\\PitaService\\Controller\\SoftwarepartnerController::initBeforeControllerAction<\/a>"],[0,6,"<a href=\"SoftwarepartnerController.php.html#25\">Sparefoot\\PitaService\\Controller\\SoftwarepartnerController::indexAction<\/a>"],[0,1,"<a href=\"SphinxsearchController.php.html#15\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"SphinxsearchController.php.html#29\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::indexAction<\/a>"],[0,2,"<a href=\"SphinxsearchController.php.html#39\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::testAction<\/a>"],[0,1,"<a href=\"SphinxsearchController.php.html#62\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::_initWeights<\/a>"],[0,3,"<a href=\"SphinxsearchController.php.html#82\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::_conductSearch<\/a>"],[0,6,"<a href=\"SphinxsearchController.php.html#127\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::distanceAction<\/a>"],[0,1,"<a href=\"StatementsController.php.html#21\">Sparefoot\\PitaService\\Controller\\StatementsController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"StatementsController.php.html#35\">Sparefoot\\PitaService\\Controller\\StatementsController::indexAction<\/a>"],[0,2,"<a href=\"StatementsController.php.html#52\">Sparefoot\\PitaService\\Controller\\StatementsController::createAction<\/a>"],[0,4,"<a href=\"StatementsController.php.html#85\">Sparefoot\\PitaService\\Controller\\StatementsController::generateAction<\/a>"],[0,2,"<a href=\"StatementsController.php.html#124\">Sparefoot\\PitaService\\Controller\\StatementsController::_export<\/a>"],[0,13,"<a href=\"StatementsController.php.html#157\">Sparefoot\\PitaService\\Controller\\StatementsController::_exportInvoice<\/a>"],[0,1,"<a href=\"StatementsController.php.html#288\">Sparefoot\\PitaService\\Controller\\StatementsController::_processReport<\/a>"],[0,3,"<a href=\"StatementsController.php.html#308\">Sparefoot\\PitaService\\Controller\\StatementsController::cancelInvoiceAction<\/a>"],[0,6,"<a href=\"StatementsController.php.html#332\">Sparefoot\\PitaService\\Controller\\StatementsController::invoiceAction<\/a>"],[0,3,"<a href=\"StatementsController.php.html#397\">Sparefoot\\PitaService\\Controller\\StatementsController::pdfAction<\/a>"],[0,6,"<a href=\"StatementsController.php.html#420\">Sparefoot\\PitaService\\Controller\\StatementsController::emailpreviewAction<\/a>"],[0,6,"<a href=\"StatementsController.php.html#465\">Sparefoot\\PitaService\\Controller\\StatementsController::getinvoiceAction<\/a>"],[0,2,"<a href=\"TableauController.php.html#21\">Sparefoot\\PitaService\\Controller\\TableauController::setErrorMessage<\/a>"],[0,2,"<a href=\"TableauController.php.html#29\">Sparefoot\\PitaService\\Controller\\TableauController::setSuccessMessage<\/a>"],[0,3,"<a href=\"TableauController.php.html#37\">Sparefoot\\PitaService\\Controller\\TableauController::initView<\/a>"],[0,1,"<a href=\"TableauController.php.html#59\">Sparefoot\\PitaService\\Controller\\TableauController::initBeforeControllerAction<\/a>"],[0,4,"<a href=\"TableauController.php.html#74\">Sparefoot\\PitaService\\Controller\\TableauController::indexAction<\/a>"],[0,1,"<a href=\"TableauController.php.html#105\">Sparefoot\\PitaService\\Controller\\TableauController::addAction<\/a>"],[0,3,"<a href=\"TableauController.php.html#118\">Sparefoot\\PitaService\\Controller\\TableauController::editAction<\/a>"],[0,7,"<a href=\"TableauController.php.html#140\">Sparefoot\\PitaService\\Controller\\TableauController::saveAction<\/a>"],[0,3,"<a href=\"TableauController.php.html#175\">Sparefoot\\PitaService\\Controller\\TableauController::deleteAction<\/a>"],[0,1,"<a href=\"TableauController.php.html#198\">Sparefoot\\PitaService\\Controller\\TableauController::manageKeysAction<\/a>"],[0,3,"<a href=\"TableauController.php.html#211\">Sparefoot\\PitaService\\Controller\\TableauController::editKeyAction<\/a>"],[0,5,"<a href=\"TableauController.php.html#233\">Sparefoot\\PitaService\\Controller\\TableauController::saveKeyAction<\/a>"],[0,4,"<a href=\"TableauController.php.html#270\">Sparefoot\\PitaService\\Controller\\TableauController::deleteKeyAction<\/a>"],[0,3,"<a href=\"TableauController.php.html#297\">Sparefoot\\PitaService\\Controller\\TableauController::massEditKeyAction<\/a>"],[0,11,"<a href=\"TableauController.php.html#322\">Sparefoot\\PitaService\\Controller\\TableauController::saveMassEditAction<\/a>"],[0,1,"<a href=\"TableauController.php.html#371\">Sparefoot\\PitaService\\Controller\\TableauController::manageTeamsAction<\/a>"],[0,2,"<a href=\"TableauController.php.html#384\">Sparefoot\\PitaService\\Controller\\TableauController::addTeamAction<\/a>"],[0,1,"<a href=\"TestController.php.html#16\">Sparefoot\\PitaService\\Controller\\TestController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"TestController.php.html#28\">Sparefoot\\PitaService\\Controller\\TestController::indexAction<\/a>"],[0,2,"<a href=\"TestController.php.html#38\">Sparefoot\\PitaService\\Controller\\TestController::testsAction<\/a>"],[0,3,"<a href=\"TestController.php.html#63\">Sparefoot\\PitaService\\Controller\\TestController::manageAction<\/a>"],[0,3,"<a href=\"TestController.php.html#91\">Sparefoot\\PitaService\\Controller\\TestController::manageuiAction<\/a>"],[0,1,"<a href=\"TestController.php.html#119\">Sparefoot\\PitaService\\Controller\\TestController::stopAction<\/a>"],[0,1,"<a href=\"TestController.php.html#131\">Sparefoot\\PitaService\\Controller\\TestController::stopuiAction<\/a>"],[0,6,"<a href=\"TestController.php.html#145\">Sparefoot\\PitaService\\Controller\\TestController::modifytestAction<\/a>"],[0,2,"<a href=\"TestController.php.html#180\">Sparefoot\\PitaService\\Controller\\TestController::ajaxcreatesearchAction<\/a>"],[0,2,"<a href=\"TestController.php.html#206\">Sparefoot\\PitaService\\Controller\\TestController::ajaxcreateuiAction<\/a>"],[0,1,"<a href=\"TestController.php.html#229\">Sparefoot\\PitaService\\Controller\\TestController::_saveWeight<\/a>"],[0,4,"<a href=\"TestController.php.html#243\">Sparefoot\\PitaService\\Controller\\TestController::_saveTestVariation<\/a>"],[0,1,"<a href=\"ToolsController.php.html#16\">Sparefoot\\PitaService\\Controller\\ToolsController::__construct<\/a>"],[0,2,"<a href=\"ToolsController.php.html#26\">Sparefoot\\PitaService\\Controller\\ToolsController::indexAction<\/a>"],[0,1,"<a href=\"ToolsController.php.html#48\">Sparefoot\\PitaService\\Controller\\ToolsController::urlBuilderAction<\/a>"],[0,2,"<a href=\"ToolsController.php.html#60\">Sparefoot\\PitaService\\Controller\\ToolsController::perfectPhotosSelectorAction<\/a>"],[0,2,"<a href=\"ToolsController.php.html#81\">Sparefoot\\PitaService\\Controller\\ToolsController::perfectPhotosSelectorPostAction<\/a>"],[0,15,"<a href=\"ToolsController.php.html#114\">Sparefoot\\PitaService\\Controller\\ToolsController::noEmailListAction<\/a>"],[0,16,"<a href=\"ToolsController.php.html#214\">Sparefoot\\PitaService\\Controller\\ToolsController::searchKeyChangerAction<\/a>"],[0,11,"<a href=\"ToolsController.php.html#341\">Sparefoot\\PitaService\\Controller\\ToolsController::twilioReportsAction<\/a>"],[0,11,"<a href=\"ToolsController.php.html#450\">Sparefoot\\PitaService\\Controller\\ToolsController::createTwilioNumberAction<\/a>"],[0,5,"<a href=\"ToolsController.php.html#520\">Sparefoot\\PitaService\\Controller\\ToolsController::purchaseTwilioNumbersAction<\/a>"],[0,9,"<a href=\"ToolsController.php.html#588\">Sparefoot\\PitaService\\Controller\\ToolsController::siteContentEditorAction<\/a>"],[0,12,"<a href=\"ToolsController.php.html#681\">Sparefoot\\PitaService\\Controller\\ToolsController::siteContentCreatorAction<\/a>"],[0,7,"<a href=\"ToolsController.php.html#812\">Sparefoot\\PitaService\\Controller\\ToolsController::siteContentImportAction<\/a>"],[0,13,"<a href=\"ToolsController.php.html#901\">Sparefoot\\PitaService\\Controller\\ToolsController::siteContentCreatorImportAction<\/a>"],[0,13,"<a href=\"ToolsController.php.html#1033\">Sparefoot\\PitaService\\Controller\\ToolsController::accountingMapAction<\/a>"],[0,3,"<a href=\"ToolsController.php.html#1138\">Sparefoot\\PitaService\\Controller\\ToolsController::cabinetAction<\/a>"],[0,8,"<a href=\"ToolsController.php.html#1166\">Sparefoot\\PitaService\\Controller\\ToolsController::featureFlagsAction<\/a>"],[0,10,"<a href=\"ToolsController.php.html#1208\">Sparefoot\\PitaService\\Controller\\ToolsController::handleFeatureFlagSave<\/a>"],[0,2,"<a href=\"ToolsController.php.html#1254\">Sparefoot\\PitaService\\Controller\\ToolsController::addFeatureFlagAction<\/a>"],[0,2,"<a href=\"ToolsController.php.html#1272\">Sparefoot\\PitaService\\Controller\\ToolsController::deleteFeatureFlagAction<\/a>"],[0,3,"<a href=\"ToolsController.php.html#1290\">Sparefoot\\PitaService\\Controller\\ToolsController::pressPageAction<\/a>"],[0,3,"<a href=\"ToolsController.php.html#1317\">Sparefoot\\PitaService\\Controller\\ToolsController::pressPageEditorAction<\/a>"],[0,1,"<a href=\"ToolsController.php.html#1346\">Sparefoot\\PitaService\\Controller\\ToolsController::platformSearchAction<\/a>"],[0,16,"<a href=\"ToolsController.php.html#1356\">Sparefoot\\PitaService\\Controller\\ToolsController::platformQueryAction<\/a>"],[0,1,"<a href=\"TriController.php.html#17\">Sparefoot\\PitaService\\Controller\\TriController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"TriController.php.html#27\">Sparefoot\\PitaService\\Controller\\TriController::index<\/a>"],[0,1,"<a href=\"TriController.php.html#37\">Sparefoot\\PitaService\\Controller\\TriController::fortuneAction<\/a>"],[0,1,"<a href=\"UserController.php.html#17\">Sparefoot\\PitaService\\Controller\\UserController::__construct<\/a>"],[0,16,"<a href=\"UserController.php.html#27\">Sparefoot\\PitaService\\Controller\\UserController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"UserController.php.html#65\">Sparefoot\\PitaService\\Controller\\UserController::indexAction<\/a>"],[0,8,"<a href=\"UserController.php.html#73\">Sparefoot\\PitaService\\Controller\\UserController::_listAction<\/a>"],[0,1,"<a href=\"UserController.php.html#119\">Sparefoot\\PitaService\\Controller\\UserController::myfootAction<\/a>"],[0,1,"<a href=\"UserController.php.html#126\">Sparefoot\\PitaService\\Controller\\UserController::pitaAction<\/a>"],[0,1,"<a href=\"UserController.php.html#133\">Sparefoot\\PitaService\\Controller\\UserController::portalAction<\/a>"],[0,9,"<a href=\"UserController.php.html#141\">Sparefoot\\PitaService\\Controller\\UserController::loadusersAction<\/a>"],[0,13,"<a href=\"UserController.php.html#199\">Sparefoot\\PitaService\\Controller\\UserController::editinfoAction<\/a>"],[0,5,"<a href=\"UserController.php.html#248\">Sparefoot\\PitaService\\Controller\\UserController::ajaxcreateAction<\/a>"],[0,4,"<a href=\"UserController.php.html#287\">Sparefoot\\PitaService\\Controller\\UserController::_createPitaUser<\/a>"],[0,3,"<a href=\"UserController.php.html#326\">Sparefoot\\PitaService\\Controller\\UserController::_createPortalUser<\/a>"],[0,14,"<a href=\"UserController.php.html#355\">Sparefoot\\PitaService\\Controller\\UserController::ajaxcreatemyfootAction<\/a>"],[0,2,"<a href=\"UserController.php.html#448\">Sparefoot\\PitaService\\Controller\\UserController::isEmailValid<\/a>"],[0,2,"<a href=\"UserController.php.html#458\">Sparefoot\\PitaService\\Controller\\UserController::_restrictFacilityAccess<\/a>"],[0,3,"<a href=\"UserController.php.html#468\">Sparefoot\\PitaService\\Controller\\UserController::_addFacilityContact<\/a>"],[0,4,"<a href=\"UserController.php.html#485\">Sparefoot\\PitaService\\Controller\\UserController::_buildUser<\/a>"],[0,1,"<a href=\"UserController.php.html#514\">Sparefoot\\PitaService\\Controller\\UserController::createAction<\/a>"],[0,5,"<a href=\"UserController.php.html#582\">Sparefoot\\PitaService\\Controller\\UserController::ajaxpasswordAction<\/a>"],[0,7,"<a href=\"UserController.php.html#612\">Sparefoot\\PitaService\\Controller\\UserController::passwordAction<\/a>"],[0,10,"<a href=\"UserController.php.html#644\">Sparefoot\\PitaService\\Controller\\UserController::changeattributesAction<\/a>"],[0,4,"<a href=\"UserController.php.html#714\">Sparefoot\\PitaService\\Controller\\UserController::addstmtrecipientsAction<\/a>"],[0,4,"<a href=\"UserController.php.html#748\">Sparefoot\\PitaService\\Controller\\UserController::removestmtrecipientsAction<\/a>"],[0,8,"<a href=\"UserController.php.html#782\">Sparefoot\\PitaService\\Controller\\UserController::_export<\/a>"],[0,10,"<a href=\"UserController.php.html#858\">Sparefoot\\PitaService\\Controller\\UserController::_emailRecipientExport<\/a>"],[0,1,"<a href=\"UserController.php.html#926\">Sparefoot\\PitaService\\Controller\\UserController::_getDefaultQuery<\/a>"],[0,8,"<a href=\"UserController.php.html#933\">Sparefoot\\PitaService\\Controller\\UserController::_processQuery<\/a>"],[0,1,"<a href=\"UserController.php.html#953\">Sparefoot\\PitaService\\Controller\\UserController::_processReport<\/a>"],[0,24,"<a href=\"UserController.php.html#966\">Sparefoot\\PitaService\\Controller\\UserController::_processEmailRecipientReport<\/a>"],[0,2,"<a href=\"UserController.php.html#1076\">Sparefoot\\PitaService\\Controller\\UserController::_addAcctMgmtUser<\/a>"],[0,8,"<a href=\"UserController.php.html#1093\">Sparefoot\\PitaService\\Controller\\UserController::_acctMgmtRecipients<\/a>"],[0,11,"<a href=\"UserController.php.html#1142\">Sparefoot\\PitaService\\Controller\\UserController::_facilityContactRecipients<\/a>"],[0,2,"<a href=\"UserController.php.html#1195\">Sparefoot\\PitaService\\Controller\\UserController::_getActiveAcctIds<\/a>"],[0,2,"<a href=\"UserController.php.html#1223\">Sparefoot\\PitaService\\Controller\\UserController::_getActiveListingAvailIds<\/a>"],[0,18,"<a href=\"UserController.php.html#1251\">Sparefoot\\PitaService\\Controller\\UserController::createusersAction<\/a>"],[0,1,"<a href=\"UserController.php.html#1368\">Sparefoot\\PitaService\\Controller\\UserController::tokenAction<\/a>"],[0,2,"<a href=\"UtilitiesController.php.html#26\">Sparefoot\\PitaService\\Controller\\UtilitiesController::initBeforeControllerAction<\/a>"],[0,2,"<a href=\"UtilitiesController.php.html#47\">Sparefoot\\PitaService\\Controller\\UtilitiesController::indexAction<\/a>"],[0,10,"<a href=\"UtilitiesController.php.html#69\">Sparefoot\\PitaService\\Controller\\UtilitiesController::renderAction<\/a>"],[0,3,"<a href=\"UtilitiesController.php.html#142\">Sparefoot\\PitaService\\Controller\\UtilitiesController::updateInputsAction<\/a>"],[0,2,"<a href=\"YellowpagesController.php.html#24\">Sparefoot\\PitaService\\Controller\\YellowpagesController::initBeforeControllerAction<\/a>"],[0,5,"<a href=\"YellowpagesController.php.html#58\">Sparefoot\\PitaService\\Controller\\YellowpagesController::indexAction<\/a>"],[0,4,"<a href=\"YellowpagesController.php.html#103\">Sparefoot\\PitaService\\Controller\\YellowpagesController::citygridAction<\/a>"],[0,3,"<a href=\"YellowpagesController.php.html#148\">Sparefoot\\PitaService\\Controller\\YellowpagesController::citygridfacilityAction<\/a>"],[0,3,"<a href=\"YellowpagesController.php.html#199\">Sparefoot\\PitaService\\Controller\\YellowpagesController::superpagesAction<\/a>"],[0,4,"<a href=\"YellowpagesController.php.html#233\">Sparefoot\\PitaService\\Controller\\YellowpagesController::superpagesfacilityAction<\/a>"],[0,1,"<a href=\"YellowpagesController.php.html#263\">Sparefoot\\PitaService\\Controller\\YellowpagesController::allowiypAction<\/a>"],[0,7,"<a href=\"YellowpagesController.php.html#277\">Sparefoot\\PitaService\\Controller\\YellowpagesController::accountonAction<\/a>"],[0,7,"<a href=\"YellowpagesController.php.html#313\">Sparefoot\\PitaService\\Controller\\YellowpagesController::accountoffAction<\/a>"],[0,1,"<a href=\"YellowpagesController.php.html#359\">Sparefoot\\PitaService\\Controller\\YellowpagesController::localdotcomAction<\/a>"],[0,3,"<a href=\"YellowpagesController.php.html#365\">Sparefoot\\PitaService\\Controller\\YellowpagesController::_initTrueDateRange<\/a>"],[0,1,"<a href=\"YellowpagesController.php.html#385\">Sparefoot\\PitaService\\Controller\\YellowpagesController::getTrueBeginDate<\/a>"],[0,1,"<a href=\"YellowpagesController.php.html#390\">Sparefoot\\PitaService\\Controller\\YellowpagesController::getTrueEndDate<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

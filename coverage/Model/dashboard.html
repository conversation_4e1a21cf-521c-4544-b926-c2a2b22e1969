<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/Model</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="index.html">Model</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbstractModel.php.html#9">Sparefoot\PitaService\Model\AbstractModel</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adhoc.php.html#5">Sparefoot\PitaService\Model\Adhoc</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AgentTeams.php.html#5">Sparefoot\PitaService\Model\AgentTeams</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Child.php.html#5">Sparefoot\PitaService\Model\Child</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Command.php.html#5">Sparefoot\PitaService\Model\Command</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#5">Sparefoot\PitaService\Model\FacilityToSearch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobLock.php.html#5">Sparefoot\PitaService\Model\JobLock</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobLog.php.html#7">Sparefoot\PitaService\Model\JobLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#5">Sparefoot\PitaService\Model\MatchFacilityMapper</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduled.php.html#5">Sparefoot\PitaService\Model\Scheduled</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauAnnotation.php.html#5">Sparefoot\PitaService\Model\TableauAnnotation</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauAnnotationKeys.php.html#5">Sparefoot\PitaService\Model\TableauAnnotationKeys</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkerLog.php.html#5">Sparefoot\PitaService\Model\WorkerLog</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="FacilityToSearch.php.html#5">Sparefoot\PitaService\Model\FacilityToSearch</a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#5">Sparefoot\PitaService\Model\MatchFacilityMapper</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="AbstractModel.php.html#9">Sparefoot\PitaService\Model\AbstractModel</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="TableauAnnotationKeys.php.html#5">Sparefoot\PitaService\Model\TableauAnnotationKeys</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="AgentTeams.php.html#5">Sparefoot\PitaService\Model\AgentTeams</a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobLog.php.html#7">Sparefoot\PitaService\Model\JobLog</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbstractModel.php.html#14"><abbr title="Sparefoot\PitaService\Model\AbstractModel::createAdapter">createAdapter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractModel.php.html#28"><abbr title="Sparefoot\PitaService\Model\AbstractModel::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractModel.php.html#35"><abbr title="Sparefoot\PitaService\Model\AbstractModel::getTableGateway">getTableGateway</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractModel.php.html#40"><abbr title="Sparefoot\PitaService\Model\AbstractModel::getAdapter">getAdapter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractModel.php.html#45"><abbr title="Sparefoot\PitaService\Model\AbstractModel::initSelect">initSelect</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Adhoc.php.html#7"><abbr title="Sparefoot\PitaService\Model\Adhoc::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AgentTeams.php.html#7"><abbr title="Sparefoot\PitaService\Model\AgentTeams::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AgentTeams.php.html#12"><abbr title="Sparefoot\PitaService\Model\AgentTeams::insert">insert</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Child.php.html#7"><abbr title="Sparefoot\PitaService\Model\Child::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Command.php.html#7"><abbr title="Sparefoot\PitaService\Model\Command::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#41"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#55"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::__toString">__toString</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#68"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::toRecord">toRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#84"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setComplementData">setComplementData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#103"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::validateHeaders">validateHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#122"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getHeader">getHeader</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#138"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getFacilityName">getFacilityName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#143"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setFacilityName">setFacilityName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#148"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getStreetAddress">getStreetAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#153"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setStreetAddress">setStreetAddress</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#158"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getCity">getCity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#163"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setCity">setCity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#168"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getState">getState</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#173"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setState">setState</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#178"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getZip">getZip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#183"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setZip">setZip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#188"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getMatched">getMatched</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#193"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setMatched">setMatched</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#198"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getFacilityId">getFacilityId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#203"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setFacilityId">setFacilityId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#208"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getType">getType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#213"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setType">setType</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#218"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getLatitude">getLatitude</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#223"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setLatitude">setLatitude</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#228"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::getLongitude">getLongitude</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#233"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setLongitude">setLongitude</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobLock.php.html#7"><abbr title="Sparefoot\PitaService\Model\JobLock::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobLog.php.html#11"><abbr title="Sparefoot\PitaService\Model\JobLog::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobLog.php.html#16"><abbr title="Sparefoot\PitaService\Model\JobLog::getLastJobLog">getLastJobLog</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#15"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#21"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::mapper">mapper</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#32"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::toCsv">toCsv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#46"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::getFacilities">getFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#54"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::setFacilities">setFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#63"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::getSeparator">getSeparator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#71"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::setSeparator">setSeparator</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Scheduled.php.html#7"><abbr title="Sparefoot\PitaService\Model\Scheduled::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauAnnotation.php.html#7"><abbr title="Sparefoot\PitaService\Model\TableauAnnotation::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauAnnotation.php.html#12"><abbr title="Sparefoot\PitaService\Model\TableauAnnotation::insert">insert</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauAnnotation.php.html#21"><abbr title="Sparefoot\PitaService\Model\TableauAnnotation::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauAnnotationKeys.php.html#7"><abbr title="Sparefoot\PitaService\Model\TableauAnnotationKeys::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauAnnotationKeys.php.html#12"><abbr title="Sparefoot\PitaService\Model\TableauAnnotationKeys::insert">insert</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauAnnotationKeys.php.html#21"><abbr title="Sparefoot\PitaService\Model\TableauAnnotationKeys::update">update</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauAnnotationKeys.php.html#28"><abbr title="Sparefoot\PitaService\Model\TableauAnnotationKeys::updateRow">updateRow</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="WorkerLog.php.html#7"><abbr title="Sparefoot\PitaService\Model\WorkerLog::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="MatchFacilityMapper.php.html#21"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::mapper">mapper</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#84"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::setComplementData">setComplementData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#103"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::validateHeaders">validateHeaders</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#32"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::toCsv">toCsv</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#71"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::setSeparator">setSeparator</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AbstractModel.php.html#45"><abbr title="Sparefoot\PitaService\Model\AbstractModel::initSelect">initSelect</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AgentTeams.php.html#12"><abbr title="Sparefoot\PitaService\Model\AgentTeams::insert">insert</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FacilityToSearch.php.html#41"><abbr title="Sparefoot\PitaService\Model\FacilityToSearch::__construct">__construct</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobLog.php.html#16"><abbr title="Sparefoot\PitaService\Model\JobLog::getLastJobLog">getLastJobLog</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="MatchFacilityMapper.php.html#54"><abbr title="Sparefoot\PitaService\Model\MatchFacilityMapper::setFacilities">setFacilities</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TableauAnnotationKeys.php.html#28"><abbr title="Sparefoot\PitaService\Model\TableauAnnotationKeys::updateRow">updateRow</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([13,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([55,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"AbstractModel.php.html#9\">Sparefoot\\PitaService\\Model\\AbstractModel<\/a>"],[0,1,"<a href=\"Adhoc.php.html#5\">Sparefoot\\PitaService\\Model\\Adhoc<\/a>"],[0,3,"<a href=\"AgentTeams.php.html#5\">Sparefoot\\PitaService\\Model\\AgentTeams<\/a>"],[0,1,"<a href=\"Child.php.html#5\">Sparefoot\\PitaService\\Model\\Child<\/a>"],[0,1,"<a href=\"Command.php.html#5\">Sparefoot\\PitaService\\Model\\Command<\/a>"],[0,33,"<a href=\"FacilityToSearch.php.html#5\">Sparefoot\\PitaService\\Model\\FacilityToSearch<\/a>"],[0,1,"<a href=\"JobLock.php.html#5\">Sparefoot\\PitaService\\Model\\JobLock<\/a>"],[0,3,"<a href=\"JobLog.php.html#7\">Sparefoot\\PitaService\\Model\\JobLog<\/a>"],[0,17,"<a href=\"MatchFacilityMapper.php.html#5\">Sparefoot\\PitaService\\Model\\MatchFacilityMapper<\/a>"],[0,1,"<a href=\"Scheduled.php.html#5\">Sparefoot\\PitaService\\Model\\Scheduled<\/a>"],[0,3,"<a href=\"TableauAnnotation.php.html#5\">Sparefoot\\PitaService\\Model\\TableauAnnotation<\/a>"],[0,5,"<a href=\"TableauAnnotationKeys.php.html#5\">Sparefoot\\PitaService\\Model\\TableauAnnotationKeys<\/a>"],[0,1,"<a href=\"WorkerLog.php.html#5\">Sparefoot\\PitaService\\Model\\WorkerLog<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AbstractModel.php.html#14\">Sparefoot\\PitaService\\Model\\AbstractModel::createAdapter<\/a>"],[0,1,"<a href=\"AbstractModel.php.html#28\">Sparefoot\\PitaService\\Model\\AbstractModel::__construct<\/a>"],[0,1,"<a href=\"AbstractModel.php.html#35\">Sparefoot\\PitaService\\Model\\AbstractModel::getTableGateway<\/a>"],[0,1,"<a href=\"AbstractModel.php.html#40\">Sparefoot\\PitaService\\Model\\AbstractModel::getAdapter<\/a>"],[0,2,"<a href=\"AbstractModel.php.html#45\">Sparefoot\\PitaService\\Model\\AbstractModel::initSelect<\/a>"],[0,1,"<a href=\"Adhoc.php.html#7\">Sparefoot\\PitaService\\Model\\Adhoc::__construct<\/a>"],[0,1,"<a href=\"AgentTeams.php.html#7\">Sparefoot\\PitaService\\Model\\AgentTeams::__construct<\/a>"],[0,2,"<a href=\"AgentTeams.php.html#12\">Sparefoot\\PitaService\\Model\\AgentTeams::insert<\/a>"],[0,1,"<a href=\"Child.php.html#7\">Sparefoot\\PitaService\\Model\\Child::__construct<\/a>"],[0,1,"<a href=\"Command.php.html#7\">Sparefoot\\PitaService\\Model\\Command::__construct<\/a>"],[0,2,"<a href=\"FacilityToSearch.php.html#41\">Sparefoot\\PitaService\\Model\\FacilityToSearch::__construct<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#55\">Sparefoot\\PitaService\\Model\\FacilityToSearch::__toString<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#68\">Sparefoot\\PitaService\\Model\\FacilityToSearch::toRecord<\/a>"],[0,4,"<a href=\"FacilityToSearch.php.html#84\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setComplementData<\/a>"],[0,4,"<a href=\"FacilityToSearch.php.html#103\">Sparefoot\\PitaService\\Model\\FacilityToSearch::validateHeaders<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#122\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getHeader<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#138\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getFacilityName<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#143\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setFacilityName<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#148\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getStreetAddress<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#153\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setStreetAddress<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#158\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getCity<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#163\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setCity<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#168\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getState<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#173\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setState<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#178\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getZip<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#183\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setZip<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#188\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getMatched<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#193\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setMatched<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#198\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getFacilityId<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#203\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setFacilityId<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#208\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getType<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#213\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setType<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#218\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getLatitude<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#223\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setLatitude<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#228\">Sparefoot\\PitaService\\Model\\FacilityToSearch::getLongitude<\/a>"],[0,1,"<a href=\"FacilityToSearch.php.html#233\">Sparefoot\\PitaService\\Model\\FacilityToSearch::setLongitude<\/a>"],[0,1,"<a href=\"JobLock.php.html#7\">Sparefoot\\PitaService\\Model\\JobLock::__construct<\/a>"],[0,1,"<a href=\"JobLog.php.html#11\">Sparefoot\\PitaService\\Model\\JobLog::__construct<\/a>"],[0,2,"<a href=\"JobLog.php.html#16\">Sparefoot\\PitaService\\Model\\JobLog::getLastJobLog<\/a>"],[0,1,"<a href=\"MatchFacilityMapper.php.html#15\">Sparefoot\\PitaService\\Model\\MatchFacilityMapper::__construct<\/a>"],[0,5,"<a href=\"MatchFacilityMapper.php.html#21\">Sparefoot\\PitaService\\Model\\MatchFacilityMapper::mapper<\/a>"],[0,4,"<a href=\"MatchFacilityMapper.php.html#32\">Sparefoot\\PitaService\\Model\\MatchFacilityMapper::toCsv<\/a>"],[0,1,"<a href=\"MatchFacilityMapper.php.html#46\">Sparefoot\\PitaService\\Model\\MatchFacilityMapper::getFacilities<\/a>"],[0,2,"<a href=\"MatchFacilityMapper.php.html#54\">Sparefoot\\PitaService\\Model\\MatchFacilityMapper::setFacilities<\/a>"],[0,1,"<a href=\"MatchFacilityMapper.php.html#63\">Sparefoot\\PitaService\\Model\\MatchFacilityMapper::getSeparator<\/a>"],[0,3,"<a href=\"MatchFacilityMapper.php.html#71\">Sparefoot\\PitaService\\Model\\MatchFacilityMapper::setSeparator<\/a>"],[0,1,"<a href=\"Scheduled.php.html#7\">Sparefoot\\PitaService\\Model\\Scheduled::__construct<\/a>"],[0,1,"<a href=\"TableauAnnotation.php.html#7\">Sparefoot\\PitaService\\Model\\TableauAnnotation::__construct<\/a>"],[0,1,"<a href=\"TableauAnnotation.php.html#12\">Sparefoot\\PitaService\\Model\\TableauAnnotation::insert<\/a>"],[0,1,"<a href=\"TableauAnnotation.php.html#21\">Sparefoot\\PitaService\\Model\\TableauAnnotation::update<\/a>"],[0,1,"<a href=\"TableauAnnotationKeys.php.html#7\">Sparefoot\\PitaService\\Model\\TableauAnnotationKeys::__construct<\/a>"],[0,1,"<a href=\"TableauAnnotationKeys.php.html#12\">Sparefoot\\PitaService\\Model\\TableauAnnotationKeys::insert<\/a>"],[0,1,"<a href=\"TableauAnnotationKeys.php.html#21\">Sparefoot\\PitaService\\Model\\TableauAnnotationKeys::update<\/a>"],[0,2,"<a href=\"TableauAnnotationKeys.php.html#28\">Sparefoot\\PitaService\\Model\\TableauAnnotationKeys::updateRow<\/a>"],[0,1,"<a href=\"WorkerLog.php.html#7\">Sparefoot\\PitaService\\Model\\WorkerLog::__construct<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

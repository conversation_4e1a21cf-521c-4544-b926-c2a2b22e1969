<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickClient/IntegrationClients</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../index.html">QuickClient</a></li>
         <li class="breadcrumb-item"><a href="index.html">IntegrationClients</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="EasyStorageSolutions.php.html#8">Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Extraspace.php.html#8">Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="EasyStorageSolutions.php.html#8">Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="Extraspace.php.html#8">Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace</a></td><td class="text-right">110</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="EasyStorageSolutions.php.html#12"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#22"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#27"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::getFacilities">getFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#36"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::getUnitsForFacility">getUnitsForFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#51"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#66"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::getMoveIns">getMoveIns</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Extraspace.php.html#12"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Extraspace.php.html#17"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Extraspace.php.html#22"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace::getFacilities">getFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Extraspace.php.html#31"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace::getUnitsForFacility">getUnitsForFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Extraspace.php.html#47"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="EasyStorageSolutions.php.html#66"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::getMoveIns">getMoveIns</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#36"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::getUnitsForFacility">getUnitsForFacility</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#51"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::getFacility">getFacility</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Extraspace.php.html#31"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace::getUnitsForFacility">getUnitsForFacility</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Extraspace.php.html#47"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace::getFacility">getFacility</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#27"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\EasyStorageSolutions::getFacilities">getFacilities</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Extraspace.php.html#22"><abbr title="Sparefoot\PitaService\QuickClient\IntegrationClients\ExtraSpace::getFacilities">getFacilities</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([2,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([11,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,14,"<a href=\"EasyStorageSolutions.php.html#8\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\EasyStorageSolutions<\/a>"],[0,10,"<a href=\"Extraspace.php.html#8\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\ExtraSpace<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"EasyStorageSolutions.php.html#12\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\EasyStorageSolutions::__construct<\/a>"],[0,1,"<a href=\"EasyStorageSolutions.php.html#22\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\EasyStorageSolutions::getClient<\/a>"],[0,2,"<a href=\"EasyStorageSolutions.php.html#27\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\EasyStorageSolutions::getFacilities<\/a>"],[0,3,"<a href=\"EasyStorageSolutions.php.html#36\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\EasyStorageSolutions::getUnitsForFacility<\/a>"],[0,3,"<a href=\"EasyStorageSolutions.php.html#51\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\EasyStorageSolutions::getFacility<\/a>"],[0,4,"<a href=\"EasyStorageSolutions.php.html#66\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\EasyStorageSolutions::getMoveIns<\/a>"],[0,1,"<a href=\"Extraspace.php.html#12\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\ExtraSpace::__construct<\/a>"],[0,1,"<a href=\"Extraspace.php.html#17\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\ExtraSpace::getClient<\/a>"],[0,2,"<a href=\"Extraspace.php.html#22\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\ExtraSpace::getFacilities<\/a>"],[0,3,"<a href=\"Extraspace.php.html#31\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\ExtraSpace::getUnitsForFacility<\/a>"],[0,3,"<a href=\"Extraspace.php.html#47\">Sparefoot\\PitaService\\QuickClient\\IntegrationClients\\ExtraSpace::getFacility<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickClient/Client</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../index.html">QuickClient</a></li>
         <li class="breadcrumb-item"><a href="index.html">Client</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Centershift4.php.html#7">Sparefoot\PitaService\QuickClient\Client\Centershift4</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Cubesmart.php.html#7">Sparefoot\PitaService\QuickClient\Client\Cubesmart</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domico.php.html#7">Sparefoot\PitaService\QuickClient\Client\Domico</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Doorswap.php.html#7">Sparefoot\PitaService\QuickClient\Client\Doorswap</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#8">Sparefoot\PitaService\QuickClient\Client\EasyStorageSolutions</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExtraSpace.php.html#8">Sparefoot\PitaService\QuickClient\Client\ExtraSpace</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Opentech.php.html#7">Sparefoot\PitaService\QuickClient\Client\Opentech</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quikstor.php.html#7">Sparefoot\PitaService\QuickClient\Client\Quikstor</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#7">Sparefoot\PitaService\QuickClient\Client\Safeguard</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#7">Sparefoot\PitaService\QuickClient\Client\SelfStorageManager</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#7">Sparefoot\PitaService\QuickClient\Client\Sentry</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sitelink.php.html#7">Sparefoot\PitaService\QuickClient\Client\Sitelink</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#7">Sparefoot\PitaService\QuickClient\Client\SitelinkReporting</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageMart.php.html#7">Sparefoot\PitaService\QuickClient\Client\StorageMart</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Storedge.php.html#7">Sparefoot\PitaService\QuickClient\Client\Storedge</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UncleBobs.php.html#7">Sparefoot\PitaService\QuickClient\Client\UncleBobs</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SelfStorageManager.php.html#7">Sparefoot\PitaService\QuickClient\Client\SelfStorageManager</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="Centershift4.php.html#7">Sparefoot\PitaService\QuickClient\Client\Centershift4</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Domico.php.html#7">Sparefoot\PitaService\QuickClient\Client\Domico</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Doorswap.php.html#7">Sparefoot\PitaService\QuickClient\Client\Doorswap</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Quikstor.php.html#7">Sparefoot\PitaService\QuickClient\Client\Quikstor</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Sitelink.php.html#7">Sparefoot\PitaService\QuickClient\Client\Sitelink</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#7">Sparefoot\PitaService\QuickClient\Client\SitelinkReporting</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="Cubesmart.php.html#7">Sparefoot\PitaService\QuickClient\Client\Cubesmart</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="StorageMart.php.html#7">Sparefoot\PitaService\QuickClient\Client\StorageMart</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="UncleBobs.php.html#7">Sparefoot\PitaService\QuickClient\Client\UncleBobs</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ExtraSpace.php.html#8">Sparefoot\PitaService\QuickClient\Client\ExtraSpace</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Opentech.php.html#7">Sparefoot\PitaService\QuickClient\Client\Opentech</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="Storedge.php.html#7">Sparefoot\PitaService\QuickClient\Client\Storedge</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#8">Sparefoot\PitaService\QuickClient\Client\EasyStorageSolutions</a></td><td class="text-right">56</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Centershift4.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Centershift4.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Centershift4.php.html#18"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Centershift4.php.html#35"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Centershift4.php.html#49"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Centershift4.php.html#54"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Centershift4.php.html#67"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Centershift4.php.html#72"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Cubesmart.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Cubesmart.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Cubesmart.php.html#22"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Cubesmart.php.html#37"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Cubesmart.php.html#52"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Cubesmart.php.html#56"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Cubesmart.php.html#61"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Cubesmart.php.html#66"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domico.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domico.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domico.php.html#29"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domico.php.html#44"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domico.php.html#59"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domico.php.html#63"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domico.php.html#68"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Domico.php.html#73"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Doorswap.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Doorswap.php.html#19"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Doorswap.php.html#43"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Doorswap.php.html#58"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Doorswap.php.html#73"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Doorswap.php.html#77"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Doorswap.php.html#82"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Doorswap.php.html#92"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#10"><abbr title="Sparefoot\PitaService\QuickClient\Client\EasyStorageSolutions::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#15"><abbr title="Sparefoot\PitaService\QuickClient\Client\EasyStorageSolutions::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#19"><abbr title="Sparefoot\PitaService\QuickClient\Client\EasyStorageSolutions::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#29"><abbr title="Sparefoot\PitaService\QuickClient\Client\EasyStorageSolutions::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#34"><abbr title="Sparefoot\PitaService\QuickClient\Client\EasyStorageSolutions::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#39"><abbr title="Sparefoot\PitaService\QuickClient\Client\EasyStorageSolutions::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExtraSpace.php.html#12"><abbr title="Sparefoot\PitaService\QuickClient\Client\ExtraSpace::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExtraSpace.php.html#17"><abbr title="Sparefoot\PitaService\QuickClient\Client\ExtraSpace::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExtraSpace.php.html#32"><abbr title="Sparefoot\PitaService\QuickClient\Client\ExtraSpace::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExtraSpace.php.html#36"><abbr title="Sparefoot\PitaService\QuickClient\Client\ExtraSpace::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExtraSpace.php.html#41"><abbr title="Sparefoot\PitaService\QuickClient\Client\ExtraSpace::getIntegrations">getIntegrations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExtraSpace.php.html#48"><abbr title="Sparefoot\PitaService\QuickClient\Client\ExtraSpace::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExtraSpace.php.html#53"><abbr title="Sparefoot\PitaService\QuickClient\Client\ExtraSpace::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExtraSpace.php.html#58"><abbr title="Sparefoot\PitaService\QuickClient\Client\ExtraSpace::customFacilitiesFetch">customFacilitiesFetch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Opentech.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Opentech::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Opentech.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Opentech::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Opentech.php.html#28"><abbr title="Sparefoot\PitaService\QuickClient\Client\Opentech::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Opentech.php.html#42"><abbr title="Sparefoot\PitaService\QuickClient\Client\Opentech::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Opentech.php.html#56"><abbr title="Sparefoot\PitaService\QuickClient\Client\Opentech::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Opentech.php.html#60"><abbr title="Sparefoot\PitaService\QuickClient\Client\Opentech::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Opentech.php.html#65"><abbr title="Sparefoot\PitaService\QuickClient\Client\Opentech::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Opentech.php.html#70"><abbr title="Sparefoot\PitaService\QuickClient\Client\Opentech::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quikstor.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quikstor.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quikstor.php.html#31"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quikstor.php.html#45"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quikstor.php.html#59"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quikstor.php.html#63"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quikstor.php.html#68"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Quikstor.php.html#73"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Safeguard::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Safeguard::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#18"><abbr title="Sparefoot\PitaService\QuickClient\Client\Safeguard::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#24"><abbr title="Sparefoot\PitaService\QuickClient\Client\Safeguard::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#31"><abbr title="Sparefoot\PitaService\QuickClient\Client\Safeguard::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#36"><abbr title="Sparefoot\PitaService\QuickClient\Client\Safeguard::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#43"><abbr title="Sparefoot\PitaService\QuickClient\Client\Safeguard::getUnits">getUnits</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#48"><abbr title="Sparefoot\PitaService\QuickClient\Client\Safeguard::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Safeguard.php.html#53"><abbr title="Sparefoot\PitaService\QuickClient\Client\Safeguard::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#19"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#42"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#65"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#90"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#94"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#99"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#104"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sentry::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sentry::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#18"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sentry::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#24"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sentry::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#31"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sentry::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#36"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sentry::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#43"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sentry::getUnits">getUnits</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#48"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sentry::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sentry.php.html#53"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sentry::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sitelink.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sitelink.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sitelink.php.html#34"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sitelink.php.html#48"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sitelink.php.html#62"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sitelink.php.html#66"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sitelink.php.html#71"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Sitelink.php.html#76"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#34"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#48"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#62"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#66"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#71"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#76"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageMart.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageMart.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageMart.php.html#22"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageMart.php.html#33"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageMart.php.html#43"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageMart.php.html#47"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageMart.php.html#52"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorageMart.php.html#57"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Storedge.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\Storedge::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Storedge.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Storedge::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Storedge.php.html#23"><abbr title="Sparefoot\PitaService\QuickClient\Client\Storedge::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Storedge.php.html#38"><abbr title="Sparefoot\PitaService\QuickClient\Client\Storedge::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Storedge.php.html#47"><abbr title="Sparefoot\PitaService\QuickClient\Client\Storedge::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Storedge.php.html#51"><abbr title="Sparefoot\PitaService\QuickClient\Client\Storedge::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Storedge.php.html#56"><abbr title="Sparefoot\PitaService\QuickClient\Client\Storedge::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Storedge.php.html#61"><abbr title="Sparefoot\PitaService\QuickClient\Client\Storedge::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UncleBobs.php.html#9"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::getClient">getClient</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UncleBobs.php.html#19"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UncleBobs.php.html#26"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UncleBobs.php.html#40"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UncleBobs.php.html#54"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UncleBobs.php.html#58"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::getIntegrationName">getIntegrationName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UncleBobs.php.html#63"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::getSourceId">getSourceId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UncleBobs.php.html#68"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::getFacility">getFacility</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SelfStorageManager.php.html#42"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#65"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#104"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getFacility">getFacility</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Centershift4.php.html#18"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Centershift4.php.html#35"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Centershift4.php.html#54"><abbr title="Sparefoot\PitaService\QuickClient\Client\Centershift4::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Cubesmart.php.html#22"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Cubesmart.php.html#37"><abbr title="Sparefoot\PitaService\QuickClient\Client\Cubesmart::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Domico.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Domico.php.html#29"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Domico.php.html#44"><abbr title="Sparefoot\PitaService\QuickClient\Client\Domico::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Doorswap.php.html#19"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Doorswap.php.html#43"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Doorswap.php.html#58"><abbr title="Sparefoot\PitaService\QuickClient\Client\Doorswap::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="EasyStorageSolutions.php.html#19"><abbr title="Sparefoot\PitaService\QuickClient\Client\EasyStorageSolutions::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExtraSpace.php.html#17"><abbr title="Sparefoot\PitaService\QuickClient\Client\ExtraSpace::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Opentech.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Opentech::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quikstor.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quikstor.php.html#31"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Quikstor.php.html#45"><abbr title="Sparefoot\PitaService\QuickClient\Client\Quikstor::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SelfStorageManager.php.html#19"><abbr title="Sparefoot\PitaService\QuickClient\Client\SelfStorageManager::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Sitelink.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Sitelink.php.html#34"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Sitelink.php.html#48"><abbr title="Sparefoot\PitaService\QuickClient\Client\Sitelink::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#14"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getIntegrationParams">getIntegrationParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#34"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SitelinkReporting.php.html#48"><abbr title="Sparefoot\PitaService\QuickClient\Client\SitelinkReporting::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StorageMart.php.html#22"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StorageMart.php.html#33"><abbr title="Sparefoot\PitaService\QuickClient\Client\StorageMart::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Storedge.php.html#23"><abbr title="Sparefoot\PitaService\QuickClient\Client\Storedge::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UncleBobs.php.html#26"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::getFacilityParams">getFacilityParams</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UncleBobs.php.html#40"><abbr title="Sparefoot\PitaService\QuickClient\Client\UncleBobs::getUnitParams">getUnitParams</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([16,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([129,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,11,"<a href=\"Centershift4.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Centershift4<\/a>"],[0,10,"<a href=\"Cubesmart.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Cubesmart<\/a>"],[0,11,"<a href=\"Domico.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Domico<\/a>"],[0,11,"<a href=\"Doorswap.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Doorswap<\/a>"],[0,7,"<a href=\"EasyStorageSolutions.php.html#8\">Sparefoot\\PitaService\\QuickClient\\Client\\EasyStorageSolutions<\/a>"],[0,9,"<a href=\"ExtraSpace.php.html#8\">Sparefoot\\PitaService\\QuickClient\\Client\\ExtraSpace<\/a>"],[0,9,"<a href=\"Opentech.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Opentech<\/a>"],[0,11,"<a href=\"Quikstor.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Quikstor<\/a>"],[0,9,"<a href=\"Safeguard.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard<\/a>"],[0,18,"<a href=\"SelfStorageManager.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager<\/a>"],[0,9,"<a href=\"Sentry.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry<\/a>"],[0,11,"<a href=\"Sitelink.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Sitelink<\/a>"],[0,11,"<a href=\"SitelinkReporting.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\SitelinkReporting<\/a>"],[0,10,"<a href=\"StorageMart.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\StorageMart<\/a>"],[0,9,"<a href=\"Storedge.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\Storedge<\/a>"],[0,10,"<a href=\"UncleBobs.php.html#7\">Sparefoot\\PitaService\\QuickClient\\Client\\UncleBobs<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"Centershift4.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Centershift4::getClient<\/a>"],[0,1,"<a href=\"Centershift4.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\Centershift4::execute<\/a>"],[0,2,"<a href=\"Centershift4.php.html#18\">Sparefoot\\PitaService\\QuickClient\\Client\\Centershift4::getIntegrationParams<\/a>"],[0,2,"<a href=\"Centershift4.php.html#35\">Sparefoot\\PitaService\\QuickClient\\Client\\Centershift4::getFacilityParams<\/a>"],[0,1,"<a href=\"Centershift4.php.html#49\">Sparefoot\\PitaService\\QuickClient\\Client\\Centershift4::getFacility<\/a>"],[0,2,"<a href=\"Centershift4.php.html#54\">Sparefoot\\PitaService\\QuickClient\\Client\\Centershift4::getUnitParams<\/a>"],[0,1,"<a href=\"Centershift4.php.html#67\">Sparefoot\\PitaService\\QuickClient\\Client\\Centershift4::getIntegrationName<\/a>"],[0,1,"<a href=\"Centershift4.php.html#72\">Sparefoot\\PitaService\\QuickClient\\Client\\Centershift4::getSourceId<\/a>"],[0,1,"<a href=\"Cubesmart.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Cubesmart::getClient<\/a>"],[0,1,"<a href=\"Cubesmart.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\Cubesmart::getIntegrationParams<\/a>"],[0,2,"<a href=\"Cubesmart.php.html#22\">Sparefoot\\PitaService\\QuickClient\\Client\\Cubesmart::getFacilityParams<\/a>"],[0,2,"<a href=\"Cubesmart.php.html#37\">Sparefoot\\PitaService\\QuickClient\\Client\\Cubesmart::getUnitParams<\/a>"],[0,1,"<a href=\"Cubesmart.php.html#52\">Sparefoot\\PitaService\\QuickClient\\Client\\Cubesmart::execute<\/a>"],[0,1,"<a href=\"Cubesmart.php.html#56\">Sparefoot\\PitaService\\QuickClient\\Client\\Cubesmart::getIntegrationName<\/a>"],[0,1,"<a href=\"Cubesmart.php.html#61\">Sparefoot\\PitaService\\QuickClient\\Client\\Cubesmart::getSourceId<\/a>"],[0,1,"<a href=\"Cubesmart.php.html#66\">Sparefoot\\PitaService\\QuickClient\\Client\\Cubesmart::getFacility<\/a>"],[0,1,"<a href=\"Domico.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Domico::getClient<\/a>"],[0,2,"<a href=\"Domico.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\Domico::getIntegrationParams<\/a>"],[0,2,"<a href=\"Domico.php.html#29\">Sparefoot\\PitaService\\QuickClient\\Client\\Domico::getFacilityParams<\/a>"],[0,2,"<a href=\"Domico.php.html#44\">Sparefoot\\PitaService\\QuickClient\\Client\\Domico::getUnitParams<\/a>"],[0,1,"<a href=\"Domico.php.html#59\">Sparefoot\\PitaService\\QuickClient\\Client\\Domico::execute<\/a>"],[0,1,"<a href=\"Domico.php.html#63\">Sparefoot\\PitaService\\QuickClient\\Client\\Domico::getIntegrationName<\/a>"],[0,1,"<a href=\"Domico.php.html#68\">Sparefoot\\PitaService\\QuickClient\\Client\\Domico::getSourceId<\/a>"],[0,1,"<a href=\"Domico.php.html#73\">Sparefoot\\PitaService\\QuickClient\\Client\\Domico::getFacility<\/a>"],[0,1,"<a href=\"Doorswap.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Doorswap::getClient<\/a>"],[0,2,"<a href=\"Doorswap.php.html#19\">Sparefoot\\PitaService\\QuickClient\\Client\\Doorswap::getIntegrationParams<\/a>"],[0,2,"<a href=\"Doorswap.php.html#43\">Sparefoot\\PitaService\\QuickClient\\Client\\Doorswap::getFacilityParams<\/a>"],[0,2,"<a href=\"Doorswap.php.html#58\">Sparefoot\\PitaService\\QuickClient\\Client\\Doorswap::getUnitParams<\/a>"],[0,1,"<a href=\"Doorswap.php.html#73\">Sparefoot\\PitaService\\QuickClient\\Client\\Doorswap::execute<\/a>"],[0,1,"<a href=\"Doorswap.php.html#77\">Sparefoot\\PitaService\\QuickClient\\Client\\Doorswap::getIntegrationName<\/a>"],[0,1,"<a href=\"Doorswap.php.html#82\">Sparefoot\\PitaService\\QuickClient\\Client\\Doorswap::getSourceId<\/a>"],[0,1,"<a href=\"Doorswap.php.html#92\">Sparefoot\\PitaService\\QuickClient\\Client\\Doorswap::getFacility<\/a>"],[0,1,"<a href=\"EasyStorageSolutions.php.html#10\">Sparefoot\\PitaService\\QuickClient\\Client\\EasyStorageSolutions::getClient<\/a>"],[0,1,"<a href=\"EasyStorageSolutions.php.html#15\">Sparefoot\\PitaService\\QuickClient\\Client\\EasyStorageSolutions::execute<\/a>"],[0,2,"<a href=\"EasyStorageSolutions.php.html#19\">Sparefoot\\PitaService\\QuickClient\\Client\\EasyStorageSolutions::getFacilityParams<\/a>"],[0,1,"<a href=\"EasyStorageSolutions.php.html#29\">Sparefoot\\PitaService\\QuickClient\\Client\\EasyStorageSolutions::getFacility<\/a>"],[0,1,"<a href=\"EasyStorageSolutions.php.html#34\">Sparefoot\\PitaService\\QuickClient\\Client\\EasyStorageSolutions::getIntegrationName<\/a>"],[0,1,"<a href=\"EasyStorageSolutions.php.html#39\">Sparefoot\\PitaService\\QuickClient\\Client\\EasyStorageSolutions::getSourceId<\/a>"],[0,1,"<a href=\"ExtraSpace.php.html#12\">Sparefoot\\PitaService\\QuickClient\\Client\\ExtraSpace::getClient<\/a>"],[0,2,"<a href=\"ExtraSpace.php.html#17\">Sparefoot\\PitaService\\QuickClient\\Client\\ExtraSpace::getFacilityParams<\/a>"],[0,1,"<a href=\"ExtraSpace.php.html#32\">Sparefoot\\PitaService\\QuickClient\\Client\\ExtraSpace::execute<\/a>"],[0,1,"<a href=\"ExtraSpace.php.html#36\">Sparefoot\\PitaService\\QuickClient\\Client\\ExtraSpace::getIntegrationName<\/a>"],[0,1,"<a href=\"ExtraSpace.php.html#41\">Sparefoot\\PitaService\\QuickClient\\Client\\ExtraSpace::getIntegrations<\/a>"],[0,1,"<a href=\"ExtraSpace.php.html#48\">Sparefoot\\PitaService\\QuickClient\\Client\\ExtraSpace::getSourceId<\/a>"],[0,1,"<a href=\"ExtraSpace.php.html#53\">Sparefoot\\PitaService\\QuickClient\\Client\\ExtraSpace::getFacility<\/a>"],[0,1,"<a href=\"ExtraSpace.php.html#58\">Sparefoot\\PitaService\\QuickClient\\Client\\ExtraSpace::customFacilitiesFetch<\/a>"],[0,1,"<a href=\"Opentech.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Opentech::getClient<\/a>"],[0,2,"<a href=\"Opentech.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\Opentech::getIntegrationParams<\/a>"],[0,1,"<a href=\"Opentech.php.html#28\">Sparefoot\\PitaService\\QuickClient\\Client\\Opentech::getFacilityParams<\/a>"],[0,1,"<a href=\"Opentech.php.html#42\">Sparefoot\\PitaService\\QuickClient\\Client\\Opentech::getUnitParams<\/a>"],[0,1,"<a href=\"Opentech.php.html#56\">Sparefoot\\PitaService\\QuickClient\\Client\\Opentech::execute<\/a>"],[0,1,"<a href=\"Opentech.php.html#60\">Sparefoot\\PitaService\\QuickClient\\Client\\Opentech::getIntegrationName<\/a>"],[0,1,"<a href=\"Opentech.php.html#65\">Sparefoot\\PitaService\\QuickClient\\Client\\Opentech::getSourceId<\/a>"],[0,1,"<a href=\"Opentech.php.html#70\">Sparefoot\\PitaService\\QuickClient\\Client\\Opentech::getFacility<\/a>"],[0,1,"<a href=\"Quikstor.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Quikstor::getClient<\/a>"],[0,2,"<a href=\"Quikstor.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\Quikstor::getIntegrationParams<\/a>"],[0,2,"<a href=\"Quikstor.php.html#31\">Sparefoot\\PitaService\\QuickClient\\Client\\Quikstor::getFacilityParams<\/a>"],[0,2,"<a href=\"Quikstor.php.html#45\">Sparefoot\\PitaService\\QuickClient\\Client\\Quikstor::getUnitParams<\/a>"],[0,1,"<a href=\"Quikstor.php.html#59\">Sparefoot\\PitaService\\QuickClient\\Client\\Quikstor::execute<\/a>"],[0,1,"<a href=\"Quikstor.php.html#63\">Sparefoot\\PitaService\\QuickClient\\Client\\Quikstor::getIntegrationName<\/a>"],[0,1,"<a href=\"Quikstor.php.html#68\">Sparefoot\\PitaService\\QuickClient\\Client\\Quikstor::getSourceId<\/a>"],[0,1,"<a href=\"Quikstor.php.html#73\">Sparefoot\\PitaService\\QuickClient\\Client\\Quikstor::getFacility<\/a>"],[0,1,"<a href=\"Safeguard.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard::getClient<\/a>"],[0,1,"<a href=\"Safeguard.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard::execute<\/a>"],[0,1,"<a href=\"Safeguard.php.html#18\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard::getIntegrationParams<\/a>"],[0,1,"<a href=\"Safeguard.php.html#24\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard::getFacilityParams<\/a>"],[0,1,"<a href=\"Safeguard.php.html#31\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard::getFacility<\/a>"],[0,1,"<a href=\"Safeguard.php.html#36\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard::getUnitParams<\/a>"],[0,1,"<a href=\"Safeguard.php.html#43\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard::getUnits<\/a>"],[0,1,"<a href=\"Safeguard.php.html#48\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard::getIntegrationName<\/a>"],[0,1,"<a href=\"Safeguard.php.html#53\">Sparefoot\\PitaService\\QuickClient\\Client\\Safeguard::getSourceId<\/a>"],[0,1,"<a href=\"SelfStorageManager.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager::__construct<\/a>"],[0,1,"<a href=\"SelfStorageManager.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager::getClient<\/a>"],[0,2,"<a href=\"SelfStorageManager.php.html#19\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager::getIntegrationParams<\/a>"],[0,4,"<a href=\"SelfStorageManager.php.html#42\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager::getFacilityParams<\/a>"],[0,4,"<a href=\"SelfStorageManager.php.html#65\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager::getUnitParams<\/a>"],[0,1,"<a href=\"SelfStorageManager.php.html#90\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager::execute<\/a>"],[0,1,"<a href=\"SelfStorageManager.php.html#94\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager::getIntegrationName<\/a>"],[0,1,"<a href=\"SelfStorageManager.php.html#99\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager::getSourceId<\/a>"],[0,3,"<a href=\"SelfStorageManager.php.html#104\">Sparefoot\\PitaService\\QuickClient\\Client\\SelfStorageManager::getFacility<\/a>"],[0,1,"<a href=\"Sentry.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry::getClient<\/a>"],[0,1,"<a href=\"Sentry.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry::execute<\/a>"],[0,1,"<a href=\"Sentry.php.html#18\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry::getIntegrationParams<\/a>"],[0,1,"<a href=\"Sentry.php.html#24\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry::getFacilityParams<\/a>"],[0,1,"<a href=\"Sentry.php.html#31\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry::getFacility<\/a>"],[0,1,"<a href=\"Sentry.php.html#36\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry::getUnitParams<\/a>"],[0,1,"<a href=\"Sentry.php.html#43\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry::getUnits<\/a>"],[0,1,"<a href=\"Sentry.php.html#48\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry::getIntegrationName<\/a>"],[0,1,"<a href=\"Sentry.php.html#53\">Sparefoot\\PitaService\\QuickClient\\Client\\Sentry::getSourceId<\/a>"],[0,1,"<a href=\"Sitelink.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Sitelink::getClient<\/a>"],[0,2,"<a href=\"Sitelink.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\Sitelink::getIntegrationParams<\/a>"],[0,2,"<a href=\"Sitelink.php.html#34\">Sparefoot\\PitaService\\QuickClient\\Client\\Sitelink::getFacilityParams<\/a>"],[0,2,"<a href=\"Sitelink.php.html#48\">Sparefoot\\PitaService\\QuickClient\\Client\\Sitelink::getUnitParams<\/a>"],[0,1,"<a href=\"Sitelink.php.html#62\">Sparefoot\\PitaService\\QuickClient\\Client\\Sitelink::execute<\/a>"],[0,1,"<a href=\"Sitelink.php.html#66\">Sparefoot\\PitaService\\QuickClient\\Client\\Sitelink::getIntegrationName<\/a>"],[0,1,"<a href=\"Sitelink.php.html#71\">Sparefoot\\PitaService\\QuickClient\\Client\\Sitelink::getSourceId<\/a>"],[0,1,"<a href=\"Sitelink.php.html#76\">Sparefoot\\PitaService\\QuickClient\\Client\\Sitelink::getFacility<\/a>"],[0,1,"<a href=\"SitelinkReporting.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\SitelinkReporting::getClient<\/a>"],[0,2,"<a href=\"SitelinkReporting.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\SitelinkReporting::getIntegrationParams<\/a>"],[0,2,"<a href=\"SitelinkReporting.php.html#34\">Sparefoot\\PitaService\\QuickClient\\Client\\SitelinkReporting::getFacilityParams<\/a>"],[0,2,"<a href=\"SitelinkReporting.php.html#48\">Sparefoot\\PitaService\\QuickClient\\Client\\SitelinkReporting::getUnitParams<\/a>"],[0,1,"<a href=\"SitelinkReporting.php.html#62\">Sparefoot\\PitaService\\QuickClient\\Client\\SitelinkReporting::execute<\/a>"],[0,1,"<a href=\"SitelinkReporting.php.html#66\">Sparefoot\\PitaService\\QuickClient\\Client\\SitelinkReporting::getIntegrationName<\/a>"],[0,1,"<a href=\"SitelinkReporting.php.html#71\">Sparefoot\\PitaService\\QuickClient\\Client\\SitelinkReporting::getSourceId<\/a>"],[0,1,"<a href=\"SitelinkReporting.php.html#76\">Sparefoot\\PitaService\\QuickClient\\Client\\SitelinkReporting::getFacility<\/a>"],[0,1,"<a href=\"StorageMart.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\StorageMart::getClient<\/a>"],[0,1,"<a href=\"StorageMart.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\StorageMart::getIntegrationParams<\/a>"],[0,2,"<a href=\"StorageMart.php.html#22\">Sparefoot\\PitaService\\QuickClient\\Client\\StorageMart::getFacilityParams<\/a>"],[0,2,"<a href=\"StorageMart.php.html#33\">Sparefoot\\PitaService\\QuickClient\\Client\\StorageMart::getUnitParams<\/a>"],[0,1,"<a href=\"StorageMart.php.html#43\">Sparefoot\\PitaService\\QuickClient\\Client\\StorageMart::execute<\/a>"],[0,1,"<a href=\"StorageMart.php.html#47\">Sparefoot\\PitaService\\QuickClient\\Client\\StorageMart::getIntegrationName<\/a>"],[0,1,"<a href=\"StorageMart.php.html#52\">Sparefoot\\PitaService\\QuickClient\\Client\\StorageMart::getSourceId<\/a>"],[0,1,"<a href=\"StorageMart.php.html#57\">Sparefoot\\PitaService\\QuickClient\\Client\\StorageMart::getFacility<\/a>"],[0,1,"<a href=\"Storedge.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\Storedge::getClient<\/a>"],[0,1,"<a href=\"Storedge.php.html#14\">Sparefoot\\PitaService\\QuickClient\\Client\\Storedge::getIntegrationParams<\/a>"],[0,2,"<a href=\"Storedge.php.html#23\">Sparefoot\\PitaService\\QuickClient\\Client\\Storedge::getFacilityParams<\/a>"],[0,1,"<a href=\"Storedge.php.html#38\">Sparefoot\\PitaService\\QuickClient\\Client\\Storedge::getUnitParams<\/a>"],[0,1,"<a href=\"Storedge.php.html#47\">Sparefoot\\PitaService\\QuickClient\\Client\\Storedge::execute<\/a>"],[0,1,"<a href=\"Storedge.php.html#51\">Sparefoot\\PitaService\\QuickClient\\Client\\Storedge::getIntegrationName<\/a>"],[0,1,"<a href=\"Storedge.php.html#56\">Sparefoot\\PitaService\\QuickClient\\Client\\Storedge::getSourceId<\/a>"],[0,1,"<a href=\"Storedge.php.html#61\">Sparefoot\\PitaService\\QuickClient\\Client\\Storedge::getFacility<\/a>"],[0,1,"<a href=\"UncleBobs.php.html#9\">Sparefoot\\PitaService\\QuickClient\\Client\\UncleBobs::getClient<\/a>"],[0,1,"<a href=\"UncleBobs.php.html#19\">Sparefoot\\PitaService\\QuickClient\\Client\\UncleBobs::getIntegrationParams<\/a>"],[0,2,"<a href=\"UncleBobs.php.html#26\">Sparefoot\\PitaService\\QuickClient\\Client\\UncleBobs::getFacilityParams<\/a>"],[0,2,"<a href=\"UncleBobs.php.html#40\">Sparefoot\\PitaService\\QuickClient\\Client\\UncleBobs::getUnitParams<\/a>"],[0,1,"<a href=\"UncleBobs.php.html#54\">Sparefoot\\PitaService\\QuickClient\\Client\\UncleBobs::execute<\/a>"],[0,1,"<a href=\"UncleBobs.php.html#58\">Sparefoot\\PitaService\\QuickClient\\Client\\UncleBobs::getIntegrationName<\/a>"],[0,1,"<a href=\"UncleBobs.php.html#63\">Sparefoot\\PitaService\\QuickClient\\Client\\UncleBobs::getSourceId<\/a>"],[0,1,"<a href=\"UncleBobs.php.html#68\">Sparefoot\\PitaService\\QuickClient\\Client\\UncleBobs::getFacility<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

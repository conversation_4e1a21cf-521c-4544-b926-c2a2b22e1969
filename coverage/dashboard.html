<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/Controller</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="index.html">/var/www/service/src/Controller</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbstractGenericController.php.html#8">Sparefoot\PitaService\Controller\AbstractGenericController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#8">Sparefoot\PitaService\Controller\AbstractView\GenericView</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#11">Sparefoot\PitaService\Controller\AccountView\ListActionView</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#11">Sparefoot\PitaService\Controller\BookingsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#11">Sparefoot\PitaService\Controller\BuyoutController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#9">Sparefoot\PitaService\Controller\CdpController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchController.php.html#8">Sparefoot\PitaService\Controller\CentaursearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#9">Sparefoot\PitaService\Controller\CentaursearchapiController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#9">Sparefoot\PitaService\Controller\CitygridController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#13">Sparefoot\PitaService\Controller\CpaPercentRolloutController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutView/IndexActionView.php.html#10">Sparefoot\PitaService\Controller\CpaPercentRolloutView\IndexActionView</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#10">Sparefoot\PitaService\Controller\DashboardController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#9">Sparefoot\PitaService\Controller\DisastersController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#11">Sparefoot\PitaService\Controller\DisputesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#9">Sparefoot\PitaService\Controller\EmailController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ErrorController.php.html#10">Sparefoot\PitaService\Controller\ErrorController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#10">Sparefoot\PitaService\Controller\FacilitiesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#24">Sparefoot\PitaService\Controller\FacilityController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#10">Sparefoot\PitaService\Controller\FaqsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FederatedController.php.html#11">Sparefoot\PitaService\Controller\FederatedController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#9">Sparefoot\PitaService\Controller\FeedsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FiltersController.php.html#11">Sparefoot\PitaService\Controller\FiltersController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HeatmapController.php.html#10">Sparefoot\PitaService\Controller\HeatmapController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#11">Sparefoot\PitaService\Controller\IncentivesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#12">Sparefoot\PitaService\Controller\IndexController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexView/IndexActionView.php.html#10">Sparefoot\PitaService\Controller\IndexView\IndexActionView</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#13">Sparefoot\PitaService\Controller\InventoryController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#17">Sparefoot\PitaService\Controller\JobsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#12">Sparefoot\PitaService\Controller\LoginController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#9">Sparefoot\PitaService\Controller\MaintenanceModeController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NetsuiteController.php.html#10">Sparefoot\PitaService\Controller\NetsuiteController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#9">Sparefoot\PitaService\Controller\OmnomController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#10">Sparefoot\PitaService\Controller\PaidmediaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#12">Sparefoot\PitaService\Controller\PayoutController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PhotosController.php.html#10">Sparefoot\PitaService\Controller\PhotosController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PitaSearchController.php.html#13">Sparefoot\PitaService\Controller\PitaSearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#16">Sparefoot\PitaService\Controller\ProxyController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#13">Sparefoot\PitaService\Controller\PublicController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#11">Sparefoot\PitaService\Controller\QuickclientController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#9">Sparefoot\PitaService\Controller\QuickformController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickjobController.php.html#10">Sparefoot\PitaService\Controller\QuickjobController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#15">Sparefoot\PitaService\Controller\QuickrepController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#16">Sparefoot\PitaService\Controller\QuicksoapController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#10">Sparefoot\PitaService\Controller\QuicktaggerController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#9">Sparefoot\PitaService\Controller\ReferralsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#14">Sparefoot\PitaService\Controller\ReportsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#10">Sparefoot\PitaService\Controller\ReviewsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#9">Sparefoot\PitaService\Controller\RewardsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#9">Sparefoot\PitaService\Controller\SalesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesforceController.php.html#11">Sparefoot\PitaService\Controller\SalesforceController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#9">Sparefoot\PitaService\Controller\SearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#10">Sparefoot\PitaService\Controller\ServiceareaController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#9">Sparefoot\PitaService\Controller\SoftwarepartnerController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#9">Sparefoot\PitaService\Controller\SphinxsearchController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#14">Sparefoot\PitaService\Controller\StatementsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#13">Sparefoot\PitaService\Controller\TableauController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#10">Sparefoot\PitaService\Controller\TestController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#13">Sparefoot\PitaService\Controller\ToolsController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TriController.php.html#9">Sparefoot\PitaService\Controller\TriController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#13">Sparefoot\PitaService\Controller\UserController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#12">Sparefoot\PitaService\Controller\UtilitiesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#9">Sparefoot\PitaService\Controller\YellowpagesController</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#11">Sparefoot\PitaService\Controller\AccountController</a></td><td class="text-right">1%</td></tr>
       <tr><td><a href="ApiController.php.html#14">Sparefoot\PitaService\Controller\ApiController</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="AffiliateController.php.html#12">Sparefoot\PitaService\Controller\AffiliateController</a></td><td class="text-right">4%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#14">Sparefoot\PitaService\Controller\AnalyticsController</a></td><td class="text-right">7%</td></tr>
       <tr><td><a href="BillingController.php.html#10">Sparefoot\PitaService\Controller\BillingController</a></td><td class="text-right">7%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#15">Sparefoot\PitaService\Controller\AbstractRestrictedController</a></td><td class="text-right">56%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="InventoryController.php.html#13">Sparefoot\PitaService\Controller\InventoryController</a></td><td class="text-right">440232</td></tr>
       <tr><td><a href="AccountController.php.html#11">Sparefoot\PitaService\Controller\AccountController</a></td><td class="text-right">113787</td></tr>
       <tr><td><a href="UserController.php.html#13">Sparefoot\PitaService\Controller\UserController</a></td><td class="text-right">46440</td></tr>
       <tr><td><a href="ToolsController.php.html#13">Sparefoot\PitaService\Controller\ToolsController</a></td><td class="text-right">30102</td></tr>
       <tr><td><a href="BookingsController.php.html#11">Sparefoot\PitaService\Controller\BookingsController</a></td><td class="text-right">16256</td></tr>
       <tr><td><a href="FacilityController.php.html#24">Sparefoot\PitaService\Controller\FacilityController</a></td><td class="text-right">16002</td></tr>
       <tr><td><a href="ApiController.php.html#14">Sparefoot\PitaService\Controller\ApiController</a></td><td class="text-right">13068</td></tr>
       <tr><td><a href="JobsController.php.html#17">Sparefoot\PitaService\Controller\JobsController</a></td><td class="text-right">6806</td></tr>
       <tr><td><a href="AnalyticsController.php.html#14">Sparefoot\PitaService\Controller\AnalyticsController</a></td><td class="text-right">6458</td></tr>
       <tr><td><a href="DisputesController.php.html#11">Sparefoot\PitaService\Controller\DisputesController</a></td><td class="text-right">3906</td></tr>
       <tr><td><a href="TableauController.php.html#13">Sparefoot\PitaService\Controller\TableauController</a></td><td class="text-right">3192</td></tr>
       <tr><td><a href="StatementsController.php.html#14">Sparefoot\PitaService\Controller\StatementsController</a></td><td class="text-right">2756</td></tr>
       <tr><td><a href="BuyoutController.php.html#11">Sparefoot\PitaService\Controller\BuyoutController</a></td><td class="text-right">2652</td></tr>
       <tr><td><a href="FiltersController.php.html#11">Sparefoot\PitaService\Controller\FiltersController</a></td><td class="text-right">2450</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#8">Sparefoot\PitaService\Controller\AbstractGenericController</a></td><td class="text-right">2256</td></tr>
       <tr><td><a href="YellowpagesController.php.html#9">Sparefoot\PitaService\Controller\YellowpagesController</a></td><td class="text-right">1806</td></tr>
       <tr><td><a href="SalesforceController.php.html#11">Sparefoot\PitaService\Controller\SalesforceController</a></td><td class="text-right">1560</td></tr>
       <tr><td><a href="AffiliateController.php.html#12">Sparefoot\PitaService\Controller\AffiliateController</a></td><td class="text-right">1278</td></tr>
       <tr><td><a href="QuickclientController.php.html#11">Sparefoot\PitaService\Controller\QuickclientController</a></td><td class="text-right">1260</td></tr>
       <tr><td><a href="FacilitiesController.php.html#10">Sparefoot\PitaService\Controller\FacilitiesController</a></td><td class="text-right">1056</td></tr>
       <tr><td><a href="IncentivesController.php.html#11">Sparefoot\PitaService\Controller\IncentivesController</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="TestController.php.html#10">Sparefoot\PitaService\Controller\TestController</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="ReviewsController.php.html#10">Sparefoot\PitaService\Controller\ReviewsController</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="OmnomController.php.html#9">Sparefoot\PitaService\Controller\OmnomController</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="ProxyController.php.html#16">Sparefoot\PitaService\Controller\ProxyController</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="QuickrepController.php.html#15">Sparefoot\PitaService\Controller\QuickrepController</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="SearchController.php.html#9">Sparefoot\PitaService\Controller\SearchController</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#9">Sparefoot\PitaService\Controller\CentaursearchapiController</a></td><td class="text-right">552</td></tr>
       <tr><td><a href="ReportsController.php.html#14">Sparefoot\PitaService\Controller\ReportsController</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="CdpController.php.html#9">Sparefoot\PitaService\Controller\CdpController</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="PaidmediaController.php.html#10">Sparefoot\PitaService\Controller\PaidmediaController</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#8">Sparefoot\PitaService\Controller\AbstractView\GenericView</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="PayoutController.php.html#12">Sparefoot\PitaService\Controller\PayoutController</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="PublicController.php.html#13">Sparefoot\PitaService\Controller\PublicController</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#10">Sparefoot\PitaService\Controller\QuicktaggerController</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="DashboardController.php.html#10">Sparefoot\PitaService\Controller\DashboardController</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="PitaSearchController.php.html#13">Sparefoot\PitaService\Controller\PitaSearchController</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="UtilitiesController.php.html#12">Sparefoot\PitaService\Controller\UtilitiesController</a></td><td class="text-right">306</td></tr>
       <tr><td><a href="IndexController.php.html#12">Sparefoot\PitaService\Controller\IndexController</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#15">Sparefoot\PitaService\Controller\AbstractRestrictedController</a></td><td class="text-right">243</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#11">Sparefoot\PitaService\Controller\AccountView\ListActionView</a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ServiceareaController.php.html#10">Sparefoot\PitaService\Controller\ServiceareaController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#9">Sparefoot\PitaService\Controller\SphinxsearchController</a></td><td class="text-right">210</td></tr>
       <tr><td><a href="LoginController.php.html#12">Sparefoot\PitaService\Controller\LoginController</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="DisastersController.php.html#9">Sparefoot\PitaService\Controller\DisastersController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="FeedsController.php.html#9">Sparefoot\PitaService\Controller\FeedsController</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="BillingController.php.html#10">Sparefoot\PitaService\Controller\BillingController</a></td><td class="text-right">146</td></tr>
       <tr><td><a href="RewardsController.php.html#9">Sparefoot\PitaService\Controller\RewardsController</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="NetsuiteController.php.html#10">Sparefoot\PitaService\Controller\NetsuiteController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="PhotosController.php.html#10">Sparefoot\PitaService\Controller\PhotosController</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="EmailController.php.html#9">Sparefoot\PitaService\Controller\EmailController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="FaqsController.php.html#10">Sparefoot\PitaService\Controller\FaqsController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="SalesController.php.html#9">Sparefoot\PitaService\Controller\SalesController</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ErrorController.php.html#10">Sparefoot\PitaService\Controller\ErrorController</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#9">Sparefoot\PitaService\Controller\MaintenanceModeController</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="QuicksoapController.php.html#16">Sparefoot\PitaService\Controller\QuicksoapController</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ReferralsController.php.html#9">Sparefoot\PitaService\Controller\ReferralsController</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#9">Sparefoot\PitaService\Controller\SoftwarepartnerController</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="FederatedController.php.html#11">Sparefoot\PitaService\Controller\FederatedController</a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AbstractGenericController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::getMetrics">getMetrics</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::getFilters">getFilters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::getCustoms">getCustoms</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::export">export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::_exportReport">_exportReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::_exportServerReport">_exportServerReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#133"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processQueryParams">processQueryParams</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#161"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::newBuilder">newBuilder</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#168"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::newQuery">newQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processQuery">processQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#188"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processReport">processReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#217"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::getDefaultQuery">getDefaultQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#222"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::tableFromReport">tableFromReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#245"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::initFilterList">initFilterList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#272"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::populateFilter">populateFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#300"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::toCamelCase">toCamelCase</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#111"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::setTitlePage">setTitlePage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#150"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::setCss">setCss</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#172"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::setScripts">setScripts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#311"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getLoggedUser">getLoggedUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#316"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getGenesisUserAccess">getGenesisUserAccess</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#321"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getParam">getParam</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#343"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::accessDeniedNotUse">accessDeniedNotUse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#356"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::accessDenied">accessDenied</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#364"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getTwig">getTwig</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#376"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::dispatchError">dispatchError</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#393"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::dispatchSuccess">dispatchSuccess</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#415"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getControllerActionName">getControllerActionName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#437"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getOldClass">getOldClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#448"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getNewClass">getNewClass</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#465"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::dbConnectionInstance">dbConnectionInstance</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#10"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::get_class">get_class</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#15"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::implode">implode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#21"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisUtilVersionerVersion">genesisUtilVersionerVersion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#27"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::strtotime">strtotime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#33"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::newDateTime">newDateTime</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#38"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::formatEstimatedMoney">formatEstimatedMoney</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#44"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisUtilFormatterFormatDateDiff">genesisUtilFormatterFormatDateDiff</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#60"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::getMetricInfoGetter">getMetricInfoGetter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#70"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisConfigServerIsProduction">genesisConfigServerIsProduction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#76"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisEntitySiteCommission">genesisEntitySiteCommission</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#95"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisConst">genesisConst</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#112"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::dateFormat">dateFormat</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#117"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::getEnv">getEnv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#123"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::serializeQuery">serializeQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#121"><abbr title="Sparefoot\PitaService\Controller\AccountController::_export">_export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\AccountController::moveintegrationAction">moveintegrationAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#271"><abbr title="Sparefoot\PitaService\Controller\AccountController::moveacctmgmtusersAction">moveacctmgmtusersAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#305"><abbr title="Sparefoot\PitaService\Controller\AccountController::syncAction">syncAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#369"><abbr title="Sparefoot\PitaService\Controller\AccountController::_resetSync">_resetSync</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#391"><abbr title="Sparefoot\PitaService\Controller\AccountController::changebidtypeAction">changebidtypeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#416"><abbr title="Sparefoot\PitaService\Controller\AccountController::changesupportexistingltvreservationsAction">changesupportexistingltvreservationsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#442"><abbr title="Sparefoot\PitaService\Controller\AccountController::changebidsAction">changebidsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#492"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeaccountminimumbidAction">changeaccountminimumbidAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#522"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeminimumbidsAction">changeminimumbidsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#559"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeexclusiveAction">changeexclusiveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#586"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeprimarycontactAction">changeprimarycontactAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#629"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeofflinereservationscdpAction">changeofflinereservationscdpAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#659"><abbr title="Sparefoot\PitaService\Controller\AccountController::verifyAction">verifyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#749"><abbr title="Sparefoot\PitaService\Controller\AccountController::getintegrationdetailsAction">getintegrationdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#789"><abbr title="Sparefoot\PitaService\Controller\AccountController::unverifyAction">unverifyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#809"><abbr title="Sparefoot\PitaService\Controller\AccountController::editinfoAction">editinfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#842"><abbr title="Sparefoot\PitaService\Controller\AccountController::getacctdetailsAction">getacctdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#905"><abbr title="Sparefoot\PitaService\Controller\AccountController::removeacctuserAction">removeacctuserAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#931"><abbr title="Sparefoot\PitaService\Controller\AccountController::removeadminAction">removeadminAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#955"><abbr title="Sparefoot\PitaService\Controller\AccountController::addacctadminAction">addacctadminAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#988"><abbr title="Sparefoot\PitaService\Controller\AccountController::newacctAction">newacctAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1253"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeUser">_makeUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1276"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeAcctMgmtUser">_makeAcctMgmtUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1288"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeIntegration">_makeIntegration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1490"><abbr title="Sparefoot\PitaService\Controller\AccountController::deleteaccountAction">deleteaccountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1529"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeintegrationcredsAction">changeintegrationcredsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1815"><abbr title="Sparefoot\PitaService\Controller\AccountController::getintegrationcredsAction">getintegrationcredsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1904"><abbr title="Sparefoot\PitaService\Controller\AccountController::geteditintegrationdetailsAction">geteditintegrationdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#1951"><abbr title="Sparefoot\PitaService\Controller\AccountController::saveeditintegrationdetailsAction">saveeditintegrationdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2001"><abbr title="Sparefoot\PitaService\Controller\AccountController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2026"><abbr title="Sparefoot\PitaService\Controller\AccountController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2053"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentitiesAction">getbillableentitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2126"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentitydetailsAction">getbillableentitydetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2174"><abbr title="Sparefoot\PitaService\Controller\AccountController::updateqbcustomerAction">updateqbcustomerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2270"><abbr title="Sparefoot\PitaService\Controller\AccountController::updatenscustomerAction">updatenscustomerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2401"><abbr title="Sparefoot\PitaService\Controller\AccountController::loadFacilityIdToFacilityMapForBillableEntity">loadFacilityIdToFacilityMapForBillableEntity</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2413"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentityfacilitiesAction">getbillableentityfacilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2468"><abbr title="Sparefoot\PitaService\Controller\AccountController::updateadnetworkpriceAction">updateadnetworkpriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2502"><abbr title="Sparefoot\PitaService\Controller\AccountController::updatehostedwebsitepriceAction">updatehostedwebsitepriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2537"><abbr title="Sparefoot\PitaService\Controller\AccountController::changedocusigncompleteAction">changedocusigncompleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2562"><abbr title="Sparefoot\PitaService\Controller\AccountController::changetestaccountAction">changetestaccountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountController.php.html#2582"><abbr title="Sparefoot\PitaService\Controller\AccountController::clearaccounttermsAction">clearaccounttermsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#20"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getBidTypeContinueResidualMessage">getBidTypeContinueResidualMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#37"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getAdminsHtml">getAdminsHtml</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#64"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::numCoporations">numCoporations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#76"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::numFacilities">numFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#88"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::bidCurrency">bidCurrency</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#105"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::labelText">labelText</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#132"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::formatDate">formatDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#146"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getPaymentTypes">getPaymentTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#28"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#36"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#44"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#64"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::generateapiAction">generateapiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#81"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_generateApiKey">_generateApiKey</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#86"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#108"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processCreate">_processCreate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#138"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::updateAction">updateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#173"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processUpdate">_processUpdate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#193"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::addsiteAction">addsiteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#215"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::updatesiteAction">updatesiteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#249"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::exclusionsAction">exclusionsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#287"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processSaveSite">_processSaveSite</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#321"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processAddExclusion">_processAddExclusion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AffiliateController.php.html#332"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processDelExclusion">_processDelExclusion</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#30"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::sqlAction">sqlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::savequeryAction">savequeryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#95"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::loadqueryAction">loadqueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#156"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_export">_export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#328"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_processQuery">_processQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#428"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_processReport">_processReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#456"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_buildSql">_buildSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#164"><abbr title="Sparefoot\PitaService\Controller\ApiController::exportunitapiAction">exportunitapiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#428"><abbr title="Sparefoot\PitaService\Controller\ApiController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#482"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSiteLinkFacilityData">_pullSiteLinkFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#495"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullQuikstorFacilityData">_pullQuikstorFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#507"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDoorswapFacilityData">_pullDoorswapFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#529"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStoredgeFacilityData">_pullStoredgeFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#536"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershiftFacilityData">_pullCentershiftFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#548"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStorageMartFacilityData">_pullStorageMartFacilityData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#610"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStorageMartUnitData">_pullStorageMartUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#658"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershiftUnitData">_pullCentershiftUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#684"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullUncleBobsUnitData">_pullUncleBobsUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#702"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershift4UnitData">_pullCentershift4UnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#732"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullUsiUnitData">_pullUsiUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#744"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCubesmartUnitData">_pullCubesmartUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#764"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSelfStorageManagerUnitData">_pullSelfStorageManagerUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#789"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStoredgeUnitData">_pullStoredgeUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#801"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDoorswapUnitData">_pullDoorswapUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#866"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullExtraspaceUnits">_pullExtraspaceUnits</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#893"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSiteLinkUnitData">_pullSiteLinkUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#918"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullQuickstorUnitData">_pullQuickstorUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApiController.php.html#944"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDomicoUnitData">_pullDomicoUnitData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\BillingController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\BillingController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#32"><abbr title="Sparefoot\PitaService\Controller\BillingController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#48"><abbr title="Sparefoot\PitaService\Controller\BillingController::exportemailsAction">exportemailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#93"><abbr title="Sparefoot\PitaService\Controller\BillingController::closeAction">closeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#108"><abbr title="Sparefoot\PitaService\Controller\BillingController::deleteAction">deleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingController.php.html#124"><abbr title="Sparefoot\PitaService\Controller\BillingController::_runQuery">_runQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\BookingsController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\BookingsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#49"><abbr title="Sparefoot\PitaService\Controller\BookingsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#107"><abbr title="Sparefoot\PitaService\Controller\BookingsController::confcodeAction">confcodeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#129"><abbr title="Sparefoot\PitaService\Controller\BookingsController::salesforceAction">salesforceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#185"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getqueryAction">getqueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\BookingsController::historyAction">historyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#217"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getbookinginfoAction">getbookinginfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#296"><abbr title="Sparefoot\PitaService\Controller\BookingsController::jsondetailsAction">jsondetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#336"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changemoveinAction">changemoveinAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#364"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changeunitpriceAction">changeunitpriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#383"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changemoveoutAction">changemoveoutAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#408"><abbr title="Sparefoot\PitaService\Controller\BookingsController::addduplicateAction">addduplicateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#468"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changebookingsAction">changebookingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#543"><abbr title="Sparefoot\PitaService\Controller\BookingsController::_changeBookingState">_changeBookingState</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#609"><abbr title="Sparefoot\PitaService\Controller\BookingsController::makebookingresidualAction">makebookingresidualAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#632"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#856"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getunitinfoAction">getunitinfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#894"><abbr title="Sparefoot\PitaService\Controller\BookingsController::previewemailsAction">previewemailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#935"><abbr title="Sparefoot\PitaService\Controller\BookingsController::resendemailsAction">resendemailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#975"><abbr title="Sparefoot\PitaService\Controller\BookingsController::moreActionsAction">moreActionsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsController.php.html#990"><abbr title="Sparefoot\PitaService\Controller\BookingsController::sitelinkledgerAction">sitelinkledgerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#41"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#49"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#68"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editAction">editAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#164"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::viewAction">viewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#185"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::invoiceAction">invoiceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#212"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::deleteAction">deleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#235"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::downloadAction">downloadAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#280"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editUpdateAction">editUpdateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#308"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editUpdateQuoteAction">editUpdateQuoteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#319"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editBookingsAction">editBookingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#340"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_updatePendingBookings">_updatePendingBookings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BuyoutController.php.html#378"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_updateExistingTenants">_updateExistingTenants</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\CdpController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\CdpController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#33"><abbr title="Sparefoot\PitaService\Controller\CdpController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchRunsAction">matchRunsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchAccountsAction">matchAccountsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#95"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchFacilitiesAction">matchFacilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#133"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchBookingsAction">matchBookingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#159"><abbr title="Sparefoot\PitaService\Controller\CdpController::killMatchRunAction">killMatchRunAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#185"><abbr title="Sparefoot\PitaService\Controller\CdpController::viewLogAction">viewLogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpController.php.html#213"><abbr title="Sparefoot\PitaService\Controller\CdpController::runCdpAction">runCdpAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchController.php.html#10"><abbr title="Sparefoot\PitaService\Controller\CentaursearchController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_getApiResponse">_getApiResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_getApiResponseTest">_getApiResponseTest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#97"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#113"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::testAction">testAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#212"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_initWeights">_initWeights</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#222"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\CitygridController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\CitygridController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\CitygridController::resyncAction">resyncAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\CitygridController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CitygridController.php.html#46"><abbr title="Sparefoot\PitaService\Controller\CitygridController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#38"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutController::dashboardAction">dashboardAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CpaPercentRolloutView/IndexActionView.php.html#13"><abbr title="Sparefoot\PitaService\Controller\CpaPercentRolloutView\IndexActionView::usortReports">usortReports</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\DashboardController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\DashboardController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\DashboardController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#43"><abbr title="Sparefoot\PitaService\Controller\DashboardController::_loadData">_loadData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\DashboardController::initDashboardLayout">initDashboardLayout</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#113"><abbr title="Sparefoot\PitaService\Controller\DashboardController::bookingsAction">bookingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\DashboardController::initDataForBookingsView">initDataForBookingsView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DashboardController.php.html#185"><abbr title="Sparefoot\PitaService\Controller\DashboardController::initDailyBookingsProjection">initDailyBookingsProjection</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\DisastersController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\DisastersController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\DisastersController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\DisastersController::includeContainedFacilities">includeContainedFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\DisastersController::addDisasterAction">addDisasterAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisastersController.php.html#130"><abbr title="Sparefoot\PitaService\Controller\DisastersController::editDisasterAction">editDisasterAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\DisputesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#45"><abbr title="Sparefoot\PitaService\Controller\DisputesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\DisputesController::opendisputesAction">opendisputesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#93"><abbr title="Sparefoot\PitaService\Controller\DisputesController::closeddisputesAction">closeddisputesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#127"><abbr title="Sparefoot\PitaService\Controller\DisputesController::reviewdisputeAction">reviewdisputeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#149"><abbr title="Sparefoot\PitaService\Controller\DisputesController::applyRuling">applyRuling</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#214"><abbr title="Sparefoot\PitaService\Controller\DisputesController::validateBookingIsReviewable">validateBookingIsReviewable</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#239"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeRuling">updateDisputeRuling</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#268"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewRulingReason">updateDisputeReviewRulingReason</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#284"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewNotes">updateDisputeReviewNotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#300"><abbr title="Sparefoot\PitaService\Controller\DisputesController::deleteDisputeReviewNotes">deleteDisputeReviewNotes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#309"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewAgent">updateDisputeReviewAgent</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#333"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_exportOpenDisputes">_exportOpenDisputes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#346"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_exportClosedDisputes">_exportClosedDisputes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#359"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_createCsv">_createCsv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#375"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_getCsvHeaders">_getCsvHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#418"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_getCsvLine">_getCsvLine</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#476"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_cleanCsvLine">_cleanCsvLine</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#485"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_getDefaultQuery">_getDefaultQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#492"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_processQuery">_processQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#501"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_processOpenDisputesReport">_processOpenDisputesReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DisputesController.php.html#512"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_processClosedDisputesReport">_processClosedDisputesReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\EmailController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\EmailController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#49"><abbr title="Sparefoot\PitaService\Controller\EmailController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#68"><abbr title="Sparefoot\PitaService\Controller\EmailController::ajaxemailsAction">ajaxemailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#83"><abbr title="Sparefoot\PitaService\Controller\EmailController::ajaxsendAction">ajaxsendAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailController.php.html#111"><abbr title="Sparefoot\PitaService\Controller\EmailController::_getGreenArrowAddresses">_getGreenArrowAddresses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ErrorController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\ErrorController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ErrorController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\ErrorController::errorAction">errorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ErrorController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\ErrorController::hipchatAction">hipchatAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#32"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#117"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::bysourceAction">bysourceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::getqueryAction">getqueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::loadbyidAction">loadbyidAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#30"><abbr title="Sparefoot\PitaService\Controller\FacilityController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\FacilityController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#147"><abbr title="Sparefoot\PitaService\Controller\FacilityController::acquiretwilionumberAction">acquiretwilionumberAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#190"><abbr title="Sparefoot\PitaService\Controller\FacilityController::createJsonResponse">createJsonResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#202"><abbr title="Sparefoot\PitaService\Controller\FacilityController::releasetwilionumberAction">releasetwilionumberAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#239"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_fetchInventoryData">_fetchInventoryData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#274"><abbr title="Sparefoot\PitaService\Controller\FacilityController::jsonunitlistdetailsAction">jsonunitlistdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#324"><abbr title="Sparefoot\PitaService\Controller\FacilityController::jsonunitdetailsAction">jsonunitdetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#393"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_loadFacilities">_loadFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#540"><abbr title="Sparefoot\PitaService\Controller\FacilityController::getqueryAction">getqueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#558"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_tableFromReport">_tableFromReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#644"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_initFilter">_initFilter</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#692"><abbr title="Sparefoot\PitaService\Controller\FacilityController::directoryAction">directoryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#707"><abbr title="Sparefoot\PitaService\Controller\FacilityController::activateAction">activateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#734"><abbr title="Sparefoot\PitaService\Controller\FacilityController::deactivateAction">deactivateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#761"><abbr title="Sparefoot\PitaService\Controller\FacilityController::exportdirectoryrecordingsAction">exportdirectoryrecordingsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#825"><abbr title="Sparefoot\PitaService\Controller\FacilityController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityController.php.html#840"><abbr title="Sparefoot\PitaService\Controller\FacilityController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\FaqsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\FaqsController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#32"><abbr title="Sparefoot\PitaService\Controller\FaqsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\FaqsController::getAction">getAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FaqsController.php.html#67"><abbr title="Sparefoot\PitaService\Controller\FaqsController::saveAction">saveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FederatedController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\FederatedController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FederatedController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\FederatedController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FederatedController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\FederatedController::awsAction">awsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#11"><abbr title="Sparefoot\PitaService\Controller\FeedsController::citysearchAction">citysearchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#50"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_prepHours">_prepHours</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#68"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_facilityDetailsUrl">_facilityDetailsUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#88"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_prepUrl">_prepUrl</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeedsController.php.html#93"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_urlTitle">_urlTitle</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FiltersController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\FiltersController::loadAction">loadAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HeatmapController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\HeatmapController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HeatmapController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\HeatmapController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#28"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#36"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::_init">_init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#54"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::redemptionRequestsAction">redemptionRequestsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#88"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::updateStatusAction">updateStatusAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#101"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::parseIncentiveResponse">parseIncentiveResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#116"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getRedemptionRequests">getRedemptionRequests</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#129"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getRedemptionRequest">getRedemptionRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#139"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getOffer">getOffer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#149"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getIncentive">getIncentive</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#159"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::setStatus">setStatus</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#172"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::addIncentiveAndBookingDataToView">addIncentiveAndBookingDataToView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::searchRequestsAction">searchRequestsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IncentivesController.php.html#234"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::getRedemptionRequestsAction">getRedemptionRequestsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\IndexController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\IndexController::_getQuickRepContainer">_getQuickRepContainer</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#30"><abbr title="Sparefoot\PitaService\Controller\IndexController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\IndexController::oldIndexAction">oldIndexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#65"><abbr title="Sparefoot\PitaService\Controller\IndexController::fatalerrorAction">fatalerrorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#75"><abbr title="Sparefoot\PitaService\Controller\IndexController::exceptionAction">exceptionAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#92"><abbr title="Sparefoot\PitaService\Controller\IndexController::addBookmarkAction">addBookmarkAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#129"><abbr title="Sparefoot\PitaService\Controller\IndexController::updateBookmarksAction">updateBookmarksAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#166"><abbr title="Sparefoot\PitaService\Controller\IndexController::_loadTimespanBookingData">_loadTimespanBookingData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexController.php.html#229"><abbr title="Sparefoot\PitaService\Controller\IndexController::auditAction">auditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexView/IndexActionView.php.html#12"><abbr title="Sparefoot\PitaService\Controller\IndexView\IndexActionView::usortReports">usortReports</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\InventoryController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#64"><abbr title="Sparefoot\PitaService\Controller\InventoryController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#98"><abbr title="Sparefoot\PitaService\Controller\InventoryController::mainAction">mainAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#108"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadpercentcompleteAction">loadpercentcompleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#138"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadfacilitiesAction">loadfacilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#285"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_getFacilitiesInDeletedAccounts">_getFacilitiesInDeletedAccounts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#328"><abbr title="Sparefoot\PitaService\Controller\InventoryController::accountlistAction">accountlistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#360"><abbr title="Sparefoot\PitaService\Controller\InventoryController::productlistAction">productlistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#412"><abbr title="Sparefoot\PitaService\Controller\InventoryController::toggleproductAction">toggleproductAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#484"><abbr title="Sparefoot\PitaService\Controller\InventoryController::togglehostedwebsitetypeAction">togglehostedwebsitetypeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#515"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateadnetworkpriceAction">updateadnetworkpriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#568"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatehostedwebsitepriceAction">updatehostedwebsitepriceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#621"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatetenantconnectprecalldigitsAction">updatetenantconnectprecalldigitsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#646"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadinfoAction">loadinfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#777"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadresaleratesAction">loadresaleratesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#817"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateresalebucketAction">updateresalebucketAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#839"><abbr title="Sparefoot\PitaService\Controller\InventoryController::deleteresalebucketAction">deleteresalebucketAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#861"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addupdateresalebucketAction">addupdateresalebucketAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#892"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unithtmlAction">unithtmlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#931"><abbr title="Sparefoot\PitaService\Controller\InventoryController::approveAction">approveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#980"><abbr title="Sparefoot\PitaService\Controller\InventoryController::facilityapproveAction">facilityapproveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1010"><abbr title="Sparefoot\PitaService\Controller\InventoryController::jsondetailsAction">jsondetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1371"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatecustomclosureAction">updatecustomclosureAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1397"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removecustomclosureAction">removecustomclosureAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1416"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatedetailsAction">updatedetailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#1857"><abbr title="Sparefoot\PitaService\Controller\InventoryController::multiplefacilityupdateAction">multiplefacilityupdateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2047"><abbr title="Sparefoot\PitaService\Controller\InventoryController::billableentitylistAction">billableentitylistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2066"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_makeBillableEntityList">_makeBillableEntityList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2083"><abbr title="Sparefoot\PitaService\Controller\InventoryController::streetviewAction">streetviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2119"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatestreetviewAction">updatestreetviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2157"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadlogAction">loadlogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2178"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unitAction">unitAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2467"><abbr title="Sparefoot\PitaService\Controller\InventoryController::photoframeAction">photoframeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2529"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getyieldavgsAction">getyieldavgsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2547"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadyieldjsonAction">loadyieldjsonAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2567"><abbr title="Sparefoot\PitaService\Controller\InventoryController::saveyieldAction">saveyieldAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2614"><abbr title="Sparefoot\PitaService\Controller\InventoryController::visibilityAction">visibilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2631"><abbr title="Sparefoot\PitaService\Controller\InventoryController::amenitiescheckAction">amenitiescheckAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2722"><abbr title="Sparefoot\PitaService\Controller\InventoryController::visibilitycheckAction">visibilitycheckAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2786"><abbr title="Sparefoot\PitaService\Controller\InventoryController::acctfaclistAction">acctfaclistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2875"><abbr title="Sparefoot\PitaService\Controller\InventoryController::syncAction">syncAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2901"><abbr title="Sparefoot\PitaService\Controller\InventoryController::phidosyncAction">phidosyncAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2923"><abbr title="Sparefoot\PitaService\Controller\InventoryController::refreshsearchAction">refreshsearchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2929"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_refreshSearch">_refreshSearch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#2985"><abbr title="Sparefoot\PitaService\Controller\InventoryController::resetlocationAction">resetlocationAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3011"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unitexportAction">unitexportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3058"><abbr title="Sparefoot\PitaService\Controller\InventoryController::logexportAction">logexportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3089"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_fetchLogData">_fetchLogData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3119"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_fetchInventoryData">_fetchInventoryData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3155"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatefacilityAction">updatefacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3192"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addfacilitycontactAction">addfacilitycontactAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3234"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removefacilitycontactAction">removefacilitycontactAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3266"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateinventoryAction">updateinventoryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3338"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getaccessAction">getaccessAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3419"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addaccessAction">addaccessAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3445"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removeaccessAction">removeaccessAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3469"><abbr title="Sparefoot\PitaService\Controller\InventoryController::movefacilityAction">movefacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3508"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getsitelinkfacilitylistAction">getsitelinkfacilitylistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3539"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getselfstoragemanagerfacilitylistAction">getselfstoragemanagerfacilitylistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3570"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getcentershiftfacilitylistAction">getcentershiftfacilitylistAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3602"><abbr title="Sparefoot\PitaService\Controller\InventoryController::syncselectedfacsAction">syncselectedfacsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3665"><abbr title="Sparefoot\PitaService\Controller\InventoryController::copyFacilityDataAction">copyFacilityDataAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3895"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadDuplicateReviews">loadDuplicateReviews</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3907"><abbr title="Sparefoot\PitaService\Controller\InventoryController::copyReviewToFacility">copyReviewToFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3947"><abbr title="Sparefoot\PitaService\Controller\InventoryController::integrationsByAccountAction">integrationsByAccountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#3977"><abbr title="Sparefoot\PitaService\Controller\InventoryController::facilitiesByIntegrationAction">facilitiesByIntegrationAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#4006"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getfacilityAction">getfacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#4036"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essfacilitylookupAction">essfacilitylookupAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#4057"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essConvertFromManualAction">essConvertFromManualAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryController.php.html#4106"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essConvertToManualAction">essConvertToManualAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\JobsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\JobsController::summaryAction">summaryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#113"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandAction">commandAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#131"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandAddAction">commandAddAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#153"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandEditAction">commandEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandLogAction">commandLogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#204"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandSaveAction">commandSaveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#229"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandDeleteAction">commandDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#252"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledAction">scheduledAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#317"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledEditAction">scheduledEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#344"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledAddAction">scheduledAddAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#373"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledDeleteAction">scheduledDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#390"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledSaveAction">scheduledSaveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#418"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocChooseAction">adhocChooseAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#439"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocLogAction">adhocLogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#466"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocAddAction">adhocAddAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#492"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocDeleteAction">adhocDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#509"><abbr title="Sparefoot\PitaService\Controller\JobsController::unlockAction">unlockAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#526"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerZombieAction">workerZombieAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#576"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerLogAction">workerLogAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#600"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerInfoAction">workerInfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#637"><abbr title="Sparefoot\PitaService\Controller\JobsController::childAction">childAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#663"><abbr title="Sparefoot\PitaService\Controller\JobsController::childAddAction">childAddAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#683"><abbr title="Sparefoot\PitaService\Controller\JobsController::childEditAction">childEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#704"><abbr title="Sparefoot\PitaService\Controller\JobsController::childUpdateAction">childUpdateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#731"><abbr title="Sparefoot\PitaService\Controller\JobsController::childSaveAction">childSaveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#754"><abbr title="Sparefoot\PitaService\Controller\JobsController::childDeleteAction">childDeleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#767"><abbr title="Sparefoot\PitaService\Controller\JobsController::colDisplay">colDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#772"><abbr title="Sparefoot\PitaService\Controller\JobsController::finishCode">finishCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#786"><abbr title="Sparefoot\PitaService\Controller\JobsController::successCode">successCode</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#801"><abbr title="Sparefoot\PitaService\Controller\JobsController::timeDisplay">timeDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#832"><abbr title="Sparefoot\PitaService\Controller\JobsController::dateDisplay">dateDisplay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="JobsController.php.html#839"><abbr title="Sparefoot\PitaService\Controller\JobsController::_verifyChildAllowed">_verifyChildAllowed</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\LoginController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\LoginController::check">check</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#67"><abbr title="Sparefoot\PitaService\Controller\LoginController::getCurrentUser">getCurrentUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\LoginController::logout">logout</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#92"><abbr title="Sparefoot\PitaService\Controller\LoginController::amILoggedIn">amILoggedIn</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LoginController.php.html#98"><abbr title="Sparefoot\PitaService\Controller\LoginController::isUserGod">isUserGod</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\MaintenanceModeController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\MaintenanceModeController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#71"><abbr title="Sparefoot\PitaService\Controller\MaintenanceModeController::setFlagAction">setFlagAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\MaintenanceModeController::_sendMaintenanceModeEmail">_sendMaintenanceModeEmail</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NetsuiteController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\NetsuiteController::accountAction">accountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NetsuiteController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\NetsuiteController::statusAction">statusAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\OmnomController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\OmnomController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#60"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByJob">_queryStatusByJob</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#107"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByIntegration">_queryStatusByIntegration</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#166"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByFacility">_queryStatusByFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#219"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getJobs">_getJobs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#239"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getIntegrations">_getIntegrations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OmnomController.php.html#259"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getAccounts">_getAccounts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#36"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#46"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::paidmediaAction">paidmediaAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#62"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::managepaidmediaAction">managepaidmediaAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::modifypaidmediaAction">modifypaidmediaAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#107"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::ajaxcreatepaidmediaupdateAction">ajaxcreatepaidmediaupdateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#122"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::getRuleengineList">getRuleengineList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PaidmediaController.php.html#147"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::getCampaignList">getCampaignList</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\PayoutController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#38"><abbr title="Sparefoot\PitaService\Controller\PayoutController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#48"><abbr title="Sparefoot\PitaService\Controller\PayoutController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#61"><abbr title="Sparefoot\PitaService\Controller\PayoutController::previewAction">previewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#81"><abbr title="Sparefoot\PitaService\Controller\PayoutController::detailsAction">detailsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#97"><abbr title="Sparefoot\PitaService\Controller\PayoutController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\PayoutController::pdfAction">pdfAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#137"><abbr title="Sparefoot\PitaService\Controller\PayoutController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#163"><abbr title="Sparefoot\PitaService\Controller\PayoutController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#191"><abbr title="Sparefoot\PitaService\Controller\PayoutController::_assignBookings">_assignBookings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#209"><abbr title="Sparefoot\PitaService\Controller\PayoutController::_preparePayoutQuery">_preparePayoutQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PayoutController.php.html#221"><abbr title="Sparefoot\PitaService\Controller\PayoutController::_prepareDetails">_prepareDetails</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PhotosController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\PhotosController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PhotosController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\PhotosController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PhotosController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\PhotosController::approveAction">approveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PingController.php.html#51"><abbr title="Sparefoot\PitaService\Controller\PingController::indexAction2">indexAction2</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PitaSearchController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PitaSearchController.php.html#30"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PitaSearchController.php.html#82"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::queryAction">queryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\ProxyController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\ProxyController::loadAction">loadAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#89"><abbr title="Sparefoot\PitaService\Controller\ProxyController::AuthenticateProxyAuthorization">AuthenticateProxyAuthorization</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#110"><abbr title="Sparefoot\PitaService\Controller\ProxyController::proxyRequest">proxyRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#153"><abbr title="Sparefoot\PitaService\Controller\ProxyController::infoAction">infoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ProxyController.php.html#167"><abbr title="Sparefoot\PitaService\Controller\ProxyController::debugAction">debugAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\PublicController::searchServicesAction">searchServicesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\PublicController::bookUnitAction">bookUnitAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#122"><abbr title="Sparefoot\PitaService\Controller\PublicController::searchAction">searchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PublicController.php.html#147"><abbr title="Sparefoot\PitaService\Controller\PublicController::hourlyAlertsAction">hourlyAlertsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#28"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#38"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#76"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#110"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::paramsAction">paramsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#126"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::facilityAction">facilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#143"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#171"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::_facSort">_facSort</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::unitsAction">unitsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#208"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::unitAction">unitAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#224"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::executeAction">executeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickclientController.php.html#284"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::toArray">toArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\QuickformController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\QuickformController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#36"><abbr title="Sparefoot\PitaService\Controller\QuickformController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickformController.php.html#48"><abbr title="Sparefoot\PitaService\Controller\QuickformController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickjobController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\QuickjobController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickjobController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\QuickjobController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickjobController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\QuickjobController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#45"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#55"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::renderdashAction">renderdashAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#65"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#75"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::updateinputsAction">updateinputsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#97"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::tableAction">tableAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#124"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::chartAction">chartAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#154"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::sqlAction">sqlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#174"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::exportAction">exportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuickrepController.php.html#195"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::hourlyAlertsAction">hourlyAlertsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::wsdlAction">wsdlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#50"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::handleSoapRequest">handleSoapRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicksoapController.php.html#77"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::generateWsdlResponse">generateWsdlResponse</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::integrationAction">integrationAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::saveorderAction">saveorderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#125"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::savenewAction">savenewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#158"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::saveexistingAction">saveexistingAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#204"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::removeAction">removeAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::toggleconvertedAction">toggleconvertedAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#59"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::toggleclaimedAction">toggleclaimedAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReferralsController.php.html#78"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::savenotesAction">savenotesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\ReportsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#43"><abbr title="Sparefoot\PitaService\Controller\ReportsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#66"><abbr title="Sparefoot\PitaService\Controller\ReportsController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#128"><abbr title="Sparefoot\PitaService\Controller\ReportsController::updateInputsAction">updateInputsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#152"><abbr title="Sparefoot\PitaService\Controller\ReportsController::tableAction">tableAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#179"><abbr title="Sparefoot\PitaService\Controller\ReportsController::sqlAction">sqlAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReportsController.php.html#198"><abbr title="Sparefoot\PitaService\Controller\ReportsController::exportAction">exportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#84"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::getQueryAction">getQueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#102"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::deleteReviewAction">deleteReviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#126"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::getReviewAction">getReviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#156"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::saveReviewAction">saveReviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#200"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::approvalsAction">approvalsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#213"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::setStatusAction">setStatusAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsController.php.html#244"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::editReviewMessageAction">editReviewMessageAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\RewardsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\RewardsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\RewardsController::newkiindAction">newkiindAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#84"><abbr title="Sparefoot\PitaService\Controller\RewardsController::reviewkiindAction">reviewkiindAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#96"><abbr title="Sparefoot\PitaService\Controller\RewardsController::browsekiindAction">browsekiindAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RewardsController.php.html#114"><abbr title="Sparefoot\PitaService\Controller\RewardsController::listAction">listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\SalesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\SalesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\SalesController::searchAction">searchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#54"><abbr title="Sparefoot\PitaService\Controller\SalesController::_queryData">_queryData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#67"><abbr title="Sparefoot\PitaService\Controller\SalesController::_getSearchDataByZip">_getSearchDataByZip</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#130"><abbr title="Sparefoot\PitaService\Controller\SalesController::_getSearchData">_getSearchData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#148"><abbr title="Sparefoot\PitaService\Controller\SalesController::_getClickData">_getClickData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesController.php.html#170"><abbr title="Sparefoot\PitaService\Controller\SalesController::_getReservationData">_getReservationData</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesforceController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\SalesforceController::addAccountAction">addAccountAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SalesforceController.php.html#197"><abbr title="Sparefoot\PitaService\Controller\SalesforceController::_createManualFacilities">_createManualFacilities</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\SearchController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\SearchController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\SearchController::testAction">testAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#144"><abbr title="Sparefoot\PitaService\Controller\SearchController::distanceAction">distanceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#205"><abbr title="Sparefoot\PitaService\Controller\SearchController::_initWeights">_initWeights</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SearchController.php.html#225"><abbr title="Sparefoot\PitaService\Controller\SearchController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#55"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::getAction">getAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#87"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::saveAction">saveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#115"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::convexhullAction">convexhullAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::simplifyAction">simplifyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#167"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::getServiceAreaWktByFacilityId">getServiceAreaWktByFacilityId</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ServiceareaController.php.html#177"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::postLocationService">postLocationService</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\SoftwarepartnerController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\SoftwarepartnerController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#15"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::testAction">testAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#66"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::distanceAction">distanceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#124"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::_initWeights">_initWeights</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#144"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#20"><abbr title="Sparefoot\PitaService\Controller\StatementsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#34"><abbr title="Sparefoot\PitaService\Controller\StatementsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#51"><abbr title="Sparefoot\PitaService\Controller\StatementsController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#84"><abbr title="Sparefoot\PitaService\Controller\StatementsController::generateAction">generateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#123"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_export">_export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#153"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_exportInvoice">_exportInvoice</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#245"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_processReport">_processReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#265"><abbr title="Sparefoot\PitaService\Controller\StatementsController::cancelInvoiceAction">cancelInvoiceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#289"><abbr title="Sparefoot\PitaService\Controller\StatementsController::invoiceAction">invoiceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#357"><abbr title="Sparefoot\PitaService\Controller\StatementsController::pdfAction">pdfAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#382"><abbr title="Sparefoot\PitaService\Controller\StatementsController::emailpreviewAction">emailpreviewAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementsController.php.html#430"><abbr title="Sparefoot\PitaService\Controller\StatementsController::getInvoiceAction">getInvoiceAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\TableauController::setErrorMessage">setErrorMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\TableauController::setSuccessMessage">setSuccessMessage</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\TableauController::initView">initView</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#59"><abbr title="Sparefoot\PitaService\Controller\TableauController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\TableauController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#105"><abbr title="Sparefoot\PitaService\Controller\TableauController::addAction">addAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#118"><abbr title="Sparefoot\PitaService\Controller\TableauController::editAction">editAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#140"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveAction">saveAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\TableauController::deleteAction">deleteAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#198"><abbr title="Sparefoot\PitaService\Controller\TableauController::manageKeysAction">manageKeysAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#211"><abbr title="Sparefoot\PitaService\Controller\TableauController::editKeyAction">editKeyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#233"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveKeyAction">saveKeyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#270"><abbr title="Sparefoot\PitaService\Controller\TableauController::deleteKeyAction">deleteKeyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#297"><abbr title="Sparefoot\PitaService\Controller\TableauController::massEditKeyAction">massEditKeyAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#322"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveMassEditAction">saveMassEditAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#371"><abbr title="Sparefoot\PitaService\Controller\TableauController::manageTeamsAction">manageTeamsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TableauController.php.html#384"><abbr title="Sparefoot\PitaService\Controller\TableauController::addTeamAction">addTeamAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\TestController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#28"><abbr title="Sparefoot\PitaService\Controller\TestController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#38"><abbr title="Sparefoot\PitaService\Controller\TestController::testsAction">testsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#63"><abbr title="Sparefoot\PitaService\Controller\TestController::manageAction">manageAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#91"><abbr title="Sparefoot\PitaService\Controller\TestController::manageuiAction">manageuiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\TestController::stopAction">stopAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#131"><abbr title="Sparefoot\PitaService\Controller\TestController::stopuiAction">stopuiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#145"><abbr title="Sparefoot\PitaService\Controller\TestController::modifytestAction">modifytestAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\TestController::ajaxcreatesearchAction">ajaxcreatesearchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#207"><abbr title="Sparefoot\PitaService\Controller\TestController::ajaxcreateuiAction">ajaxcreateuiAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#231"><abbr title="Sparefoot\PitaService\Controller\TestController::_saveWeight">_saveWeight</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestController.php.html#245"><abbr title="Sparefoot\PitaService\Controller\TestController::_saveTestVariation">_saveTestVariation</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\ToolsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\ToolsController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#107"><abbr title="Sparefoot\PitaService\Controller\ToolsController::urlBuilderAction">urlBuilderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\ToolsController::perfectPhotosSelectorAction">perfectPhotosSelectorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#140"><abbr title="Sparefoot\PitaService\Controller\ToolsController::perfectPhotosSelectorPostAction">perfectPhotosSelectorPostAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#173"><abbr title="Sparefoot\PitaService\Controller\ToolsController::noEmailListAction">noEmailListAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#265"><abbr title="Sparefoot\PitaService\Controller\ToolsController::searchKeyChangerAction">searchKeyChangerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#390"><abbr title="Sparefoot\PitaService\Controller\ToolsController::twilioReportsAction">twilioReportsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#476"><abbr title="Sparefoot\PitaService\Controller\ToolsController::createTwilioNumberAction">createTwilioNumberAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#545"><abbr title="Sparefoot\PitaService\Controller\ToolsController::purchaseTwilioNumbersAction">purchaseTwilioNumbersAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#614"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentEditorAction">siteContentEditorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#706"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentCreatorAction">siteContentCreatorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#837"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentImportAction">siteContentImportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#924"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentCreatorImportAction">siteContentCreatorImportAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1055"><abbr title="Sparefoot\PitaService\Controller\ToolsController::accountingMapAction">accountingMapAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1160"><abbr title="Sparefoot\PitaService\Controller\ToolsController::cabinetAction">cabinetAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1188"><abbr title="Sparefoot\PitaService\Controller\ToolsController::featureFlagsAction">featureFlagsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1241"><abbr title="Sparefoot\PitaService\Controller\ToolsController::handleFeatureFlagSave">handleFeatureFlagSave</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1287"><abbr title="Sparefoot\PitaService\Controller\ToolsController::addFeatureFlagAction">addFeatureFlagAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1305"><abbr title="Sparefoot\PitaService\Controller\ToolsController::deleteFeatureFlagAction">deleteFeatureFlagAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1323"><abbr title="Sparefoot\PitaService\Controller\ToolsController::pressPageAction">pressPageAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1350"><abbr title="Sparefoot\PitaService\Controller\ToolsController::pressPageEditorAction">pressPageEditorAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1379"><abbr title="Sparefoot\PitaService\Controller\ToolsController::platformSearchAction">platformSearchAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ToolsController.php.html#1389"><abbr title="Sparefoot\PitaService\Controller\ToolsController::platformQueryAction">platformQueryAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TriController.php.html#17"><abbr title="Sparefoot\PitaService\Controller\TriController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TriController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\TriController::index">index</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TriController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\TriController::fortuneAction">fortuneAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\UserController::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\UserController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#65"><abbr title="Sparefoot\PitaService\Controller\UserController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#73"><abbr title="Sparefoot\PitaService\Controller\UserController::_listAction">_listAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#120"><abbr title="Sparefoot\PitaService\Controller\UserController::myfootAction">myfootAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#128"><abbr title="Sparefoot\PitaService\Controller\UserController::pitaAction">pitaAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#136"><abbr title="Sparefoot\PitaService\Controller\UserController::portalAction">portalAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#144"><abbr title="Sparefoot\PitaService\Controller\UserController::loadusersAction">loadusersAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#202"><abbr title="Sparefoot\PitaService\Controller\UserController::editinfoAction">editinfoAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#252"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxcreateAction">ajaxcreateAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#291"><abbr title="Sparefoot\PitaService\Controller\UserController::_createPitaUser">_createPitaUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#330"><abbr title="Sparefoot\PitaService\Controller\UserController::_createPortalUser">_createPortalUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#359"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxcreatemyfootAction">ajaxcreatemyfootAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#452"><abbr title="Sparefoot\PitaService\Controller\UserController::isEmailValid">isEmailValid</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#463"><abbr title="Sparefoot\PitaService\Controller\UserController::_restrictFacilityAccess">_restrictFacilityAccess</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#473"><abbr title="Sparefoot\PitaService\Controller\UserController::_addFacilityContact">_addFacilityContact</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#490"><abbr title="Sparefoot\PitaService\Controller\UserController::_buildUser">_buildUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#519"><abbr title="Sparefoot\PitaService\Controller\UserController::createAction">createAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#587"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxpasswordAction">ajaxpasswordAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#618"><abbr title="Sparefoot\PitaService\Controller\UserController::passwordAction">passwordAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#652"><abbr title="Sparefoot\PitaService\Controller\UserController::changeattributesAction">changeattributesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#722"><abbr title="Sparefoot\PitaService\Controller\UserController::addstmtrecipientsAction">addstmtrecipientsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#756"><abbr title="Sparefoot\PitaService\Controller\UserController::removestmtrecipientsAction">removestmtrecipientsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#790"><abbr title="Sparefoot\PitaService\Controller\UserController::_export">_export</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#866"><abbr title="Sparefoot\PitaService\Controller\UserController::_emailRecipientExport">_emailRecipientExport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#934"><abbr title="Sparefoot\PitaService\Controller\UserController::_getDefaultQuery">_getDefaultQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#941"><abbr title="Sparefoot\PitaService\Controller\UserController::_processQuery">_processQuery</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#961"><abbr title="Sparefoot\PitaService\Controller\UserController::_processReport">_processReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#974"><abbr title="Sparefoot\PitaService\Controller\UserController::_processEmailRecipientReport">_processEmailRecipientReport</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1084"><abbr title="Sparefoot\PitaService\Controller\UserController::_addAcctMgmtUser">_addAcctMgmtUser</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1101"><abbr title="Sparefoot\PitaService\Controller\UserController::_acctMgmtRecipients">_acctMgmtRecipients</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1150"><abbr title="Sparefoot\PitaService\Controller\UserController::_facilityContactRecipients">_facilityContactRecipients</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1203"><abbr title="Sparefoot\PitaService\Controller\UserController::_getActiveAcctIds">_getActiveAcctIds</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1231"><abbr title="Sparefoot\PitaService\Controller\UserController::_getActiveListingAvailIds">_getActiveListingAvailIds</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1259"><abbr title="Sparefoot\PitaService\Controller\UserController::createusersAction">createusersAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UserController.php.html#1376"><abbr title="Sparefoot\PitaService\Controller\UserController::tokenAction">tokenAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#69"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::renderAction">renderAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UtilitiesController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::updateInputsAction">updateInputsAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::indexAction">indexAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#103"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::citygridAction">citygridAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#148"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::citygridfacilityAction">citygridfacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::superpagesAction">superpagesAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#233"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::superpagesfacilityAction">superpagesfacilityAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#263"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::allowiypAction">allowiypAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#277"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::accountonAction">accountonAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#313"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::accountoffAction">accountoffAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#359"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::localdotcomAction">localdotcomAction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#365"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::_initTrueDateRange">_initTrueDateRange</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#385"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::getTrueBeginDate">getTrueBeginDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="YellowpagesController.php.html#390"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::getTrueEndDate">getTrueEndDate</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#177"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::scriptFooter">scriptFooter</abbr></a></td><td class="text-right">9%</td></tr>
       <tr><td><a href="ApiController.php.html#49"><abbr title="Sparefoot\PitaService\Controller\ApiController::unitsAction">unitsAction</abbr></a></td><td class="text-right">21%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#155"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::cssHeader">cssHeader</abbr></a></td><td class="text-right">25%</td></tr>
       <tr><td><a href="AccountController.php.html#32"><abbr title="Sparefoot\PitaService\Controller\AccountController::listAction">listAction</abbr></a></td><td class="text-right">35%</td></tr>
       <tr><td><a href="ApiController.php.html#829"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullEasyStorageSolutionsUnits">_pullEasyStorageSolutionsUnits</abbr></a></td><td class="text-right">40%</td></tr>
       <tr><td><a href="AnalyticsController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::indexAction">indexAction</abbr></a></td><td class="text-right">61%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#299"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::authenticateUser">authenticateUser</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#332"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::_getParam">_getParam</abbr></a></td><td class="text-right">66%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#56"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::init">init</abbr></a></td><td class="text-right">85%</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#73"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::initGeneralData">initGeneralData</abbr></a></td><td class="text-right">86%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="InventoryController.php.html#1010"><abbr title="Sparefoot\PitaService\Controller\InventoryController::jsondetailsAction">jsondetailsAction</abbr></a></td><td class="text-right">9506</td></tr>
       <tr><td><a href="InventoryController.php.html#1416"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatedetailsAction">updatedetailsAction</abbr></a></td><td class="text-right">5852</td></tr>
       <tr><td><a href="InventoryController.php.html#2178"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unitAction">unitAction</abbr></a></td><td class="text-right">2970</td></tr>
       <tr><td><a href="AnalyticsController.php.html#156"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_export">_export</abbr></a></td><td class="text-right">2550</td></tr>
       <tr><td><a href="FiltersController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\FiltersController::loadAction">loadAction</abbr></a></td><td class="text-right">2450</td></tr>
       <tr><td><a href="AccountController.php.html#1529"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeintegrationcredsAction">changeintegrationcredsAction</abbr></a></td><td class="text-right">2256</td></tr>
       <tr><td><a href="AccountController.php.html#988"><abbr title="Sparefoot\PitaService\Controller\AccountController::newacctAction">newacctAction</abbr></a></td><td class="text-right">1892</td></tr>
       <tr><td><a href="InventoryController.php.html#3665"><abbr title="Sparefoot\PitaService\Controller\InventoryController::copyFacilityDataAction">copyFacilityDataAction</abbr></a></td><td class="text-right">1560</td></tr>
       <tr><td><a href="InventoryController.php.html#1857"><abbr title="Sparefoot\PitaService\Controller\InventoryController::multiplefacilityupdateAction">multiplefacilityupdateAction</abbr></a></td><td class="text-right">1406</td></tr>
       <tr><td><a href="AccountController.php.html#1288"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeIntegration">_makeIntegration</abbr></a></td><td class="text-right">992</td></tr>
       <tr><td><a href="SalesforceController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\SalesforceController::addAccountAction">addAccountAction</abbr></a></td><td class="text-right">992</td></tr>
       <tr><td><a href="InventoryController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\InventoryController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">812</td></tr>
       <tr><td><a href="InventoryController.php.html#138"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadfacilitiesAction">loadfacilitiesAction</abbr></a></td><td class="text-right">812</td></tr>
       <tr><td><a href="ApiController.php.html#164"><abbr title="Sparefoot\PitaService\Controller\ApiController::exportunitapiAction">exportunitapiAction</abbr></a></td><td class="text-right">756</td></tr>
       <tr><td><a href="FacilityController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_init">_init</abbr></a></td><td class="text-right">600</td></tr>
       <tr><td><a href="UserController.php.html#974"><abbr title="Sparefoot\PitaService\Controller\UserController::_processEmailRecipientReport">_processEmailRecipientReport</abbr></a></td><td class="text-right">600</td></tr>
       <tr><td><a href="FacilityController.php.html#393"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_loadFacilities">_loadFacilities</abbr></a></td><td class="text-right">552</td></tr>
       <tr><td><a href="InventoryController.php.html#2631"><abbr title="Sparefoot\PitaService\Controller\InventoryController::amenitiescheckAction">amenitiescheckAction</abbr></a></td><td class="text-right">462</td></tr>
       <tr><td><a href="BookingsController.php.html#543"><abbr title="Sparefoot\PitaService\Controller\BookingsController::_changeBookingState">_changeBookingState</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="AccountController.php.html#2270"><abbr title="Sparefoot\PitaService\Controller\AccountController::updatenscustomerAction">updatenscustomerAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="BookingsController.php.html#632"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="FacilitiesController.php.html#32"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::indexAction">indexAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="InventoryController.php.html#646"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadinfoAction">loadinfoAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="UserController.php.html#1259"><abbr title="Sparefoot\PitaService\Controller\UserController::createusersAction">createusersAction</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="ApiController.php.html#49"><abbr title="Sparefoot\PitaService\Controller\ApiController::unitsAction">unitsAction</abbr></a></td><td class="text-right">281</td></tr>
       <tr><td><a href="AnalyticsController.php.html#328"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_processQuery">_processQuery</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="StatementsController.php.html#153"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_exportInvoice">_exportInvoice</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="ToolsController.php.html#265"><abbr title="Sparefoot\PitaService\Controller\ToolsController::searchKeyChangerAction">searchKeyChangerAction</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="ToolsController.php.html#1389"><abbr title="Sparefoot\PitaService\Controller\ToolsController::platformQueryAction">platformQueryAction</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="UserController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\UserController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">272</td></tr>
       <tr><td><a href="BookingsController.php.html#217"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getbookinginfoAction">getbookinginfoAction</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="BookingsController.php.html#468"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changebookingsAction">changebookingsAction</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="FacilityController.php.html#558"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_tableFromReport">_tableFromReport</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="InventoryController.php.html#3338"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getaccessAction">getaccessAction</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ToolsController.php.html#173"><abbr title="Sparefoot\PitaService\Controller\ToolsController::noEmailListAction">noEmailListAction</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="InventoryController.php.html#412"><abbr title="Sparefoot\PitaService\Controller\InventoryController::toggleproductAction">toggleproductAction</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="InventoryController.php.html#2786"><abbr title="Sparefoot\PitaService\Controller\InventoryController::acctfaclistAction">acctfaclistAction</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="UserController.php.html#359"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxcreatemyfootAction">ajaxcreatemyfootAction</abbr></a></td><td class="text-right">210</td></tr>
       <tr><td><a href="InventoryController.php.html#3266"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateinventoryAction">updateinventoryAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="SearchController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\SearchController::testAction">testAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ToolsController.php.html#924"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentCreatorImportAction">siteContentCreatorImportAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ToolsController.php.html#1055"><abbr title="Sparefoot\PitaService\Controller\ToolsController::accountingMapAction">accountingMapAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="UserController.php.html#202"><abbr title="Sparefoot\PitaService\Controller\UserController::editinfoAction">editinfoAction</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="AccountController.php.html#659"><abbr title="Sparefoot\PitaService\Controller\AccountController::verifyAction">verifyAction</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="FacilityController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\FacilityController::indexAction">indexAction</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="InventoryController.php.html#360"><abbr title="Sparefoot\PitaService\Controller\InventoryController::productlistAction">productlistAction</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="ToolsController.php.html#706"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentCreatorAction">siteContentCreatorAction</abbr></a></td><td class="text-right">156</td></tr>
       <tr><td><a href="AccountController.php.html#749"><abbr title="Sparefoot\PitaService\Controller\AccountController::getintegrationdetailsAction">getintegrationdetailsAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="QuickclientController.php.html#224"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::executeAction">executeAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="TableauController.php.html#322"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveMassEditAction">saveMassEditAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ToolsController.php.html#390"><abbr title="Sparefoot\PitaService\Controller\ToolsController::twilioReportsAction">twilioReportsAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ToolsController.php.html#476"><abbr title="Sparefoot\PitaService\Controller\ToolsController::createTwilioNumberAction">createTwilioNumberAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ToolsController.php.html#1188"><abbr title="Sparefoot\PitaService\Controller\ToolsController::featureFlagsAction">featureFlagsAction</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="UserController.php.html#1150"><abbr title="Sparefoot\PitaService\Controller\UserController::_facilityContactRecipients">_facilityContactRecipients</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="AccountController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\AccountController::moveintegrationAction">moveintegrationAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AccountController.php.html#305"><abbr title="Sparefoot\PitaService\Controller\AccountController::syncAction">syncAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AccountController.php.html#2053"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentitiesAction">getbillableentitiesAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AccountController.php.html#2174"><abbr title="Sparefoot\PitaService\Controller\AccountController::updateqbcustomerAction">updateqbcustomerAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AccountController.php.html#2413"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentityfacilitiesAction">getbillableentityfacilitiesAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ApiController.php.html#428"><abbr title="Sparefoot\PitaService\Controller\ApiController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="InventoryController.php.html#2467"><abbr title="Sparefoot\PitaService\Controller\InventoryController::photoframeAction">photoframeAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ReportsController.php.html#66"><abbr title="Sparefoot\PitaService\Controller\ReportsController::renderAction">renderAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ToolsController.php.html#1241"><abbr title="Sparefoot\PitaService\Controller\ToolsController::handleFeatureFlagSave">handleFeatureFlagSave</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="UserController.php.html#652"><abbr title="Sparefoot\PitaService\Controller\UserController::changeattributesAction">changeattributesAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="UserController.php.html#866"><abbr title="Sparefoot\PitaService\Controller\UserController::_emailRecipientExport">_emailRecipientExport</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="UtilitiesController.php.html#69"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::renderAction">renderAction</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="AccountController.php.html#32"><abbr title="Sparefoot\PitaService\Controller\AccountController::listAction">listAction</abbr></a></td><td class="text-right">105</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#188"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processReport">processReport</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="AccountController.php.html#121"><abbr title="Sparefoot\PitaService\Controller\AccountController::_export">_export</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="AccountController.php.html#1815"><abbr title="Sparefoot\PitaService\Controller\AccountController::getintegrationcredsAction">getintegrationcredsAction</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::testAction">testAction</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="DisputesController.php.html#149"><abbr title="Sparefoot\PitaService\Controller\DisputesController::applyRuling">applyRuling</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ToolsController.php.html#614"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentEditorAction">siteContentEditorAction</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="UserController.php.html#144"><abbr title="Sparefoot\PitaService\Controller\UserController::loadusersAction">loadusersAction</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#133"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::processQueryParams">processQueryParams</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="BuyoutController.php.html#378"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_updateExistingTenants">_updateExistingTenants</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="FacilityController.php.html#324"><abbr title="Sparefoot\PitaService\Controller\FacilityController::jsonunitdetailsAction">jsonunitdetailsAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="IncentivesController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::searchRequestsAction">searchRequestsAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="InventoryController.php.html#931"><abbr title="Sparefoot\PitaService\Controller\InventoryController::approveAction">approveAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="InventoryController.php.html#2929"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_refreshSearch">_refreshSearch</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="JobsController.php.html#801"><abbr title="Sparefoot\PitaService\Controller\JobsController::timeDisplay">timeDisplay</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PitaSearchController.php.html#30"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::indexAction">indexAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PitaSearchController.php.html#82"><abbr title="Sparefoot\PitaService\Controller\PitaSearchController::queryAction">queryAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="PublicController.php.html#147"><abbr title="Sparefoot\PitaService\Controller\PublicController::hourlyAlertsAction">hourlyAlertsAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="SalesforceController.php.html#197"><abbr title="Sparefoot\PitaService\Controller\SalesforceController::_createManualFacilities">_createManualFacilities</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#73"><abbr title="Sparefoot\PitaService\Controller\UserController::_listAction">_listAction</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#790"><abbr title="Sparefoot\PitaService\Controller\UserController::_export">_export</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#941"><abbr title="Sparefoot\PitaService\Controller\UserController::_processQuery">_processQuery</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="UserController.php.html#1101"><abbr title="Sparefoot\PitaService\Controller\UserController::_acctMgmtRecipients">_acctMgmtRecipients</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#177"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::scriptFooter">scriptFooter</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#272"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::populateFilter">populateFilter</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="AccountController.php.html#1904"><abbr title="Sparefoot\PitaService\Controller\AccountController::geteditintegrationdetailsAction">geteditintegrationdetailsAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="AccountController.php.html#1951"><abbr title="Sparefoot\PitaService\Controller\AccountController::saveeditintegrationdetailsAction">saveeditintegrationdetailsAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ApiController.php.html#866"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullExtraspaceUnits">_pullExtraspaceUnits</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="BuyoutController.php.html#340"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::_updatePendingBookings">_updatePendingBookings</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="DisputesController.php.html#214"><abbr title="Sparefoot\PitaService\Controller\DisputesController::validateBookingIsReviewable">validateBookingIsReviewable</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="FacilityController.php.html#239"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_fetchInventoryData">_fetchInventoryData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="FacilityController.php.html#644"><abbr title="Sparefoot\PitaService\Controller\FacilityController::_initFilter">_initFilter</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="InventoryController.php.html#515"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateadnetworkpriceAction">updateadnetworkpriceAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="InventoryController.php.html#568"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatehostedwebsitepriceAction">updatehostedwebsitepriceAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="InventoryController.php.html#3119"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_fetchInventoryData">_fetchInventoryData</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="LoginController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\LoginController::index">index</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ProxyController.php.html#110"><abbr title="Sparefoot\PitaService\Controller\ProxyController::proxyRequest">proxyRequest</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="QuickrepController.php.html#195"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::hourlyAlertsAction">hourlyAlertsAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#158"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::saveexistingAction">saveexistingAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="TableauController.php.html#140"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveAction">saveAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ToolsController.php.html#837"><abbr title="Sparefoot\PitaService\Controller\ToolsController::siteContentImportAction">siteContentImportAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="UserController.php.html#618"><abbr title="Sparefoot\PitaService\Controller\UserController::passwordAction">passwordAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="YellowpagesController.php.html#277"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::accountonAction">accountonAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="YellowpagesController.php.html#313"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::accountoffAction">accountoffAction</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="AccountController.php.html#442"><abbr title="Sparefoot\PitaService\Controller\AccountController::changebidsAction">changebidsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AccountController.php.html#842"><abbr title="Sparefoot\PitaService\Controller\AccountController::getacctdetailsAction">getacctdetailsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AccountController.php.html#1490"><abbr title="Sparefoot\PitaService\Controller\AccountController::deleteaccountAction">deleteaccountAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AccountController.php.html#2126"><abbr title="Sparefoot\PitaService\Controller\AccountController::getbillableentitydetailsAction">getbillableentitydetailsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AnalyticsController.php.html#428"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::_processReport">_processReport</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BookingsController.php.html#296"><abbr title="Sparefoot\PitaService\Controller\BookingsController::jsondetailsAction">jsondetailsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BookingsController.php.html#408"><abbr title="Sparefoot\PitaService\Controller\BookingsController::addduplicateAction">addduplicateAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BookingsController.php.html#935"><abbr title="Sparefoot\PitaService\Controller\BookingsController::resendemailsAction">resendemailsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BookingsController.php.html#990"><abbr title="Sparefoot\PitaService\Controller\BookingsController::sitelinkledgerAction">sitelinkledgerAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BuyoutController.php.html#68"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::createAction">createAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BuyoutController.php.html#235"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::downloadAction">downloadAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_getApiResponseTest">_getApiResponseTest</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DashboardController.php.html#113"><abbr title="Sparefoot\PitaService\Controller\DashboardController::bookingsAction">bookingsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DisputesController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\DisputesController::opendisputesAction">opendisputesAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="DisputesController.php.html#93"><abbr title="Sparefoot\PitaService\Controller\DisputesController::closeddisputesAction">closeddisputesAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="FacilitiesController.php.html#178"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::loadbyidAction">loadbyidAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#328"><abbr title="Sparefoot\PitaService\Controller\InventoryController::accountlistAction">accountlistAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#3155"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatefacilityAction">updatefacilityAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#3192"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addfacilitycontactAction">addfacilitycontactAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#3469"><abbr title="Sparefoot\PitaService\Controller\InventoryController::movefacilityAction">movefacilityAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#3602"><abbr title="Sparefoot\PitaService\Controller\InventoryController::syncselectedfacsAction">syncselectedfacsAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="InventoryController.php.html#4057"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essConvertFromManualAction">essConvertFromManualAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="NetsuiteController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\NetsuiteController::accountAction">accountAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="PhotosController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\PhotosController::indexAction">indexAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ReviewsController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::indexAction">indexAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SearchController.php.html#144"><abbr title="Sparefoot\PitaService\Controller\SearchController::distanceAction">distanceAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SoftwarepartnerController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\SoftwarepartnerController::indexAction">indexAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#66"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::distanceAction">distanceAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="StatementsController.php.html#289"><abbr title="Sparefoot\PitaService\Controller\StatementsController::invoiceAction">invoiceAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="StatementsController.php.html#382"><abbr title="Sparefoot\PitaService\Controller\StatementsController::emailpreviewAction">emailpreviewAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="StatementsController.php.html#430"><abbr title="Sparefoot\PitaService\Controller\StatementsController::getInvoiceAction">getInvoiceAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="TestController.php.html#145"><abbr title="Sparefoot\PitaService\Controller\TestController::modifytestAction">modifytestAction</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="AffiliateController.php.html#287"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processSaveSite">_processSaveSite</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ApiController.php.html#548"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStorageMartFacilityData">_pullStorageMartFacilityData</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BookingsController.php.html#894"><abbr title="Sparefoot\PitaService\Controller\BookingsController::previewemailsAction">previewemailsAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="BuyoutController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editAction">editAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DashboardController.php.html#185"><abbr title="Sparefoot\PitaService\Controller\DashboardController::initDailyBookingsProjection">initDailyBookingsProjection</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ErrorController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\ErrorController::errorAction">errorAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="FacilityController.php.html#761"><abbr title="Sparefoot\PitaService\Controller\FacilityController::exportdirectoryrecordingsAction">exportdirectoryrecordingsAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="IncentivesController.php.html#172"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::addIncentiveAndBookingDataToView">addIncentiveAndBookingDataToView</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InventoryController.php.html#285"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_getFacilitiesInDeletedAccounts">_getFacilitiesInDeletedAccounts</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InventoryController.php.html#2567"><abbr title="Sparefoot\PitaService\Controller\InventoryController::saveyieldAction">saveyieldAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InventoryController.php.html#3011"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unitexportAction">unitexportAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InventoryController.php.html#4106"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essConvertToManualAction">essConvertToManualAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JobsController.php.html#252"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledAction">scheduledAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="JobsController.php.html#600"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerInfoAction">workerInfoAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OmnomController.php.html#107"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByIntegration">_queryStatusByIntegration</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="OmnomController.php.html#166"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByFacility">_queryStatusByFacility</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PaidmediaController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::modifypaidmediaAction">modifypaidmediaAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PaidmediaController.php.html#122"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::getRuleengineList">getRuleengineList</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ProxyController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\ProxyController::loadAction">loadAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ProxyController.php.html#89"><abbr title="Sparefoot\PitaService\Controller\ProxyController::AuthenticateProxyAuthorization">AuthenticateProxyAuthorization</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PublicController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\PublicController::bookUnitAction">bookUnitAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="QuickclientController.php.html#284"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::toArray">toArray</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ReviewsController.php.html#156"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::saveReviewAction">saveReviewAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="RewardsController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\RewardsController::newkiindAction">newkiindAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="TableauController.php.html#233"><abbr title="Sparefoot\PitaService\Controller\TableauController::saveKeyAction">saveKeyAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ToolsController.php.html#545"><abbr title="Sparefoot\PitaService\Controller\ToolsController::purchaseTwilioNumbersAction">purchaseTwilioNumbersAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UserController.php.html#252"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxcreateAction">ajaxcreateAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UserController.php.html#587"><abbr title="Sparefoot\PitaService\Controller\UserController::ajaxpasswordAction">ajaxpasswordAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="YellowpagesController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::indexAction">indexAction</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ApiController.php.html#829"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullEasyStorageSolutionsUnits">_pullEasyStorageSolutionsUnits</abbr></a></td><td class="text-right">21</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#58"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::export">export</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::_exportReport">_exportReport</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#437"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getOldClass">getOldClass</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AccountController.php.html#522"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeminimumbidsAction">changeminimumbidsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AccountController.php.html#586"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeprimarycontactAction">changeprimarycontactAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#37"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getAdminsHtml">getAdminsHtml</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AffiliateController.php.html#138"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::updateAction">updateAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AffiliateController.php.html#249"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::exclusionsAction">exclusionsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ApiController.php.html#702"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershift4UnitData">_pullCentershift4UnitData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ApiController.php.html#944"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDomicoUnitData">_pullDomicoUnitData</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BillingController.php.html#124"><abbr title="Sparefoot\PitaService\Controller\BillingController::_runQuery">_runQuery</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BookingsController.php.html#49"><abbr title="Sparefoot\PitaService\Controller\BookingsController::indexAction">indexAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BookingsController.php.html#856"><abbr title="Sparefoot\PitaService\Controller\BookingsController::getunitinfoAction">getunitinfoAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BuyoutController.php.html#280"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editUpdateAction">editUpdateAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="CdpController.php.html#185"><abbr title="Sparefoot\PitaService\Controller\CdpController::viewLogAction">viewLogAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="EmailController.php.html#83"><abbr title="Sparefoot\PitaService\Controller\EmailController::ajaxsendAction">ajaxsendAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FacilitiesController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::getqueryAction">getqueryAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FacilityController.php.html#147"><abbr title="Sparefoot\PitaService\Controller\FacilityController::acquiretwilionumberAction">acquiretwilionumberAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FacilityController.php.html#202"><abbr title="Sparefoot\PitaService\Controller\FacilityController::releasetwilionumberAction">releasetwilionumberAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FacilityController.php.html#274"><abbr title="Sparefoot\PitaService\Controller\FacilityController::jsonunitlistdetailsAction">jsonunitlistdetailsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FeedsController.php.html#93"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_urlTitle">_urlTitle</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="IncentivesController.php.html#54"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::redemptionRequestsAction">redemptionRequestsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="IndexController.php.html#129"><abbr title="Sparefoot\PitaService\Controller\IndexController::updateBookmarksAction">updateBookmarksAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#484"><abbr title="Sparefoot\PitaService\Controller\InventoryController::togglehostedwebsitetypeAction">togglehostedwebsitetypeAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#892"><abbr title="Sparefoot\PitaService\Controller\InventoryController::unithtmlAction">unithtmlAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#980"><abbr title="Sparefoot\PitaService\Controller\InventoryController::facilityapproveAction">facilityapproveAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#2119"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatestreetviewAction">updatestreetviewAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="InventoryController.php.html#3234"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removefacilitycontactAction">removefacilitycontactAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#390"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledSaveAction">scheduledSaveAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#704"><abbr title="Sparefoot\PitaService\Controller\JobsController::childUpdateAction">childUpdateAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#772"><abbr title="Sparefoot\PitaService\Controller\JobsController::finishCode">finishCode</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#786"><abbr title="Sparefoot\PitaService\Controller\JobsController::successCode">successCode</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="JobsController.php.html#839"><abbr title="Sparefoot\PitaService\Controller\JobsController::_verifyChildAllowed">_verifyChildAllowed</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MaintenanceModeController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\MaintenanceModeController::_sendMaintenanceModeEmail">_sendMaintenanceModeEmail</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="NetsuiteController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\NetsuiteController::statusAction">statusAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="OmnomController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\OmnomController::indexAction">indexAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ProxyController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\ProxyController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuickclientController.php.html#143"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuickrepController.php.html#124"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::chartAction">chartAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ReviewsController.php.html#102"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::deleteReviewAction">deleteReviewAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="StatementsController.php.html#84"><abbr title="Sparefoot\PitaService\Controller\StatementsController::generateAction">generateAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TableauController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\TableauController::indexAction">indexAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TableauController.php.html#270"><abbr title="Sparefoot\PitaService\Controller\TableauController::deleteKeyAction">deleteKeyAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TestController.php.html#245"><abbr title="Sparefoot\PitaService\Controller\TestController::_saveTestVariation">_saveTestVariation</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserController.php.html#291"><abbr title="Sparefoot\PitaService\Controller\UserController::_createPitaUser">_createPitaUser</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserController.php.html#490"><abbr title="Sparefoot\PitaService\Controller\UserController::_buildUser">_buildUser</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserController.php.html#722"><abbr title="Sparefoot\PitaService\Controller\UserController::addstmtrecipientsAction">addstmtrecipientsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="UserController.php.html#756"><abbr title="Sparefoot\PitaService\Controller\UserController::removestmtrecipientsAction">removestmtrecipientsAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="YellowpagesController.php.html#103"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::citygridAction">citygridAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="YellowpagesController.php.html#233"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::superpagesfacilityAction">superpagesfacilityAction</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#95"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::genesisConst">genesisConst</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#271"><abbr title="Sparefoot\PitaService\Controller\AccountController::moveacctmgmtusersAction">moveacctmgmtusersAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#629"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeofflinereservationscdpAction">changeofflinereservationscdpAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#955"><abbr title="Sparefoot\PitaService\Controller\AccountController::addacctadminAction">addacctadminAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#1253"><abbr title="Sparefoot\PitaService\Controller\AccountController::_makeUser">_makeUser</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#2468"><abbr title="Sparefoot\PitaService\Controller\AccountController::updateadnetworkpriceAction">updateadnetworkpriceAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#2502"><abbr title="Sparefoot\PitaService\Controller\AccountController::updatehostedwebsitepriceAction">updatehostedwebsitepriceAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AccountController.php.html#2582"><abbr title="Sparefoot\PitaService\Controller\AccountController::clearaccounttermsAction">clearaccounttermsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AffiliateController.php.html#86"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::createAction">createAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AffiliateController.php.html#108"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processCreate">_processCreate</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AffiliateController.php.html#193"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::addsiteAction">addsiteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AffiliateController.php.html#215"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::updatesiteAction">updatesiteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AnalyticsController.php.html#95"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::loadqueryAction">loadqueryAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#610"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullStorageMartUnitData">_pullStorageMartUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#658"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCentershiftUnitData">_pullCentershiftUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#744"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullCubesmartUnitData">_pullCubesmartUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#764"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSelfStorageManagerUnitData">_pullSelfStorageManagerUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#893"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullSiteLinkUnitData">_pullSiteLinkUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApiController.php.html#918"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullQuickstorUnitData">_pullQuickstorUnitData</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BookingsController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\BookingsController::historyAction">historyAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BookingsController.php.html#383"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changemoveoutAction">changemoveoutAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BookingsController.php.html#609"><abbr title="Sparefoot\PitaService\Controller\BookingsController::makebookingresidualAction">makebookingresidualAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BuyoutController.php.html#185"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::invoiceAction">invoiceAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BuyoutController.php.html#212"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::deleteAction">deleteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BuyoutController.php.html#319"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::editBookingsAction">editBookingsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CdpController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchRunsAction">matchRunsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CdpController.php.html#159"><abbr title="Sparefoot\PitaService\Controller\CdpController::killMatchRunAction">killMatchRunAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CentaursearchapiController.php.html#97"><abbr title="Sparefoot\PitaService\Controller\CentaursearchapiController::_init">_init</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisastersController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\DisastersController::includeContainedFacilities">includeContainedFacilities</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisastersController.php.html#130"><abbr title="Sparefoot\PitaService\Controller\DisastersController::editDisasterAction">editDisasterAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#25"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_init">_init</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#239"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeRuling">updateDisputeRuling</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#268"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewRulingReason">updateDisputeReviewRulingReason</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#284"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewNotes">updateDisputeReviewNotes</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#309"><abbr title="Sparefoot\PitaService\Controller\DisputesController::updateDisputeReviewAgent">updateDisputeReviewAgent</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DisputesController.php.html#359"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_createCsv">_createCsv</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FacilityController.php.html#707"><abbr title="Sparefoot\PitaService\Controller\FacilityController::activateAction">activateAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FacilityController.php.html#734"><abbr title="Sparefoot\PitaService\Controller\FacilityController::deactivateAction">deactivateAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FaqsController.php.html#42"><abbr title="Sparefoot\PitaService\Controller\FaqsController::getAction">getAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FaqsController.php.html#67"><abbr title="Sparefoot\PitaService\Controller\FaqsController::saveAction">saveAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FeedsController.php.html#50"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_prepHours">_prepHours</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="IncentivesController.php.html#101"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::parseIncentiveResponse">parseIncentiveResponse</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="IndexController.php.html#92"><abbr title="Sparefoot\PitaService\Controller\IndexController::addBookmarkAction">addBookmarkAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#108"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadpercentcompleteAction">loadpercentcompleteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#777"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadresaleratesAction">loadresaleratesAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#861"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addupdateresalebucketAction">addupdateresalebucketAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#1371"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatecustomclosureAction">updatecustomclosureAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3508"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getsitelinkfacilitylistAction">getsitelinkfacilitylistAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3539"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getselfstoragemanagerfacilitylistAction">getselfstoragemanagerfacilitylistAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3570"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getcentershiftfacilitylistAction">getcentershiftfacilitylistAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3947"><abbr title="Sparefoot\PitaService\Controller\InventoryController::integrationsByAccountAction">integrationsByAccountAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#3977"><abbr title="Sparefoot\PitaService\Controller\InventoryController::facilitiesByIntegrationAction">facilitiesByIntegrationAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#4006"><abbr title="Sparefoot\PitaService\Controller\InventoryController::getfacilityAction">getfacilityAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="InventoryController.php.html#4036"><abbr title="Sparefoot\PitaService\Controller\InventoryController::essfacilitylookupAction">essfacilitylookupAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\JobsController::summaryAction">summaryAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#204"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandSaveAction">commandSaveAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#317"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledEditAction">scheduledEditAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#344"><abbr title="Sparefoot\PitaService\Controller\JobsController::scheduledAddAction">scheduledAddAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="JobsController.php.html#526"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerZombieAction">workerZombieAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="OmnomController.php.html#60"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_queryStatusByJob">_queryStatusByJob</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PaidmediaController.php.html#147"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::getCampaignList">getCampaignList</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PayoutController.php.html#163"><abbr title="Sparefoot\PitaService\Controller\PayoutController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PhotosController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\PhotosController::approveAction">approveAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="PublicController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\PublicController::searchServicesAction">searchServicesAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuickclientController.php.html#38"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::listAction">listAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuickclientController.php.html#76"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::renderAction">renderAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuickclientController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\QuickclientController::unitsAction">unitsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuickrepController.php.html#97"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::tableAction">tableAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#125"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::savenewAction">savenewAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ReportsController.php.html#152"><abbr title="Sparefoot\PitaService\Controller\ReportsController::tableAction">tableAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ReviewsController.php.html#213"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::setStatusAction">setStatusAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ServiceareaController.php.html#55"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::getAction">getAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#144"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StatementsController.php.html#123"><abbr title="Sparefoot\PitaService\Controller\StatementsController::_export">_export</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StatementsController.php.html#265"><abbr title="Sparefoot\PitaService\Controller\StatementsController::cancelInvoiceAction">cancelInvoiceAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="StatementsController.php.html#357"><abbr title="Sparefoot\PitaService\Controller\StatementsController::pdfAction">pdfAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\TableauController::initView">initView</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#118"><abbr title="Sparefoot\PitaService\Controller\TableauController::editAction">editAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\TableauController::deleteAction">deleteAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#211"><abbr title="Sparefoot\PitaService\Controller\TableauController::editKeyAction">editKeyAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TableauController.php.html#297"><abbr title="Sparefoot\PitaService\Controller\TableauController::massEditKeyAction">massEditKeyAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TestController.php.html#63"><abbr title="Sparefoot\PitaService\Controller\TestController::manageAction">manageAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TestController.php.html#91"><abbr title="Sparefoot\PitaService\Controller\TestController::manageuiAction">manageuiAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ToolsController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\ToolsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ToolsController.php.html#1160"><abbr title="Sparefoot\PitaService\Controller\ToolsController::cabinetAction">cabinetAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ToolsController.php.html#1323"><abbr title="Sparefoot\PitaService\Controller\ToolsController::pressPageAction">pressPageAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ToolsController.php.html#1350"><abbr title="Sparefoot\PitaService\Controller\ToolsController::pressPageEditorAction">pressPageEditorAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserController.php.html#330"><abbr title="Sparefoot\PitaService\Controller\UserController::_createPortalUser">_createPortalUser</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UserController.php.html#473"><abbr title="Sparefoot\PitaService\Controller\UserController::_addFacilityContact">_addFacilityContact</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="UtilitiesController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::updateInputsAction">updateInputsAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YellowpagesController.php.html#148"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::citygridfacilityAction">citygridfacilityAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YellowpagesController.php.html#199"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::superpagesAction">superpagesAction</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="YellowpagesController.php.html#365"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::_initTrueDateRange">_initTrueDateRange</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AnalyticsController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::indexAction">indexAction</abbr></a></td><td class="text-right">9</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#155"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::cssHeader">cssHeader</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::_exportServerReport">_exportServerReport</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#168"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::newQuery">newQuery</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractGenericController.php.html#222"><abbr title="Sparefoot\PitaService\Controller\AbstractGenericController::tableFromReport">tableFromReport</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#321"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getParam">getParam</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#364"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getTwig">getTwig</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#448"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::getNewClass">getNewClass</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#60"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::getMetricInfoGetter">getMetricInfoGetter</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractView/GenericView.php.html#117"><abbr title="Sparefoot\PitaService\Controller\AbstractView\GenericView::getEnv">getEnv</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#369"><abbr title="Sparefoot\PitaService\Controller\AccountController::_resetSync">_resetSync</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#492"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeaccountminimumbidAction">changeaccountminimumbidAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#559"><abbr title="Sparefoot\PitaService\Controller\AccountController::changeexclusiveAction">changeexclusiveAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#789"><abbr title="Sparefoot\PitaService\Controller\AccountController::unverifyAction">unverifyAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#809"><abbr title="Sparefoot\PitaService\Controller\AccountController::editinfoAction">editinfoAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#905"><abbr title="Sparefoot\PitaService\Controller\AccountController::removeacctuserAction">removeacctuserAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#931"><abbr title="Sparefoot\PitaService\Controller\AccountController::removeadminAction">removeadminAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#2001"><abbr title="Sparefoot\PitaService\Controller\AccountController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#2026"><abbr title="Sparefoot\PitaService\Controller\AccountController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#2401"><abbr title="Sparefoot\PitaService\Controller\AccountController::loadFacilityIdToFacilityMapForBillableEntity">loadFacilityIdToFacilityMapForBillableEntity</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#2537"><abbr title="Sparefoot\PitaService\Controller\AccountController::changedocusigncompleteAction">changedocusigncompleteAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountController.php.html#2562"><abbr title="Sparefoot\PitaService\Controller\AccountController::changetestaccountAction">changetestaccountAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#20"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::getBidTypeContinueResidualMessage">getBidTypeContinueResidualMessage</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#88"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::bidCurrency">bidCurrency</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#105"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::labelText">labelText</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AccountView/ListActionView.php.html#132"><abbr title="Sparefoot\PitaService\Controller\AccountView\ListActionView::formatDate">formatDate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AffiliateController.php.html#64"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::generateapiAction">generateapiAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AffiliateController.php.html#173"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processUpdate">_processUpdate</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AffiliateController.php.html#332"><abbr title="Sparefoot\PitaService\Controller\AffiliateController::_processDelExclusion">_processDelExclusion</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AnalyticsController.php.html#72"><abbr title="Sparefoot\PitaService\Controller\AnalyticsController::sqlAction">sqlAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApiController.php.html#507"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDoorswapFacilityData">_pullDoorswapFacilityData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApiController.php.html#684"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullUncleBobsUnitData">_pullUncleBobsUnitData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApiController.php.html#732"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullUsiUnitData">_pullUsiUnitData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApiController.php.html#801"><abbr title="Sparefoot\PitaService\Controller\ApiController::_pullDoorswapUnitData">_pullDoorswapUnitData</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BillingController.php.html#48"><abbr title="Sparefoot\PitaService\Controller\BillingController::exportemailsAction">exportemailsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BillingController.php.html#108"><abbr title="Sparefoot\PitaService\Controller\BillingController::deleteAction">deleteAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BookingsController.php.html#27"><abbr title="Sparefoot\PitaService\Controller\BookingsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BookingsController.php.html#107"><abbr title="Sparefoot\PitaService\Controller\BookingsController::confcodeAction">confcodeAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BookingsController.php.html#129"><abbr title="Sparefoot\PitaService\Controller\BookingsController::salesforceAction">salesforceAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BookingsController.php.html#336"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changemoveinAction">changemoveinAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BookingsController.php.html#364"><abbr title="Sparefoot\PitaService\Controller\BookingsController::changeunitpriceAction">changeunitpriceAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BuyoutController.php.html#164"><abbr title="Sparefoot\PitaService\Controller\BuyoutController::viewAction">viewAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CdpController.php.html#74"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchAccountsAction">matchAccountsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CdpController.php.html#133"><abbr title="Sparefoot\PitaService\Controller\CdpController::matchBookingsAction">matchBookingsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CdpController.php.html#213"><abbr title="Sparefoot\PitaService\Controller\CdpController::runCdpAction">runCdpAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisastersController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\DisastersController::facilitiesAction">facilitiesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisastersController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\DisastersController::addDisasterAction">addDisasterAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisputesController.php.html#300"><abbr title="Sparefoot\PitaService\Controller\DisputesController::deleteDisputeReviewNotes">deleteDisputeReviewNotes</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisputesController.php.html#418"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_getCsvLine">_getCsvLine</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="DisputesController.php.html#476"><abbr title="Sparefoot\PitaService\Controller\DisputesController::_cleanCsvLine">_cleanCsvLine</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ErrorController.php.html#57"><abbr title="Sparefoot\PitaService\Controller\ErrorController::hipchatAction">hipchatAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FacilitiesController.php.html#117"><abbr title="Sparefoot\PitaService\Controller\FacilitiesController::bysourceAction">bysourceAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FacilityController.php.html#840"><abbr title="Sparefoot\PitaService\Controller\FacilityController::changenotesAction">changenotesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FederatedController.php.html#23"><abbr title="Sparefoot\PitaService\Controller\FederatedController::index">index</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FeedsController.php.html#11"><abbr title="Sparefoot\PitaService\Controller\FeedsController::citysearchAction">citysearchAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FeedsController.php.html#68"><abbr title="Sparefoot\PitaService\Controller\FeedsController::_facilityDetailsUrl">_facilityDetailsUrl</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="IncentivesController.php.html#88"><abbr title="Sparefoot\PitaService\Controller\IncentivesController::updateStatusAction">updateStatusAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="IndexController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\IndexController::oldIndexAction">oldIndexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#621"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updatetenantconnectprecalldigitsAction">updatetenantconnectprecalldigitsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#817"><abbr title="Sparefoot\PitaService\Controller\InventoryController::updateresalebucketAction">updateresalebucketAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#839"><abbr title="Sparefoot\PitaService\Controller\InventoryController::deleteresalebucketAction">deleteresalebucketAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#1397"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removecustomclosureAction">removecustomclosureAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2047"><abbr title="Sparefoot\PitaService\Controller\InventoryController::billableentitylistAction">billableentitylistAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2066"><abbr title="Sparefoot\PitaService\Controller\InventoryController::_makeBillableEntityList">_makeBillableEntityList</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2083"><abbr title="Sparefoot\PitaService\Controller\InventoryController::streetviewAction">streetviewAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2157"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadlogAction">loadlogAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2547"><abbr title="Sparefoot\PitaService\Controller\InventoryController::loadyieldjsonAction">loadyieldjsonAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2722"><abbr title="Sparefoot\PitaService\Controller\InventoryController::visibilitycheckAction">visibilitycheckAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2875"><abbr title="Sparefoot\PitaService\Controller\InventoryController::syncAction">syncAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2901"><abbr title="Sparefoot\PitaService\Controller\InventoryController::phidosyncAction">phidosyncAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#2985"><abbr title="Sparefoot\PitaService\Controller\InventoryController::resetlocationAction">resetlocationAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#3058"><abbr title="Sparefoot\PitaService\Controller\InventoryController::logexportAction">logexportAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#3419"><abbr title="Sparefoot\PitaService\Controller\InventoryController::addaccessAction">addaccessAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#3445"><abbr title="Sparefoot\PitaService\Controller\InventoryController::removeaccessAction">removeaccessAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InventoryController.php.html#3907"><abbr title="Sparefoot\PitaService\Controller\InventoryController::copyReviewToFacility">copyReviewToFacility</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#131"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandAddAction">commandAddAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#153"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandEditAction">commandEditAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#175"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandLogAction">commandLogAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#229"><abbr title="Sparefoot\PitaService\Controller\JobsController::commandDeleteAction">commandDeleteAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#439"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocLogAction">adhocLogAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#466"><abbr title="Sparefoot\PitaService\Controller\JobsController::adhocAddAction">adhocAddAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#576"><abbr title="Sparefoot\PitaService\Controller\JobsController::workerLogAction">workerLogAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#637"><abbr title="Sparefoot\PitaService\Controller\JobsController::childAction">childAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="JobsController.php.html#832"><abbr title="Sparefoot\PitaService\Controller\JobsController::dateDisplay">dateDisplay</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="LoginController.php.html#67"><abbr title="Sparefoot\PitaService\Controller\LoginController::getCurrentUser">getCurrentUser</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OmnomController.php.html#219"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getJobs">_getJobs</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OmnomController.php.html#239"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getIntegrations">_getIntegrations</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="OmnomController.php.html#259"><abbr title="Sparefoot\PitaService\Controller\OmnomController::_getAccounts">_getAccounts</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PaidmediaController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\PaidmediaController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PayoutController.php.html#18"><abbr title="Sparefoot\PitaService\Controller\PayoutController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PayoutController.php.html#61"><abbr title="Sparefoot\PitaService\Controller\PayoutController::previewAction">previewAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PayoutController.php.html#137"><abbr title="Sparefoot\PitaService\Controller\PayoutController::getnotesAction">getnotesAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PayoutController.php.html#191"><abbr title="Sparefoot\PitaService\Controller\PayoutController::_assignBookings">_assignBookings</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ProxyController.php.html#167"><abbr title="Sparefoot\PitaService\Controller\ProxyController::debugAction">debugAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PublicController.php.html#122"><abbr title="Sparefoot\PitaService\Controller\PublicController::searchAction">searchAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuickrepController.php.html#75"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::updateinputsAction">updateinputsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuickrepController.php.html#154"><abbr title="Sparefoot\PitaService\Controller\QuickrepController::sqlAction">sqlAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicksoapController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::indexAction">indexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicksoapController.php.html#35"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::wsdlAction">wsdlAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicksoapController.php.html#77"><abbr title="Sparefoot\PitaService\Controller\QuicksoapController::generateWsdlResponse">generateWsdlResponse</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#19"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#104"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::saveorderAction">saveorderAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuicktaggerController.php.html#204"><abbr title="Sparefoot\PitaService\Controller\QuicktaggerController::removeAction">removeAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReferralsController.php.html#40"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::toggleconvertedAction">toggleconvertedAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReferralsController.php.html#59"><abbr title="Sparefoot\PitaService\Controller\ReferralsController::toggleclaimedAction">toggleclaimedAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReportsController.php.html#22"><abbr title="Sparefoot\PitaService\Controller\ReportsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReportsController.php.html#43"><abbr title="Sparefoot\PitaService\Controller\ReportsController::indexAction">indexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReportsController.php.html#179"><abbr title="Sparefoot\PitaService\Controller\ReportsController::sqlAction">sqlAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReviewsController.php.html#16"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReviewsController.php.html#126"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::getReviewAction">getReviewAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ReviewsController.php.html#244"><abbr title="Sparefoot\PitaService\Controller\ReviewsController::editReviewMessageAction">editReviewMessageAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RewardsController.php.html#96"><abbr title="Sparefoot\PitaService\Controller\RewardsController::browsekiindAction">browsekiindAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SalesController.php.html#37"><abbr title="Sparefoot\PitaService\Controller\SalesController::searchAction">searchAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SearchController.php.html#225"><abbr title="Sparefoot\PitaService\Controller\SearchController::_conductSearch">_conductSearch</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceareaController.php.html#87"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::saveAction">saveAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceareaController.php.html#115"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::convexhullAction">convexhullAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceareaController.php.html#142"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::simplifyAction">simplifyAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ServiceareaController.php.html#167"><abbr title="Sparefoot\PitaService\Controller\ServiceareaController::getServiceAreaWktByFacilityId">getServiceAreaWktByFacilityId</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SphinxsearchController.php.html#39"><abbr title="Sparefoot\PitaService\Controller\SphinxsearchController::testAction">testAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="StatementsController.php.html#51"><abbr title="Sparefoot\PitaService\Controller\StatementsController::createAction">createAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TableauController.php.html#21"><abbr title="Sparefoot\PitaService\Controller\TableauController::setErrorMessage">setErrorMessage</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TableauController.php.html#29"><abbr title="Sparefoot\PitaService\Controller\TableauController::setSuccessMessage">setSuccessMessage</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TableauController.php.html#384"><abbr title="Sparefoot\PitaService\Controller\TableauController::addTeamAction">addTeamAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TestController.php.html#38"><abbr title="Sparefoot\PitaService\Controller\TestController::testsAction">testsAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TestController.php.html#180"><abbr title="Sparefoot\PitaService\Controller\TestController::ajaxcreatesearchAction">ajaxcreatesearchAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="TestController.php.html#207"><abbr title="Sparefoot\PitaService\Controller\TestController::ajaxcreateuiAction">ajaxcreateuiAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#85"><abbr title="Sparefoot\PitaService\Controller\ToolsController::indexAction">indexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#119"><abbr title="Sparefoot\PitaService\Controller\ToolsController::perfectPhotosSelectorAction">perfectPhotosSelectorAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#140"><abbr title="Sparefoot\PitaService\Controller\ToolsController::perfectPhotosSelectorPostAction">perfectPhotosSelectorPostAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#1287"><abbr title="Sparefoot\PitaService\Controller\ToolsController::addFeatureFlagAction">addFeatureFlagAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ToolsController.php.html#1305"><abbr title="Sparefoot\PitaService\Controller\ToolsController::deleteFeatureFlagAction">deleteFeatureFlagAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#452"><abbr title="Sparefoot\PitaService\Controller\UserController::isEmailValid">isEmailValid</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#463"><abbr title="Sparefoot\PitaService\Controller\UserController::_restrictFacilityAccess">_restrictFacilityAccess</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#1084"><abbr title="Sparefoot\PitaService\Controller\UserController::_addAcctMgmtUser">_addAcctMgmtUser</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#1203"><abbr title="Sparefoot\PitaService\Controller\UserController::_getActiveAcctIds">_getActiveAcctIds</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UserController.php.html#1231"><abbr title="Sparefoot\PitaService\Controller\UserController::_getActiveListingAvailIds">_getActiveListingAvailIds</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UtilitiesController.php.html#26"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="UtilitiesController.php.html#47"><abbr title="Sparefoot\PitaService\Controller\UtilitiesController::indexAction">indexAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="YellowpagesController.php.html#24"><abbr title="Sparefoot\PitaService\Controller\YellowpagesController::initBeforeControllerAction">initBeforeControllerAction</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#73"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::initGeneralData">initGeneralData</abbr></a></td><td class="text-right">4</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#299"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::authenticateUser">authenticateUser</abbr></a></td><td class="text-right">3</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#332"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::_getParam">_getParam</abbr></a></td><td class="text-right">2</td></tr>
       <tr><td><a href="AbstractRestrictedController.php.html#56"><abbr title="Sparefoot\PitaService\Controller\AbstractRestrictedController::init">init</abbr></a></td><td class="text-right">2</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jul 2 3:06:52 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([62,5,0,0,0,0,1,0,0,0,1,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([707,1,0,2,1,1,0,3,0,2,1,13], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,47,"<a href=\"AbstractGenericController.php.html#8\">Sparefoot\\PitaService\\Controller\\AbstractGenericController<\/a>"],[56.**************,49,"<a href=\"AbstractRestrictedController.php.html#15\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController<\/a>"],[0,18,"<a href=\"AbstractView\/GenericView.php.html#8\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView<\/a>"],[1.****************,344,"<a href=\"AccountController.php.html#11\">Sparefoot\\PitaService\\Controller\\AccountController<\/a>"],[0,15,"<a href=\"AccountView\/ListActionView.php.html#11\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView<\/a>"],[4.***************,38,"<a href=\"AffiliateController.php.html#12\">Sparefoot\\PitaService\\Controller\\AffiliateController<\/a>"],[7.***************,89,"<a href=\"AnalyticsController.php.html#14\">Sparefoot\\PitaService\\Controller\\AnalyticsController<\/a>"],[4.***************,121,"<a href=\"ApiController.php.html#14\">Sparefoot\\PitaService\\Controller\\ApiController<\/a>"],[7.***************,13,"<a href=\"BillingController.php.html#10\">Sparefoot\\PitaService\\Controller\\BillingController<\/a>"],[0,127,"<a href=\"BookingsController.php.html#11\">Sparefoot\\PitaService\\Controller\\BookingsController<\/a>"],[0,51,"<a href=\"BuyoutController.php.html#11\">Sparefoot\\PitaService\\Controller\\BuyoutController<\/a>"],[0,20,"<a href=\"CdpController.php.html#9\">Sparefoot\\PitaService\\Controller\\CdpController<\/a>"],[0,1,"<a href=\"CentaursearchController.php.html#8\">Sparefoot\\PitaService\\Controller\\CentaursearchController<\/a>"],[0,23,"<a href=\"CentaursearchapiController.php.html#9\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController<\/a>"],[0,5,"<a href=\"CitygridController.php.html#9\">Sparefoot\\PitaService\\Controller\\CitygridController<\/a>"],[0,4,"<a href=\"CpaPercentRolloutController.php.html#13\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController<\/a>"],[0,1,"<a href=\"CpaPercentRolloutView\/IndexActionView.php.html#10\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutView\\IndexActionView<\/a>"],[0,17,"<a href=\"DashboardController.php.html#10\">Sparefoot\\PitaService\\Controller\\DashboardController<\/a>"],[0,12,"<a href=\"DisastersController.php.html#9\">Sparefoot\\PitaService\\Controller\\DisastersController<\/a>"],[0,62,"<a href=\"DisputesController.php.html#11\">Sparefoot\\PitaService\\Controller\\DisputesController<\/a>"],[0,9,"<a href=\"EmailController.php.html#9\">Sparefoot\\PitaService\\Controller\\EmailController<\/a>"],[0,8,"<a href=\"ErrorController.php.html#10\">Sparefoot\\PitaService\\Controller\\ErrorController<\/a>"],[0,32,"<a href=\"FacilitiesController.php.html#10\">Sparefoot\\PitaService\\Controller\\FacilitiesController<\/a>"],[0,126,"<a href=\"FacilityController.php.html#24\">Sparefoot\\PitaService\\Controller\\FacilityController<\/a>"],[0,9,"<a href=\"FaqsController.php.html#10\">Sparefoot\\PitaService\\Controller\\FaqsController<\/a>"],[0,4,"<a href=\"FederatedController.php.html#11\">Sparefoot\\PitaService\\Controller\\FederatedController<\/a>"],[0,12,"<a href=\"FeedsController.php.html#9\">Sparefoot\\PitaService\\Controller\\FeedsController<\/a>"],[0,49,"<a href=\"FiltersController.php.html#11\">Sparefoot\\PitaService\\Controller\\FiltersController<\/a>"],[0,2,"<a href=\"HeatmapController.php.html#10\">Sparefoot\\PitaService\\Controller\\HeatmapController<\/a>"],[0,31,"<a href=\"IncentivesController.php.html#11\">Sparefoot\\PitaService\\Controller\\IncentivesController<\/a>"],[0,16,"<a href=\"IndexController.php.html#12\">Sparefoot\\PitaService\\Controller\\IndexController<\/a>"],[0,1,"<a href=\"IndexView\/IndexActionView.php.html#10\">Sparefoot\\PitaService\\Controller\\IndexView\\IndexActionView<\/a>"],[0,663,"<a href=\"InventoryController.php.html#13\">Sparefoot\\PitaService\\Controller\\InventoryController<\/a>"],[0,82,"<a href=\"JobsController.php.html#17\">Sparefoot\\PitaService\\Controller\\JobsController<\/a>"],[0,13,"<a href=\"LoginController.php.html#12\">Sparefoot\\PitaService\\Controller\\LoginController<\/a>"],[0,7,"<a href=\"MaintenanceModeController.php.html#9\">Sparefoot\\PitaService\\Controller\\MaintenanceModeController<\/a>"],[0,10,"<a href=\"NetsuiteController.php.html#10\">Sparefoot\\PitaService\\Controller\\NetsuiteController<\/a>"],[0,24,"<a href=\"OmnomController.php.html#9\">Sparefoot\\PitaService\\Controller\\OmnomController<\/a>"],[0,19,"<a href=\"PaidmediaController.php.html#10\">Sparefoot\\PitaService\\Controller\\PaidmediaController<\/a>"],[0,18,"<a href=\"PayoutController.php.html#12\">Sparefoot\\PitaService\\Controller\\PayoutController<\/a>"],[0,10,"<a href=\"PhotosController.php.html#10\">Sparefoot\\PitaService\\Controller\\PhotosController<\/a>"],[94.73684210526315,6,"<a href=\"PingController.php.html#11\">Sparefoot\\PitaService\\Controller\\PingController<\/a>"],[0,17,"<a href=\"PitaSearchController.php.html#13\">Sparefoot\\PitaService\\Controller\\PitaSearchController<\/a>"],[0,24,"<a href=\"ProxyController.php.html#16\">Sparefoot\\PitaService\\Controller\\ProxyController<\/a>"],[0,18,"<a href=\"PublicController.php.html#13\">Sparefoot\\PitaService\\Controller\\PublicController<\/a>"],[0,35,"<a href=\"QuickclientController.php.html#11\">Sparefoot\\PitaService\\Controller\\QuickclientController<\/a>"],[0,4,"<a href=\"QuickformController.php.html#9\">Sparefoot\\PitaService\\Controller\\QuickformController<\/a>"],[0,3,"<a href=\"QuickjobController.php.html#10\">Sparefoot\\PitaService\\Controller\\QuickjobController<\/a>"],[0,24,"<a href=\"QuickrepController.php.html#15\">Sparefoot\\PitaService\\Controller\\QuickrepController<\/a>"],[0,7,"<a href=\"QuicksoapController.php.html#16\">Sparefoot\\PitaService\\Controller\\QuicksoapController<\/a>"],[0,18,"<a href=\"QuicktaggerController.php.html#10\">Sparefoot\\PitaService\\Controller\\QuicktaggerController<\/a>"],[0,7,"<a href=\"ReferralsController.php.html#9\">Sparefoot\\PitaService\\Controller\\ReferralsController<\/a>"],[0,21,"<a href=\"ReportsController.php.html#14\">Sparefoot\\PitaService\\Controller\\ReportsController<\/a>"],[0,26,"<a href=\"ReviewsController.php.html#10\">Sparefoot\\PitaService\\Controller\\ReviewsController<\/a>"],[0,11,"<a href=\"RewardsController.php.html#9\">Sparefoot\\PitaService\\Controller\\RewardsController<\/a>"],[0,9,"<a href=\"SalesController.php.html#9\">Sparefoot\\PitaService\\Controller\\SalesController<\/a>"],[0,39,"<a href=\"SalesforceController.php.html#11\">Sparefoot\\PitaService\\Controller\\SalesforceController<\/a>"],[0,24,"<a href=\"SearchController.php.html#9\">Sparefoot\\PitaService\\Controller\\SearchController<\/a>"],[0,14,"<a href=\"ServiceareaController.php.html#10\">Sparefoot\\PitaService\\Controller\\ServiceareaController<\/a>"],[0,7,"<a href=\"SoftwarepartnerController.php.html#9\">Sparefoot\\PitaService\\Controller\\SoftwarepartnerController<\/a>"],[0,14,"<a href=\"SphinxsearchController.php.html#9\">Sparefoot\\PitaService\\Controller\\SphinxsearchController<\/a>"],[0,52,"<a href=\"StatementsController.php.html#14\">Sparefoot\\PitaService\\Controller\\StatementsController<\/a>"],[0,56,"<a href=\"TableauController.php.html#13\">Sparefoot\\PitaService\\Controller\\TableauController<\/a>"],[0,27,"<a href=\"TestController.php.html#10\">Sparefoot\\PitaService\\Controller\\TestController<\/a>"],[0,173,"<a href=\"ToolsController.php.html#13\">Sparefoot\\PitaService\\Controller\\ToolsController<\/a>"],[0,3,"<a href=\"TriController.php.html#9\">Sparefoot\\PitaService\\Controller\\TriController<\/a>"],[0,215,"<a href=\"UserController.php.html#13\">Sparefoot\\PitaService\\Controller\\UserController<\/a>"],[0,17,"<a href=\"UtilitiesController.php.html#12\">Sparefoot\\PitaService\\Controller\\UtilitiesController<\/a>"],[0,42,"<a href=\"YellowpagesController.php.html#9\">Sparefoot\\PitaService\\Controller\\YellowpagesController<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AbstractGenericController.php.html#17\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::__construct<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#37\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::getMetrics<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#42\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::getFilters<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#47\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::getCustoms<\/a>"],[0,4,"<a href=\"AbstractGenericController.php.html#58\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::export<\/a>"],[0,4,"<a href=\"AbstractGenericController.php.html#85\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::_exportReport<\/a>"],[0,2,"<a href=\"AbstractGenericController.php.html#104\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::_exportServerReport<\/a>"],[0,8,"<a href=\"AbstractGenericController.php.html#133\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::processQueryParams<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#161\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::newBuilder<\/a>"],[0,2,"<a href=\"AbstractGenericController.php.html#168\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::newQuery<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#180\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::processQuery<\/a>"],[0,9,"<a href=\"AbstractGenericController.php.html#188\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::processReport<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#217\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::getDefaultQuery<\/a>"],[0,2,"<a href=\"AbstractGenericController.php.html#222\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::tableFromReport<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#245\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::initFilterList<\/a>"],[0,7,"<a href=\"AbstractGenericController.php.html#272\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::populateFilter<\/a>"],[0,1,"<a href=\"AbstractGenericController.php.html#300\">Sparefoot\\PitaService\\Controller\\AbstractGenericController::toCamelCase<\/a>"],[100,1,"<a href=\"AbstractRestrictedController.php.html#23\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::__construct<\/a>"],[85.71428571428571,2,"<a href=\"AbstractRestrictedController.php.html#56\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::init<\/a>"],[86.95652173913044,4,"<a href=\"AbstractRestrictedController.php.html#73\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::initGeneralData<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#111\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::setTitlePage<\/a>"],[100,1,"<a href=\"AbstractRestrictedController.php.html#117\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::initHeadElement<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#150\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::setCss<\/a>"],[25,3,"<a href=\"AbstractRestrictedController.php.html#155\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::cssHeader<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#172\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::setScripts<\/a>"],[9.090909090909092,8,"<a href=\"AbstractRestrictedController.php.html#177\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::scriptFooter<\/a>"],[100,1,"<a href=\"AbstractRestrictedController.php.html#208\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::_createAcronym<\/a>"],[97.5609756097561,3,"<a href=\"AbstractRestrictedController.php.html#233\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::initAuthorization<\/a>"],[66.66666666666666,3,"<a href=\"AbstractRestrictedController.php.html#299\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::authenticateUser<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#311\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getLoggedUser<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#316\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getGenesisUserAccess<\/a>"],[0,2,"<a href=\"AbstractRestrictedController.php.html#321\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getParam<\/a>"],[66.66666666666666,2,"<a href=\"AbstractRestrictedController.php.html#332\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::_getParam<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#343\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::accessDeniedNotUse<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#356\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::accessDenied<\/a>"],[0,2,"<a href=\"AbstractRestrictedController.php.html#364\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getTwig<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#376\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::dispatchError<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#393\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::dispatchSuccess<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#415\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getControllerActionName<\/a>"],[0,4,"<a href=\"AbstractRestrictedController.php.html#437\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getOldClass<\/a>"],[0,2,"<a href=\"AbstractRestrictedController.php.html#448\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::getNewClass<\/a>"],[0,1,"<a href=\"AbstractRestrictedController.php.html#465\">Sparefoot\\PitaService\\Controller\\AbstractRestrictedController::dbConnectionInstance<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#10\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::get_class<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#15\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::implode<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#21\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisUtilVersionerVersion<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#27\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::strtotime<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#33\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::newDateTime<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#38\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::formatEstimatedMoney<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#44\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisUtilFormatterFormatDateDiff<\/a>"],[0,2,"<a href=\"AbstractView\/GenericView.php.html#60\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::getMetricInfoGetter<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#70\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisConfigServerIsProduction<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#76\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisEntitySiteCommission<\/a>"],[0,3,"<a href=\"AbstractView\/GenericView.php.html#95\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::genesisConst<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#112\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::dateFormat<\/a>"],[0,2,"<a href=\"AbstractView\/GenericView.php.html#117\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::getEnv<\/a>"],[0,1,"<a href=\"AbstractView\/GenericView.php.html#123\">Sparefoot\\PitaService\\Controller\\AbstractView\\GenericView::serializeQuery<\/a>"],[100,1,"<a href=\"AccountController.php.html#17\">Sparefoot\\PitaService\\Controller\\AccountController::__construct<\/a>"],[100,1,"<a href=\"AccountController.php.html#23\">Sparefoot\\PitaService\\Controller\\AccountController::_init<\/a>"],[35.***************,18,"<a href=\"AccountController.php.html#32\">Sparefoot\\PitaService\\Controller\\AccountController::listAction<\/a>"],[0,9,"<a href=\"AccountController.php.html#121\">Sparefoot\\PitaService\\Controller\\AccountController::_export<\/a>"],[0,10,"<a href=\"AccountController.php.html#180\">Sparefoot\\PitaService\\Controller\\AccountController::moveintegrationAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#271\">Sparefoot\\PitaService\\Controller\\AccountController::moveacctmgmtusersAction<\/a>"],[0,10,"<a href=\"AccountController.php.html#305\">Sparefoot\\PitaService\\Controller\\AccountController::syncAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#369\">Sparefoot\\PitaService\\Controller\\AccountController::_resetSync<\/a>"],[0,1,"<a href=\"AccountController.php.html#391\">Sparefoot\\PitaService\\Controller\\AccountController::changebidtypeAction<\/a>"],[0,1,"<a href=\"AccountController.php.html#416\">Sparefoot\\PitaService\\Controller\\AccountController::changesupportexistingltvreservationsAction<\/a>"],[0,6,"<a href=\"AccountController.php.html#442\">Sparefoot\\PitaService\\Controller\\AccountController::changebidsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#492\">Sparefoot\\PitaService\\Controller\\AccountController::changeaccountminimumbidAction<\/a>"],[0,4,"<a href=\"AccountController.php.html#522\">Sparefoot\\PitaService\\Controller\\AccountController::changeminimumbidsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#559\">Sparefoot\\PitaService\\Controller\\AccountController::changeexclusiveAction<\/a>"],[0,4,"<a href=\"AccountController.php.html#586\">Sparefoot\\PitaService\\Controller\\AccountController::changeprimarycontactAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#629\">Sparefoot\\PitaService\\Controller\\AccountController::changeofflinereservationscdpAction<\/a>"],[0,12,"<a href=\"AccountController.php.html#659\">Sparefoot\\PitaService\\Controller\\AccountController::verifyAction<\/a>"],[0,11,"<a href=\"AccountController.php.html#749\">Sparefoot\\PitaService\\Controller\\AccountController::getintegrationdetailsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#789\">Sparefoot\\PitaService\\Controller\\AccountController::unverifyAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#809\">Sparefoot\\PitaService\\Controller\\AccountController::editinfoAction<\/a>"],[0,6,"<a href=\"AccountController.php.html#842\">Sparefoot\\PitaService\\Controller\\AccountController::getacctdetailsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#905\">Sparefoot\\PitaService\\Controller\\AccountController::removeacctuserAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#931\">Sparefoot\\PitaService\\Controller\\AccountController::removeadminAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#955\">Sparefoot\\PitaService\\Controller\\AccountController::addacctadminAction<\/a>"],[0,43,"<a href=\"AccountController.php.html#988\">Sparefoot\\PitaService\\Controller\\AccountController::newacctAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#1253\">Sparefoot\\PitaService\\Controller\\AccountController::_makeUser<\/a>"],[0,1,"<a href=\"AccountController.php.html#1276\">Sparefoot\\PitaService\\Controller\\AccountController::_makeAcctMgmtUser<\/a>"],[0,31,"<a href=\"AccountController.php.html#1288\">Sparefoot\\PitaService\\Controller\\AccountController::_makeIntegration<\/a>"],[0,6,"<a href=\"AccountController.php.html#1490\">Sparefoot\\PitaService\\Controller\\AccountController::deleteaccountAction<\/a>"],[0,47,"<a href=\"AccountController.php.html#1529\">Sparefoot\\PitaService\\Controller\\AccountController::changeintegrationcredsAction<\/a>"],[0,9,"<a href=\"AccountController.php.html#1815\">Sparefoot\\PitaService\\Controller\\AccountController::getintegrationcredsAction<\/a>"],[0,7,"<a href=\"AccountController.php.html#1904\">Sparefoot\\PitaService\\Controller\\AccountController::geteditintegrationdetailsAction<\/a>"],[0,7,"<a href=\"AccountController.php.html#1951\">Sparefoot\\PitaService\\Controller\\AccountController::saveeditintegrationdetailsAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#2001\">Sparefoot\\PitaService\\Controller\\AccountController::getnotesAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#2026\">Sparefoot\\PitaService\\Controller\\AccountController::changenotesAction<\/a>"],[0,10,"<a href=\"AccountController.php.html#2053\">Sparefoot\\PitaService\\Controller\\AccountController::getbillableentitiesAction<\/a>"],[0,6,"<a href=\"AccountController.php.html#2126\">Sparefoot\\PitaService\\Controller\\AccountController::getbillableentitydetailsAction<\/a>"],[0,10,"<a href=\"AccountController.php.html#2174\">Sparefoot\\PitaService\\Controller\\AccountController::updateqbcustomerAction<\/a>"],[0,18,"<a href=\"AccountController.php.html#2270\">Sparefoot\\PitaService\\Controller\\AccountController::updatenscustomerAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#2401\">Sparefoot\\PitaService\\Controller\\AccountController::loadFacilityIdToFacilityMapForBillableEntity<\/a>"],[0,10,"<a href=\"AccountController.php.html#2413\">Sparefoot\\PitaService\\Controller\\AccountController::getbillableentityfacilitiesAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#2468\">Sparefoot\\PitaService\\Controller\\AccountController::updateadnetworkpriceAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#2502\">Sparefoot\\PitaService\\Controller\\AccountController::updatehostedwebsitepriceAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#2537\">Sparefoot\\PitaService\\Controller\\AccountController::changedocusigncompleteAction<\/a>"],[0,2,"<a href=\"AccountController.php.html#2562\">Sparefoot\\PitaService\\Controller\\AccountController::changetestaccountAction<\/a>"],[0,3,"<a href=\"AccountController.php.html#2582\">Sparefoot\\PitaService\\Controller\\AccountController::clearaccounttermsAction<\/a>"],[0,2,"<a href=\"AccountView\/ListActionView.php.html#20\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::getBidTypeContinueResidualMessage<\/a>"],[0,4,"<a href=\"AccountView\/ListActionView.php.html#37\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::getAdminsHtml<\/a>"],[0,1,"<a href=\"AccountView\/ListActionView.php.html#64\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::numCoporations<\/a>"],[0,1,"<a href=\"AccountView\/ListActionView.php.html#76\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::numFacilities<\/a>"],[0,2,"<a href=\"AccountView\/ListActionView.php.html#88\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::bidCurrency<\/a>"],[0,2,"<a href=\"AccountView\/ListActionView.php.html#105\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::labelText<\/a>"],[0,2,"<a href=\"AccountView\/ListActionView.php.html#132\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::formatDate<\/a>"],[0,1,"<a href=\"AccountView\/ListActionView.php.html#146\">Sparefoot\\PitaService\\Controller\\AccountView\\ListActionView::getPaymentTypes<\/a>"],[100,1,"<a href=\"AffiliateController.php.html#16\">Sparefoot\\PitaService\\Controller\\AffiliateController::__construct<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#28\">Sparefoot\\PitaService\\Controller\\AffiliateController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#36\">Sparefoot\\PitaService\\Controller\\AffiliateController::_init<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#44\">Sparefoot\\PitaService\\Controller\\AffiliateController::indexAction<\/a>"],[100,1,"<a href=\"AffiliateController.php.html#51\">Sparefoot\\PitaService\\Controller\\AffiliateController::listAction<\/a>"],[0,2,"<a href=\"AffiliateController.php.html#64\">Sparefoot\\PitaService\\Controller\\AffiliateController::generateapiAction<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#81\">Sparefoot\\PitaService\\Controller\\AffiliateController::_generateApiKey<\/a>"],[0,3,"<a href=\"AffiliateController.php.html#86\">Sparefoot\\PitaService\\Controller\\AffiliateController::createAction<\/a>"],[0,3,"<a href=\"AffiliateController.php.html#108\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processCreate<\/a>"],[0,4,"<a href=\"AffiliateController.php.html#138\">Sparefoot\\PitaService\\Controller\\AffiliateController::updateAction<\/a>"],[0,2,"<a href=\"AffiliateController.php.html#173\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processUpdate<\/a>"],[0,3,"<a href=\"AffiliateController.php.html#193\">Sparefoot\\PitaService\\Controller\\AffiliateController::addsiteAction<\/a>"],[0,3,"<a href=\"AffiliateController.php.html#215\">Sparefoot\\PitaService\\Controller\\AffiliateController::updatesiteAction<\/a>"],[0,4,"<a href=\"AffiliateController.php.html#249\">Sparefoot\\PitaService\\Controller\\AffiliateController::exclusionsAction<\/a>"],[0,5,"<a href=\"AffiliateController.php.html#287\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processSaveSite<\/a>"],[0,1,"<a href=\"AffiliateController.php.html#321\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processAddExclusion<\/a>"],[0,2,"<a href=\"AffiliateController.php.html#332\">Sparefoot\\PitaService\\Controller\\AffiliateController::_processDelExclusion<\/a>"],[0,1,"<a href=\"AnalyticsController.php.html#22\">Sparefoot\\PitaService\\Controller\\AnalyticsController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"AnalyticsController.php.html#30\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_init<\/a>"],[61.111111111111114,7,"<a href=\"AnalyticsController.php.html#39\">Sparefoot\\PitaService\\Controller\\AnalyticsController::indexAction<\/a>"],[0,2,"<a href=\"AnalyticsController.php.html#72\">Sparefoot\\PitaService\\Controller\\AnalyticsController::sqlAction<\/a>"],[0,1,"<a href=\"AnalyticsController.php.html#85\">Sparefoot\\PitaService\\Controller\\AnalyticsController::savequeryAction<\/a>"],[0,3,"<a href=\"AnalyticsController.php.html#95\">Sparefoot\\PitaService\\Controller\\AnalyticsController::loadqueryAction<\/a>"],[0,50,"<a href=\"AnalyticsController.php.html#156\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_export<\/a>"],[100,1,"<a href=\"AnalyticsController.php.html#313\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_getDefaultQuery<\/a>"],[0,16,"<a href=\"AnalyticsController.php.html#328\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_processQuery<\/a>"],[0,6,"<a href=\"AnalyticsController.php.html#428\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_processReport<\/a>"],[0,1,"<a href=\"AnalyticsController.php.html#456\">Sparefoot\\PitaService\\Controller\\AnalyticsController::_buildSql<\/a>"],[100,1,"<a href=\"ApiController.php.html#23\">Sparefoot\\PitaService\\Controller\\ApiController::__construct<\/a>"],[100,1,"<a href=\"ApiController.php.html#29\">Sparefoot\\PitaService\\Controller\\ApiController::_init<\/a>"],[21.21212121212121,23,"<a href=\"ApiController.php.html#49\">Sparefoot\\PitaService\\Controller\\ApiController::unitsAction<\/a>"],[0,27,"<a href=\"ApiController.php.html#164\">Sparefoot\\PitaService\\Controller\\ApiController::exportunitapiAction<\/a>"],[0,10,"<a href=\"ApiController.php.html#428\">Sparefoot\\PitaService\\Controller\\ApiController::facilitiesAction<\/a>"],[0,1,"<a href=\"ApiController.php.html#482\">Sparefoot\\PitaService\\Controller\\ApiController::_pullSiteLinkFacilityData<\/a>"],[0,1,"<a href=\"ApiController.php.html#495\">Sparefoot\\PitaService\\Controller\\ApiController::_pullQuikstorFacilityData<\/a>"],[0,2,"<a href=\"ApiController.php.html#507\">Sparefoot\\PitaService\\Controller\\ApiController::_pullDoorswapFacilityData<\/a>"],[0,1,"<a href=\"ApiController.php.html#529\">Sparefoot\\PitaService\\Controller\\ApiController::_pullStoredgeFacilityData<\/a>"],[0,1,"<a href=\"ApiController.php.html#536\">Sparefoot\\PitaService\\Controller\\ApiController::_pullCentershiftFacilityData<\/a>"],[0,5,"<a href=\"ApiController.php.html#548\">Sparefoot\\PitaService\\Controller\\ApiController::_pullStorageMartFacilityData<\/a>"],[0,3,"<a href=\"ApiController.php.html#610\">Sparefoot\\PitaService\\Controller\\ApiController::_pullStorageMartUnitData<\/a>"],[0,3,"<a href=\"ApiController.php.html#658\">Sparefoot\\PitaService\\Controller\\ApiController::_pullCentershiftUnitData<\/a>"],[0,2,"<a href=\"ApiController.php.html#684\">Sparefoot\\PitaService\\Controller\\ApiController::_pullUncleBobsUnitData<\/a>"],[0,4,"<a href=\"ApiController.php.html#702\">Sparefoot\\PitaService\\Controller\\ApiController::_pullCentershift4UnitData<\/a>"],[0,2,"<a href=\"ApiController.php.html#732\">Sparefoot\\PitaService\\Controller\\ApiController::_pullUsiUnitData<\/a>"],[0,3,"<a href=\"ApiController.php.html#744\">Sparefoot\\PitaService\\Controller\\ApiController::_pullCubesmartUnitData<\/a>"],[0,3,"<a href=\"ApiController.php.html#764\">Sparefoot\\PitaService\\Controller\\ApiController::_pullSelfStorageManagerUnitData<\/a>"],[0,1,"<a href=\"ApiController.php.html#789\">Sparefoot\\PitaService\\Controller\\ApiController::_pullStoredgeUnitData<\/a>"],[0,2,"<a href=\"ApiController.php.html#801\">Sparefoot\\PitaService\\Controller\\ApiController::_pullDoorswapUnitData<\/a>"],[40,8,"<a href=\"ApiController.php.html#829\">Sparefoot\\PitaService\\Controller\\ApiController::_pullEasyStorageSolutionsUnits<\/a>"],[0,7,"<a href=\"ApiController.php.html#866\">Sparefoot\\PitaService\\Controller\\ApiController::_pullExtraspaceUnits<\/a>"],[0,3,"<a href=\"ApiController.php.html#893\">Sparefoot\\PitaService\\Controller\\ApiController::_pullSiteLinkUnitData<\/a>"],[0,3,"<a href=\"ApiController.php.html#918\">Sparefoot\\PitaService\\Controller\\ApiController::_pullQuickstorUnitData<\/a>"],[0,4,"<a href=\"ApiController.php.html#944\">Sparefoot\\PitaService\\Controller\\ApiController::_pullDomicoUnitData<\/a>"],[0,1,"<a href=\"BillingController.php.html#18\">Sparefoot\\PitaService\\Controller\\BillingController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"BillingController.php.html#26\">Sparefoot\\PitaService\\Controller\\BillingController::_init<\/a>"],[0,1,"<a href=\"BillingController.php.html#32\">Sparefoot\\PitaService\\Controller\\BillingController::indexAction<\/a>"],[0,2,"<a href=\"BillingController.php.html#48\">Sparefoot\\PitaService\\Controller\\BillingController::exportemailsAction<\/a>"],[100,1,"<a href=\"BillingController.php.html#76\">Sparefoot\\PitaService\\Controller\\BillingController::openAction<\/a>"],[0,1,"<a href=\"BillingController.php.html#93\">Sparefoot\\PitaService\\Controller\\BillingController::closeAction<\/a>"],[0,2,"<a href=\"BillingController.php.html#108\">Sparefoot\\PitaService\\Controller\\BillingController::deleteAction<\/a>"],[0,4,"<a href=\"BillingController.php.html#124\">Sparefoot\\PitaService\\Controller\\BillingController::_runQuery<\/a>"],[0,1,"<a href=\"BookingsController.php.html#15\">Sparefoot\\PitaService\\Controller\\BookingsController::__construct<\/a>"],[0,2,"<a href=\"BookingsController.php.html#27\">Sparefoot\\PitaService\\Controller\\BookingsController::initBeforeControllerAction<\/a>"],[0,4,"<a href=\"BookingsController.php.html#49\">Sparefoot\\PitaService\\Controller\\BookingsController::indexAction<\/a>"],[0,2,"<a href=\"BookingsController.php.html#107\">Sparefoot\\PitaService\\Controller\\BookingsController::confcodeAction<\/a>"],[0,2,"<a href=\"BookingsController.php.html#129\">Sparefoot\\PitaService\\Controller\\BookingsController::salesforceAction<\/a>"],[0,1,"<a href=\"BookingsController.php.html#185\">Sparefoot\\PitaService\\Controller\\BookingsController::getqueryAction<\/a>"],[0,3,"<a href=\"BookingsController.php.html#199\">Sparefoot\\PitaService\\Controller\\BookingsController::historyAction<\/a>"],[0,15,"<a href=\"BookingsController.php.html#217\">Sparefoot\\PitaService\\Controller\\BookingsController::getbookinginfoAction<\/a>"],[0,6,"<a href=\"BookingsController.php.html#296\">Sparefoot\\PitaService\\Controller\\BookingsController::jsondetailsAction<\/a>"],[0,2,"<a href=\"BookingsController.php.html#336\">Sparefoot\\PitaService\\Controller\\BookingsController::changemoveinAction<\/a>"],[0,2,"<a href=\"BookingsController.php.html#364\">Sparefoot\\PitaService\\Controller\\BookingsController::changeunitpriceAction<\/a>"],[0,3,"<a href=\"BookingsController.php.html#383\">Sparefoot\\PitaService\\Controller\\BookingsController::changemoveoutAction<\/a>"],[0,6,"<a href=\"BookingsController.php.html#408\">Sparefoot\\PitaService\\Controller\\BookingsController::addduplicateAction<\/a>"],[0,15,"<a href=\"BookingsController.php.html#468\">Sparefoot\\PitaService\\Controller\\BookingsController::changebookingsAction<\/a>"],[0,20,"<a href=\"BookingsController.php.html#543\">Sparefoot\\PitaService\\Controller\\BookingsController::_changeBookingState<\/a>"],[0,3,"<a href=\"BookingsController.php.html#609\">Sparefoot\\PitaService\\Controller\\BookingsController::makebookingresidualAction<\/a>"],[0,18,"<a href=\"BookingsController.php.html#632\">Sparefoot\\PitaService\\Controller\\BookingsController::changenotesAction<\/a>"],[0,4,"<a href=\"BookingsController.php.html#856\">Sparefoot\\PitaService\\Controller\\BookingsController::getunitinfoAction<\/a>"],[0,5,"<a href=\"BookingsController.php.html#894\">Sparefoot\\PitaService\\Controller\\BookingsController::previewemailsAction<\/a>"],[0,6,"<a href=\"BookingsController.php.html#935\">Sparefoot\\PitaService\\Controller\\BookingsController::resendemailsAction<\/a>"],[0,1,"<a href=\"BookingsController.php.html#975\">Sparefoot\\PitaService\\Controller\\BookingsController::moreActionsAction<\/a>"],[0,6,"<a href=\"BookingsController.php.html#990\">Sparefoot\\PitaService\\Controller\\BookingsController::sitelinkledgerAction<\/a>"],[0,1,"<a href=\"BuyoutController.php.html#24\">Sparefoot\\PitaService\\Controller\\BuyoutController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"BuyoutController.php.html#41\">Sparefoot\\PitaService\\Controller\\BuyoutController::_init<\/a>"],[0,1,"<a href=\"BuyoutController.php.html#49\">Sparefoot\\PitaService\\Controller\\BuyoutController::indexAction<\/a>"],[0,6,"<a href=\"BuyoutController.php.html#68\">Sparefoot\\PitaService\\Controller\\BuyoutController::createAction<\/a>"],[0,5,"<a href=\"BuyoutController.php.html#119\">Sparefoot\\PitaService\\Controller\\BuyoutController::editAction<\/a>"],[0,2,"<a href=\"BuyoutController.php.html#164\">Sparefoot\\PitaService\\Controller\\BuyoutController::viewAction<\/a>"],[0,3,"<a href=\"BuyoutController.php.html#185\">Sparefoot\\PitaService\\Controller\\BuyoutController::invoiceAction<\/a>"],[0,3,"<a href=\"BuyoutController.php.html#212\">Sparefoot\\PitaService\\Controller\\BuyoutController::deleteAction<\/a>"],[0,6,"<a href=\"BuyoutController.php.html#235\">Sparefoot\\PitaService\\Controller\\BuyoutController::downloadAction<\/a>"],[0,4,"<a href=\"BuyoutController.php.html#280\">Sparefoot\\PitaService\\Controller\\BuyoutController::editUpdateAction<\/a>"],[0,1,"<a href=\"BuyoutController.php.html#308\">Sparefoot\\PitaService\\Controller\\BuyoutController::editUpdateQuoteAction<\/a>"],[0,3,"<a href=\"BuyoutController.php.html#319\">Sparefoot\\PitaService\\Controller\\BuyoutController::editBookingsAction<\/a>"],[0,7,"<a href=\"BuyoutController.php.html#340\">Sparefoot\\PitaService\\Controller\\BuyoutController::_updatePendingBookings<\/a>"],[0,8,"<a href=\"BuyoutController.php.html#378\">Sparefoot\\PitaService\\Controller\\BuyoutController::_updateExistingTenants<\/a>"],[0,1,"<a href=\"CdpController.php.html#17\">Sparefoot\\PitaService\\Controller\\CdpController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"CdpController.php.html#24\">Sparefoot\\PitaService\\Controller\\CdpController::_init<\/a>"],[0,1,"<a href=\"CdpController.php.html#33\">Sparefoot\\PitaService\\Controller\\CdpController::indexAction<\/a>"],[0,3,"<a href=\"CdpController.php.html#42\">Sparefoot\\PitaService\\Controller\\CdpController::matchRunsAction<\/a>"],[0,2,"<a href=\"CdpController.php.html#74\">Sparefoot\\PitaService\\Controller\\CdpController::matchAccountsAction<\/a>"],[0,1,"<a href=\"CdpController.php.html#95\">Sparefoot\\PitaService\\Controller\\CdpController::matchFacilitiesAction<\/a>"],[0,2,"<a href=\"CdpController.php.html#133\">Sparefoot\\PitaService\\Controller\\CdpController::matchBookingsAction<\/a>"],[0,3,"<a href=\"CdpController.php.html#159\">Sparefoot\\PitaService\\Controller\\CdpController::killMatchRunAction<\/a>"],[0,4,"<a href=\"CdpController.php.html#185\">Sparefoot\\PitaService\\Controller\\CdpController::viewLogAction<\/a>"],[0,2,"<a href=\"CdpController.php.html#213\">Sparefoot\\PitaService\\Controller\\CdpController::runCdpAction<\/a>"],[0,1,"<a href=\"CentaursearchController.php.html#10\">Sparefoot\\PitaService\\Controller\\CentaursearchController::index<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#17\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#42\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_getApiResponse<\/a>"],[0,6,"<a href=\"CentaursearchapiController.php.html#57\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_getApiResponseTest<\/a>"],[0,3,"<a href=\"CentaursearchapiController.php.html#97\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_init<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#113\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::indexAction<\/a>"],[0,9,"<a href=\"CentaursearchapiController.php.html#119\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::testAction<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#212\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_initWeights<\/a>"],[0,1,"<a href=\"CentaursearchapiController.php.html#222\">Sparefoot\\PitaService\\Controller\\CentaursearchapiController::_conductSearch<\/a>"],[0,1,"<a href=\"CitygridController.php.html#17\">Sparefoot\\PitaService\\Controller\\CitygridController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"CitygridController.php.html#24\">Sparefoot\\PitaService\\Controller\\CitygridController::_init<\/a>"],[0,1,"<a href=\"CitygridController.php.html#29\">Sparefoot\\PitaService\\Controller\\CitygridController::resyncAction<\/a>"],[0,1,"<a href=\"CitygridController.php.html#39\">Sparefoot\\PitaService\\Controller\\CitygridController::indexAction<\/a>"],[0,1,"<a href=\"CitygridController.php.html#46\">Sparefoot\\PitaService\\Controller\\CitygridController::listAction<\/a>"],[0,1,"<a href=\"CpaPercentRolloutController.php.html#21\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"CpaPercentRolloutController.php.html#29\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController::_init<\/a>"],[0,1,"<a href=\"CpaPercentRolloutController.php.html#38\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController::indexAction<\/a>"],[0,1,"<a href=\"CpaPercentRolloutController.php.html#57\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutController::dashboardAction<\/a>"],[0,1,"<a href=\"CpaPercentRolloutView\/IndexActionView.php.html#13\">Sparefoot\\PitaService\\Controller\\CpaPercentRolloutView\\IndexActionView::usortReports<\/a>"],[0,1,"<a href=\"DashboardController.php.html#16\">Sparefoot\\PitaService\\Controller\\DashboardController::_init<\/a>"],[0,1,"<a href=\"DashboardController.php.html#23\">Sparefoot\\PitaService\\Controller\\DashboardController::index<\/a>"],[0,1,"<a href=\"DashboardController.php.html#37\">Sparefoot\\PitaService\\Controller\\DashboardController::indexAction<\/a>"],[0,1,"<a href=\"DashboardController.php.html#43\">Sparefoot\\PitaService\\Controller\\DashboardController::_loadData<\/a>"],[0,1,"<a href=\"DashboardController.php.html#104\">Sparefoot\\PitaService\\Controller\\DashboardController::initDashboardLayout<\/a>"],[0,6,"<a href=\"DashboardController.php.html#113\">Sparefoot\\PitaService\\Controller\\DashboardController::bookingsAction<\/a>"],[0,1,"<a href=\"DashboardController.php.html#180\">Sparefoot\\PitaService\\Controller\\DashboardController::initDataForBookingsView<\/a>"],[0,5,"<a href=\"DashboardController.php.html#185\">Sparefoot\\PitaService\\Controller\\DashboardController::initDailyBookingsProjection<\/a>"],[0,1,"<a href=\"DisastersController.php.html#15\">Sparefoot\\PitaService\\Controller\\DisastersController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"DisastersController.php.html#24\">Sparefoot\\PitaService\\Controller\\DisastersController::indexAction<\/a>"],[0,2,"<a href=\"DisastersController.php.html#40\">Sparefoot\\PitaService\\Controller\\DisastersController::facilitiesAction<\/a>"],[0,3,"<a href=\"DisastersController.php.html#57\">Sparefoot\\PitaService\\Controller\\DisastersController::includeContainedFacilities<\/a>"],[0,2,"<a href=\"DisastersController.php.html#104\">Sparefoot\\PitaService\\Controller\\DisastersController::addDisasterAction<\/a>"],[0,3,"<a href=\"DisastersController.php.html#130\">Sparefoot\\PitaService\\Controller\\DisastersController::editDisasterAction<\/a>"],[0,1,"<a href=\"DisputesController.php.html#19\">Sparefoot\\PitaService\\Controller\\DisputesController::initBeforeControllerAction<\/a>"],[0,3,"<a href=\"DisputesController.php.html#25\">Sparefoot\\PitaService\\Controller\\DisputesController::_init<\/a>"],[0,1,"<a href=\"DisputesController.php.html#45\">Sparefoot\\PitaService\\Controller\\DisputesController::indexAction<\/a>"],[0,6,"<a href=\"DisputesController.php.html#57\">Sparefoot\\PitaService\\Controller\\DisputesController::opendisputesAction<\/a>"],[0,6,"<a href=\"DisputesController.php.html#93\">Sparefoot\\PitaService\\Controller\\DisputesController::closeddisputesAction<\/a>"],[0,1,"<a href=\"DisputesController.php.html#127\">Sparefoot\\PitaService\\Controller\\DisputesController::reviewdisputeAction<\/a>"],[0,9,"<a href=\"DisputesController.php.html#149\">Sparefoot\\PitaService\\Controller\\DisputesController::applyRuling<\/a>"],[0,7,"<a href=\"DisputesController.php.html#214\">Sparefoot\\PitaService\\Controller\\DisputesController::validateBookingIsReviewable<\/a>"],[0,3,"<a href=\"DisputesController.php.html#239\">Sparefoot\\PitaService\\Controller\\DisputesController::updateDisputeRuling<\/a>"],[0,3,"<a href=\"DisputesController.php.html#268\">Sparefoot\\PitaService\\Controller\\DisputesController::updateDisputeReviewRulingReason<\/a>"],[0,3,"<a href=\"DisputesController.php.html#284\">Sparefoot\\PitaService\\Controller\\DisputesController::updateDisputeReviewNotes<\/a>"],[0,2,"<a href=\"DisputesController.php.html#300\">Sparefoot\\PitaService\\Controller\\DisputesController::deleteDisputeReviewNotes<\/a>"],[0,3,"<a href=\"DisputesController.php.html#309\">Sparefoot\\PitaService\\Controller\\DisputesController::updateDisputeReviewAgent<\/a>"],[0,1,"<a href=\"DisputesController.php.html#333\">Sparefoot\\PitaService\\Controller\\DisputesController::_exportOpenDisputes<\/a>"],[0,1,"<a href=\"DisputesController.php.html#346\">Sparefoot\\PitaService\\Controller\\DisputesController::_exportClosedDisputes<\/a>"],[0,3,"<a href=\"DisputesController.php.html#359\">Sparefoot\\PitaService\\Controller\\DisputesController::_createCsv<\/a>"],[0,1,"<a href=\"DisputesController.php.html#375\">Sparefoot\\PitaService\\Controller\\DisputesController::_getCsvHeaders<\/a>"],[0,2,"<a href=\"DisputesController.php.html#418\">Sparefoot\\PitaService\\Controller\\DisputesController::_getCsvLine<\/a>"],[0,2,"<a href=\"DisputesController.php.html#476\">Sparefoot\\PitaService\\Controller\\DisputesController::_cleanCsvLine<\/a>"],[0,1,"<a href=\"DisputesController.php.html#485\">Sparefoot\\PitaService\\Controller\\DisputesController::_getDefaultQuery<\/a>"],[0,1,"<a href=\"DisputesController.php.html#492\">Sparefoot\\PitaService\\Controller\\DisputesController::_processQuery<\/a>"],[0,1,"<a href=\"DisputesController.php.html#501\">Sparefoot\\PitaService\\Controller\\DisputesController::_processOpenDisputesReport<\/a>"],[0,1,"<a href=\"DisputesController.php.html#512\">Sparefoot\\PitaService\\Controller\\DisputesController::_processClosedDisputesReport<\/a>"],[0,1,"<a href=\"EmailController.php.html#15\">Sparefoot\\PitaService\\Controller\\EmailController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"EmailController.php.html#21\">Sparefoot\\PitaService\\Controller\\EmailController::_init<\/a>"],[0,1,"<a href=\"EmailController.php.html#49\">Sparefoot\\PitaService\\Controller\\EmailController::indexAction<\/a>"],[0,1,"<a href=\"EmailController.php.html#68\">Sparefoot\\PitaService\\Controller\\EmailController::ajaxemailsAction<\/a>"],[0,4,"<a href=\"EmailController.php.html#83\">Sparefoot\\PitaService\\Controller\\EmailController::ajaxsendAction<\/a>"],[0,1,"<a href=\"EmailController.php.html#111\">Sparefoot\\PitaService\\Controller\\EmailController::_getGreenArrowAddresses<\/a>"],[0,1,"<a href=\"ErrorController.php.html#20\">Sparefoot\\PitaService\\Controller\\ErrorController::initBeforeControllerAction<\/a>"],[0,5,"<a href=\"ErrorController.php.html#29\">Sparefoot\\PitaService\\Controller\\ErrorController::errorAction<\/a>"],[0,2,"<a href=\"ErrorController.php.html#57\">Sparefoot\\PitaService\\Controller\\ErrorController::hipchatAction<\/a>"],[0,1,"<a href=\"FacilitiesController.php.html#16\">Sparefoot\\PitaService\\Controller\\FacilitiesController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"FacilitiesController.php.html#22\">Sparefoot\\PitaService\\Controller\\FacilitiesController::_init<\/a>"],[0,18,"<a href=\"FacilitiesController.php.html#32\">Sparefoot\\PitaService\\Controller\\FacilitiesController::indexAction<\/a>"],[0,2,"<a href=\"FacilitiesController.php.html#117\">Sparefoot\\PitaService\\Controller\\FacilitiesController::bysourceAction<\/a>"],[0,4,"<a href=\"FacilitiesController.php.html#142\">Sparefoot\\PitaService\\Controller\\FacilitiesController::getqueryAction<\/a>"],[0,6,"<a href=\"FacilitiesController.php.html#178\">Sparefoot\\PitaService\\Controller\\FacilitiesController::loadbyidAction<\/a>"],[0,1,"<a href=\"FacilityController.php.html#30\">Sparefoot\\PitaService\\Controller\\FacilityController::initBeforeControllerAction<\/a>"],[0,24,"<a href=\"FacilityController.php.html#37\">Sparefoot\\PitaService\\Controller\\FacilityController::_init<\/a>"],[0,12,"<a href=\"FacilityController.php.html#74\">Sparefoot\\PitaService\\Controller\\FacilityController::indexAction<\/a>"],[0,4,"<a href=\"FacilityController.php.html#147\">Sparefoot\\PitaService\\Controller\\FacilityController::acquiretwilionumberAction<\/a>"],[0,1,"<a href=\"FacilityController.php.html#190\">Sparefoot\\PitaService\\Controller\\FacilityController::createJsonResponse<\/a>"],[0,4,"<a href=\"FacilityController.php.html#202\">Sparefoot\\PitaService\\Controller\\FacilityController::releasetwilionumberAction<\/a>"],[0,7,"<a href=\"FacilityController.php.html#239\">Sparefoot\\PitaService\\Controller\\FacilityController::_fetchInventoryData<\/a>"],[0,4,"<a href=\"FacilityController.php.html#274\">Sparefoot\\PitaService\\Controller\\FacilityController::jsonunitlistdetailsAction<\/a>"],[0,8,"<a href=\"FacilityController.php.html#324\">Sparefoot\\PitaService\\Controller\\FacilityController::jsonunitdetailsAction<\/a>"],[0,23,"<a href=\"FacilityController.php.html#393\">Sparefoot\\PitaService\\Controller\\FacilityController::_loadFacilities<\/a>"],[0,1,"<a href=\"FacilityController.php.html#540\">Sparefoot\\PitaService\\Controller\\FacilityController::getqueryAction<\/a>"],[0,15,"<a href=\"FacilityController.php.html#558\">Sparefoot\\PitaService\\Controller\\FacilityController::_tableFromReport<\/a>"],[0,7,"<a href=\"FacilityController.php.html#644\">Sparefoot\\PitaService\\Controller\\FacilityController::_initFilter<\/a>"],[0,1,"<a href=\"FacilityController.php.html#692\">Sparefoot\\PitaService\\Controller\\FacilityController::directoryAction<\/a>"],[0,3,"<a href=\"FacilityController.php.html#707\">Sparefoot\\PitaService\\Controller\\FacilityController::activateAction<\/a>"],[0,3,"<a href=\"FacilityController.php.html#734\">Sparefoot\\PitaService\\Controller\\FacilityController::deactivateAction<\/a>"],[0,5,"<a href=\"FacilityController.php.html#761\">Sparefoot\\PitaService\\Controller\\FacilityController::exportdirectoryrecordingsAction<\/a>"],[0,1,"<a href=\"FacilityController.php.html#825\">Sparefoot\\PitaService\\Controller\\FacilityController::getnotesAction<\/a>"],[0,2,"<a href=\"FacilityController.php.html#840\">Sparefoot\\PitaService\\Controller\\FacilityController::changenotesAction<\/a>"],[0,1,"<a href=\"FaqsController.php.html#19\">Sparefoot\\PitaService\\Controller\\FaqsController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"FaqsController.php.html#27\">Sparefoot\\PitaService\\Controller\\FaqsController::_init<\/a>"],[0,1,"<a href=\"FaqsController.php.html#32\">Sparefoot\\PitaService\\Controller\\FaqsController::indexAction<\/a>"],[0,3,"<a href=\"FaqsController.php.html#42\">Sparefoot\\PitaService\\Controller\\FaqsController::getAction<\/a>"],[0,3,"<a href=\"FaqsController.php.html#67\">Sparefoot\\PitaService\\Controller\\FaqsController::saveAction<\/a>"],[0,1,"<a href=\"FederatedController.php.html#17\">Sparefoot\\PitaService\\Controller\\FederatedController::initBeforeControllerAction<\/a>"],[0,2,"<a href=\"FederatedController.php.html#23\">Sparefoot\\PitaService\\Controller\\FederatedController::index<\/a>"],[0,1,"<a href=\"FederatedController.php.html#40\">Sparefoot\\PitaService\\Controller\\FederatedController::awsAction<\/a>"],[0,2,"<a href=\"FeedsController.php.html#11\">Sparefoot\\PitaService\\Controller\\FeedsController::citysearchAction<\/a>"],[0,3,"<a href=\"FeedsController.php.html#50\">Sparefoot\\PitaService\\Controller\\FeedsController::_prepHours<\/a>"],[0,2,"<a href=\"FeedsController.php.html#68\">Sparefoot\\PitaService\\Controller\\FeedsController::_facilityDetailsUrl<\/a>"],[0,1,"<a href=\"FeedsController.php.html#88\">Sparefoot\\PitaService\\Controller\\FeedsController::_prepUrl<\/a>"],[0,4,"<a href=\"FeedsController.php.html#93\">Sparefoot\\PitaService\\Controller\\FeedsController::_urlTitle<\/a>"],[0,49,"<a href=\"FiltersController.php.html#19\">Sparefoot\\PitaService\\Controller\\FiltersController::loadAction<\/a>"],[0,1,"<a href=\"HeatmapController.php.html#16\">Sparefoot\\PitaService\\Controller\\HeatmapController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"HeatmapController.php.html#23\">Sparefoot\\PitaService\\Controller\\HeatmapController::index<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#28\">Sparefoot\\PitaService\\Controller\\IncentivesController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#36\">Sparefoot\\PitaService\\Controller\\IncentivesController::_init<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#47\">Sparefoot\\PitaService\\Controller\\IncentivesController::indexAction<\/a>"],[0,4,"<a href=\"IncentivesController.php.html#54\">Sparefoot\\PitaService\\Controller\\IncentivesController::redemptionRequestsAction<\/a>"],[0,2,"<a href=\"IncentivesController.php.html#88\">Sparefoot\\PitaService\\Controller\\IncentivesController::updateStatusAction<\/a>"],[0,3,"<a href=\"IncentivesController.php.html#101\">Sparefoot\\PitaService\\Controller\\IncentivesController::parseIncentiveResponse<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#116\">Sparefoot\\PitaService\\Controller\\IncentivesController::getRedemptionRequests<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#129\">Sparefoot\\PitaService\\Controller\\IncentivesController::getRedemptionRequest<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#139\">Sparefoot\\PitaService\\Controller\\IncentivesController::getOffer<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#149\">Sparefoot\\PitaService\\Controller\\IncentivesController::getIncentive<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#159\">Sparefoot\\PitaService\\Controller\\IncentivesController::setStatus<\/a>"],[0,5,"<a href=\"IncentivesController.php.html#172\">Sparefoot\\PitaService\\Controller\\IncentivesController::addIncentiveAndBookingDataToView<\/a>"],[0,8,"<a href=\"IncentivesController.php.html#199\">Sparefoot\\PitaService\\Controller\\IncentivesController::searchRequestsAction<\/a>"],[0,1,"<a href=\"IncentivesController.php.html#234\">Sparefoot\\PitaService\\Controller\\IncentivesController::getRedemptionRequestsAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#18\">Sparefoot\\PitaService\\Controller\\IndexController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#25\">Sparefoot\\PitaService\\Controller\\IndexController::_getQuickRepContainer<\/a>"],[0,1,"<a href=\"IndexController.php.html#30\">Sparefoot\\PitaService\\Controller\\IndexController::indexAction<\/a>"],[0,2,"<a href=\"IndexController.php.html#39\">Sparefoot\\PitaService\\Controller\\IndexController::oldIndexAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#65\">Sparefoot\\PitaService\\Controller\\IndexController::fatalerrorAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#75\">Sparefoot\\PitaService\\Controller\\IndexController::exceptionAction<\/a>"],[0,3,"<a href=\"IndexController.php.html#92\">Sparefoot\\PitaService\\Controller\\IndexController::addBookmarkAction<\/a>"],[0,4,"<a href=\"IndexController.php.html#129\">Sparefoot\\PitaService\\Controller\\IndexController::updateBookmarksAction<\/a>"],[0,1,"<a href=\"IndexController.php.html#166\">Sparefoot\\PitaService\\Controller\\IndexController::_loadTimespanBookingData<\/a>"],[0,1,"<a href=\"IndexController.php.html#229\">Sparefoot\\PitaService\\Controller\\IndexController::auditAction<\/a>"],[0,1,"<a href=\"IndexView\/IndexActionView.php.html#12\">Sparefoot\\PitaService\\Controller\\IndexView\\IndexActionView::usortReports<\/a>"],[0,28,"<a href=\"InventoryController.php.html#19\">Sparefoot\\PitaService\\Controller\\InventoryController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#64\">Sparefoot\\PitaService\\Controller\\InventoryController::indexAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#98\">Sparefoot\\PitaService\\Controller\\InventoryController::mainAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#108\">Sparefoot\\PitaService\\Controller\\InventoryController::loadpercentcompleteAction<\/a>"],[0,28,"<a href=\"InventoryController.php.html#138\">Sparefoot\\PitaService\\Controller\\InventoryController::loadfacilitiesAction<\/a>"],[0,5,"<a href=\"InventoryController.php.html#285\">Sparefoot\\PitaService\\Controller\\InventoryController::_getFacilitiesInDeletedAccounts<\/a>"],[0,6,"<a href=\"InventoryController.php.html#328\">Sparefoot\\PitaService\\Controller\\InventoryController::accountlistAction<\/a>"],[0,12,"<a href=\"InventoryController.php.html#360\">Sparefoot\\PitaService\\Controller\\InventoryController::productlistAction<\/a>"],[0,14,"<a href=\"InventoryController.php.html#412\">Sparefoot\\PitaService\\Controller\\InventoryController::toggleproductAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#484\">Sparefoot\\PitaService\\Controller\\InventoryController::togglehostedwebsitetypeAction<\/a>"],[0,7,"<a href=\"InventoryController.php.html#515\">Sparefoot\\PitaService\\Controller\\InventoryController::updateadnetworkpriceAction<\/a>"],[0,7,"<a href=\"InventoryController.php.html#568\">Sparefoot\\PitaService\\Controller\\InventoryController::updatehostedwebsitepriceAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#621\">Sparefoot\\PitaService\\Controller\\InventoryController::updatetenantconnectprecalldigitsAction<\/a>"],[0,18,"<a href=\"InventoryController.php.html#646\">Sparefoot\\PitaService\\Controller\\InventoryController::loadinfoAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#777\">Sparefoot\\PitaService\\Controller\\InventoryController::loadresaleratesAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#817\">Sparefoot\\PitaService\\Controller\\InventoryController::updateresalebucketAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#839\">Sparefoot\\PitaService\\Controller\\InventoryController::deleteresalebucketAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#861\">Sparefoot\\PitaService\\Controller\\InventoryController::addupdateresalebucketAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#892\">Sparefoot\\PitaService\\Controller\\InventoryController::unithtmlAction<\/a>"],[0,8,"<a href=\"InventoryController.php.html#931\">Sparefoot\\PitaService\\Controller\\InventoryController::approveAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#980\">Sparefoot\\PitaService\\Controller\\InventoryController::facilityapproveAction<\/a>"],[0,97,"<a href=\"InventoryController.php.html#1010\">Sparefoot\\PitaService\\Controller\\InventoryController::jsondetailsAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#1371\">Sparefoot\\PitaService\\Controller\\InventoryController::updatecustomclosureAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#1397\">Sparefoot\\PitaService\\Controller\\InventoryController::removecustomclosureAction<\/a>"],[0,76,"<a href=\"InventoryController.php.html#1416\">Sparefoot\\PitaService\\Controller\\InventoryController::updatedetailsAction<\/a>"],[0,37,"<a href=\"InventoryController.php.html#1857\">Sparefoot\\PitaService\\Controller\\InventoryController::multiplefacilityupdateAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2047\">Sparefoot\\PitaService\\Controller\\InventoryController::billableentitylistAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2066\">Sparefoot\\PitaService\\Controller\\InventoryController::_makeBillableEntityList<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2083\">Sparefoot\\PitaService\\Controller\\InventoryController::streetviewAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#2119\">Sparefoot\\PitaService\\Controller\\InventoryController::updatestreetviewAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2157\">Sparefoot\\PitaService\\Controller\\InventoryController::loadlogAction<\/a>"],[0,54,"<a href=\"InventoryController.php.html#2178\">Sparefoot\\PitaService\\Controller\\InventoryController::unitAction<\/a>"],[0,10,"<a href=\"InventoryController.php.html#2467\">Sparefoot\\PitaService\\Controller\\InventoryController::photoframeAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#2529\">Sparefoot\\PitaService\\Controller\\InventoryController::getyieldavgsAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2547\">Sparefoot\\PitaService\\Controller\\InventoryController::loadyieldjsonAction<\/a>"],[0,5,"<a href=\"InventoryController.php.html#2567\">Sparefoot\\PitaService\\Controller\\InventoryController::saveyieldAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#2614\">Sparefoot\\PitaService\\Controller\\InventoryController::visibilityAction<\/a>"],[0,21,"<a href=\"InventoryController.php.html#2631\">Sparefoot\\PitaService\\Controller\\InventoryController::amenitiescheckAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2722\">Sparefoot\\PitaService\\Controller\\InventoryController::visibilitycheckAction<\/a>"],[0,14,"<a href=\"InventoryController.php.html#2786\">Sparefoot\\PitaService\\Controller\\InventoryController::acctfaclistAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2875\">Sparefoot\\PitaService\\Controller\\InventoryController::syncAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2901\">Sparefoot\\PitaService\\Controller\\InventoryController::phidosyncAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#2923\">Sparefoot\\PitaService\\Controller\\InventoryController::refreshsearchAction<\/a>"],[0,8,"<a href=\"InventoryController.php.html#2929\">Sparefoot\\PitaService\\Controller\\InventoryController::_refreshSearch<\/a>"],[0,2,"<a href=\"InventoryController.php.html#2985\">Sparefoot\\PitaService\\Controller\\InventoryController::resetlocationAction<\/a>"],[0,5,"<a href=\"InventoryController.php.html#3011\">Sparefoot\\PitaService\\Controller\\InventoryController::unitexportAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#3058\">Sparefoot\\PitaService\\Controller\\InventoryController::logexportAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#3089\">Sparefoot\\PitaService\\Controller\\InventoryController::_fetchLogData<\/a>"],[0,7,"<a href=\"InventoryController.php.html#3119\">Sparefoot\\PitaService\\Controller\\InventoryController::_fetchInventoryData<\/a>"],[0,6,"<a href=\"InventoryController.php.html#3155\">Sparefoot\\PitaService\\Controller\\InventoryController::updatefacilityAction<\/a>"],[0,6,"<a href=\"InventoryController.php.html#3192\">Sparefoot\\PitaService\\Controller\\InventoryController::addfacilitycontactAction<\/a>"],[0,4,"<a href=\"InventoryController.php.html#3234\">Sparefoot\\PitaService\\Controller\\InventoryController::removefacilitycontactAction<\/a>"],[0,13,"<a href=\"InventoryController.php.html#3266\">Sparefoot\\PitaService\\Controller\\InventoryController::updateinventoryAction<\/a>"],[0,15,"<a href=\"InventoryController.php.html#3338\">Sparefoot\\PitaService\\Controller\\InventoryController::getaccessAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#3419\">Sparefoot\\PitaService\\Controller\\InventoryController::addaccessAction<\/a>"],[0,2,"<a href=\"InventoryController.php.html#3445\">Sparefoot\\PitaService\\Controller\\InventoryController::removeaccessAction<\/a>"],[0,6,"<a href=\"InventoryController.php.html#3469\">Sparefoot\\PitaService\\Controller\\InventoryController::movefacilityAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3508\">Sparefoot\\PitaService\\Controller\\InventoryController::getsitelinkfacilitylistAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3539\">Sparefoot\\PitaService\\Controller\\InventoryController::getselfstoragemanagerfacilitylistAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3570\">Sparefoot\\PitaService\\Controller\\InventoryController::getcentershiftfacilitylistAction<\/a>"],[0,6,"<a href=\"InventoryController.php.html#3602\">Sparefoot\\PitaService\\Controller\\InventoryController::syncselectedfacsAction<\/a>"],[0,39,"<a href=\"InventoryController.php.html#3665\">Sparefoot\\PitaService\\Controller\\InventoryController::copyFacilityDataAction<\/a>"],[0,1,"<a href=\"InventoryController.php.html#3895\">Sparefoot\\PitaService\\Controller\\InventoryController::loadDuplicateReviews<\/a>"],[0,2,"<a href=\"InventoryController.php.html#3907\">Sparefoot\\PitaService\\Controller\\InventoryController::copyReviewToFacility<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3947\">Sparefoot\\PitaService\\Controller\\InventoryController::integrationsByAccountAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#3977\">Sparefoot\\PitaService\\Controller\\InventoryController::facilitiesByIntegrationAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#4006\">Sparefoot\\PitaService\\Controller\\InventoryController::getfacilityAction<\/a>"],[0,3,"<a href=\"InventoryController.php.html#4036\">Sparefoot\\PitaService\\Controller\\InventoryController::essfacilitylookupAction<\/a>"],[0,6,"<a href=\"InventoryController.php.html#4057\">Sparefoot\\PitaService\\Controller\\InventoryController::essConvertFromManualAction<\/a>"],[0,5,"<a href=\"InventoryController.php.html#4106\">Sparefoot\\PitaService\\Controller\\InventoryController::essConvertToManualAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#25\">Sparefoot\\PitaService\\Controller\\JobsController::initBeforeControllerAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#39\">Sparefoot\\PitaService\\Controller\\JobsController::summaryAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#113\">Sparefoot\\PitaService\\Controller\\JobsController::commandAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#131\">Sparefoot\\PitaService\\Controller\\JobsController::commandAddAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#153\">Sparefoot\\PitaService\\Controller\\JobsController::commandEditAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#175\">Sparefoot\\PitaService\\Controller\\JobsController::commandLogAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#204\">Sparefoot\\PitaService\\Controller\\JobsController::commandSaveAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#229\">Sparefoot\\PitaService\\Controller\\JobsController::commandDeleteAction<\/a>"],[0,5,"<a href=\"JobsController.php.html#252\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#317\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledEditAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#344\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledAddAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#373\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledDeleteAction<\/a>"],[0,4,"<a href=\"JobsController.php.html#390\">Sparefoot\\PitaService\\Controller\\JobsController::scheduledSaveAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#418\">Sparefoot\\PitaService\\Controller\\JobsController::adhocChooseAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#439\">Sparefoot\\PitaService\\Controller\\JobsController::adhocLogAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#466\">Sparefoot\\PitaService\\Controller\\JobsController::adhocAddAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#492\">Sparefoot\\PitaService\\Controller\\JobsController::adhocDeleteAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#509\">Sparefoot\\PitaService\\Controller\\JobsController::unlockAction<\/a>"],[0,3,"<a href=\"JobsController.php.html#526\">Sparefoot\\PitaService\\Controller\\JobsController::workerZombieAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#576\">Sparefoot\\PitaService\\Controller\\JobsController::workerLogAction<\/a>"],[0,5,"<a href=\"JobsController.php.html#600\">Sparefoot\\PitaService\\Controller\\JobsController::workerInfoAction<\/a>"],[0,2,"<a href=\"JobsController.php.html#637\">Sparefoot\\PitaService\\Controller\\JobsController::childAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#663\">Sparefoot\\PitaService\\Controller\\JobsController::childAddAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#683\">Sparefoot\\PitaService\\Controller\\JobsController::childEditAction<\/a>"],[0,4,"<a href=\"JobsController.php.html#704\">Sparefoot\\PitaService\\Controller\\JobsController::childUpdateAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#731\">Sparefoot\\PitaService\\Controller\\JobsController::childSaveAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#754\">Sparefoot\\PitaService\\Controller\\JobsController::childDeleteAction<\/a>"],[0,1,"<a href=\"JobsController.php.html#767\">Sparefoot\\PitaService\\Controller\\JobsController::colDisplay<\/a>"],[0,4,"<a href=\"JobsController.php.html#772\">Sparefoot\\PitaService\\Controller\\JobsController::finishCode<\/a>"],[0,4,"<a href=\"JobsController.php.html#786\">Sparefoot\\PitaService\\Controller\\JobsController::successCode<\/a>"],[0,8,"<a href=\"JobsController.php.html#801\">Sparefoot\\PitaService\\Controller\\JobsController::timeDisplay<\/a>"],[0,2,"<a href=\"JobsController.php.html#832\">Sparefoot\\PitaService\\Controller\\JobsController::dateDisplay<\/a>"],[0,4,"<a href=\"JobsController.php.html#839\">Sparefoot\\PitaService\\Controller\\JobsController::_verifyChildAllowed<\/a>"],[0,7,"<a href=\"LoginController.php.html#16\">Sparefoot\\PitaService\\Controller\\LoginController::index<\/a>"],[0,1,"<a href=\"LoginController.php.html#58\">Sparefoot\\PitaService\\Controller\\LoginController::check<\/a>"],[0,2,"<a href=\"LoginController.php.html#67\">Sparefoot\\PitaService\\Controller\\LoginController::getCurrentUser<\/a>"],[0,1,"<a href=\"LoginController.php.html#85\">Sparefoot\\PitaService\\Controller\\LoginController::logout<\/a>"],[0,1,"<a href=\"LoginController.php.html#92\">Sparefoot\\PitaService\\Controller\\LoginController::amILoggedIn<\/a>"],[0,1,"<a href=\"LoginController.php.html#98\">Sparefoot\\PitaService\\Controller\\LoginController::isUserGod<\/a>"],[0,1,"<a href=\"MaintenanceModeController.php.html#15\">Sparefoot\\PitaService\\Controller\\MaintenanceModeController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"MaintenanceModeController.php.html#25\">Sparefoot\\PitaService\\Controller\\MaintenanceModeController::indexAction<\/a>"],[0,1,"<a href=\"MaintenanceModeController.php.html#71\">Sparefoot\\PitaService\\Controller\\MaintenanceModeController::setFlagAction<\/a>"],[0,4,"<a href=\"MaintenanceModeController.php.html#104\">Sparefoot\\PitaService\\Controller\\MaintenanceModeController::_sendMaintenanceModeEmail<\/a>"],[0,6,"<a href=\"NetsuiteController.php.html#18\">Sparefoot\\PitaService\\Controller\\NetsuiteController::accountAction<\/a>"],[0,4,"<a href=\"NetsuiteController.php.html#72\">Sparefoot\\PitaService\\Controller\\NetsuiteController::statusAction<\/a>"],[0,1,"<a href=\"OmnomController.php.html#15\">Sparefoot\\PitaService\\Controller\\OmnomController::initBeforeControllerAction<\/a>"],[0,4,"<a href=\"OmnomController.php.html#26\">Sparefoot\\PitaService\\Controller\\OmnomController::indexAction<\/a>"],[0,3,"<a href=\"OmnomController.php.html#60\">Sparefoot\\PitaService\\Controller\\OmnomController::_queryStatusByJob<\/a>"],[0,5,"<a href=\"OmnomController.php.html#107\">Sparefoot\\PitaService\\Controller\\OmnomController::_queryStatusByIntegration<\/a>"],[0,5,"<a href=\"OmnomController.php.html#166\">Sparefoot\\PitaService\\Controller\\OmnomController::_queryStatusByFacility<\/a>"],[0,2,"<a href=\"OmnomController.php.html#219\">Sparefoot\\PitaService\\Controller\\OmnomController::_getJobs<\/a>"],[0,2,"<a href=\"OmnomController.php.html#239\">Sparefoot\\PitaService\\Controller\\OmnomController::_getIntegrations<\/a>"],[0,2,"<a href=\"OmnomController.php.html#259\">Sparefoot\\PitaService\\Controller\\OmnomController::_getAccounts<\/a>"],[0,2,"<a href=\"PaidmediaController.php.html#16\">Sparefoot\\PitaService\\Controller\\PaidmediaController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"PaidmediaController.php.html#36\">Sparefoot\\PitaService\\Controller\\PaidmediaController::indexAction<\/a>"],[0,1,"<a href=\"PaidmediaController.php.html#46\">Sparefoot\\PitaService\\Controller\\PaidmediaController::paidmediaAction<\/a>"],[0,1,"<a href=\"PaidmediaController.php.html#62\">Sparefoot\\PitaService\\Controller\\PaidmediaController::managepaidmediaAction<\/a>"],[0,5,"<a href=\"PaidmediaController.php.html#74\">Sparefoot\\PitaService\\Controller\\PaidmediaController::modifypaidmediaAction<\/a>"],[0,1,"<a href=\"PaidmediaController.php.html#107\">Sparefoot\\PitaService\\Controller\\PaidmediaController::ajaxcreatepaidmediaupdateAction<\/a>"],[0,5,"<a href=\"PaidmediaController.php.html#122\">Sparefoot\\PitaService\\Controller\\PaidmediaController::getRuleengineList<\/a>"],[0,3,"<a href=\"PaidmediaController.php.html#147\">Sparefoot\\PitaService\\Controller\\PaidmediaController::getCampaignList<\/a>"],[0,2,"<a href=\"PayoutController.php.html#18\">Sparefoot\\PitaService\\Controller\\PayoutController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"PayoutController.php.html#38\">Sparefoot\\PitaService\\Controller\\PayoutController::indexAction<\/a>"],[0,1,"<a href=\"PayoutController.php.html#48\">Sparefoot\\PitaService\\Controller\\PayoutController::listAction<\/a>"],[0,2,"<a href=\"PayoutController.php.html#61\">Sparefoot\\PitaService\\Controller\\PayoutController::previewAction<\/a>"],[0,1,"<a href=\"PayoutController.php.html#81\">Sparefoot\\PitaService\\Controller\\PayoutController::detailsAction<\/a>"],[0,1,"<a href=\"PayoutController.php.html#97\">Sparefoot\\PitaService\\Controller\\PayoutController::createAction<\/a>"],[0,1,"<a href=\"PayoutController.php.html#119\">Sparefoot\\PitaService\\Controller\\PayoutController::pdfAction<\/a>"],[0,2,"<a href=\"PayoutController.php.html#137\">Sparefoot\\PitaService\\Controller\\PayoutController::getnotesAction<\/a>"],[0,3,"<a href=\"PayoutController.php.html#163\">Sparefoot\\PitaService\\Controller\\PayoutController::changenotesAction<\/a>"],[0,2,"<a href=\"PayoutController.php.html#191\">Sparefoot\\PitaService\\Controller\\PayoutController::_assignBookings<\/a>"],[0,1,"<a href=\"PayoutController.php.html#209\">Sparefoot\\PitaService\\Controller\\PayoutController::_preparePayoutQuery<\/a>"],[0,1,"<a href=\"PayoutController.php.html#221\">Sparefoot\\PitaService\\Controller\\PayoutController::_prepareDetails<\/a>"],[0,1,"<a href=\"PhotosController.php.html#16\">Sparefoot\\PitaService\\Controller\\PhotosController::initBeforeControllerAction<\/a>"],[0,6,"<a href=\"PhotosController.php.html#27\">Sparefoot\\PitaService\\Controller\\PhotosController::indexAction<\/a>"],[0,3,"<a href=\"PhotosController.php.html#72\">Sparefoot\\PitaService\\Controller\\PhotosController::approveAction<\/a>"],[100,1,"<a href=\"PingController.php.html#15\">Sparefoot\\PitaService\\Controller\\PingController::__construct<\/a>"],[100,4,"<a href=\"PingController.php.html#25\">Sparefoot\\PitaService\\Controller\\PingController::indexAction<\/a>"],[0,1,"<a href=\"PingController.php.html#51\">Sparefoot\\PitaService\\Controller\\PingController::indexAction2<\/a>"],[0,1,"<a href=\"PitaSearchController.php.html#19\">Sparefoot\\PitaService\\Controller\\PitaSearchController::initBeforeControllerAction<\/a>"],[0,8,"<a href=\"PitaSearchController.php.html#30\">Sparefoot\\PitaService\\Controller\\PitaSearchController::indexAction<\/a>"],[0,8,"<a href=\"PitaSearchController.php.html#82\">Sparefoot\\PitaService\\Controller\\PitaSearchController::queryAction<\/a>"],[0,4,"<a href=\"ProxyController.php.html#26\">Sparefoot\\PitaService\\Controller\\ProxyController::initBeforeControllerAction<\/a>"],[0,5,"<a href=\"ProxyController.php.html#47\">Sparefoot\\PitaService\\Controller\\ProxyController::loadAction<\/a>"],[0,5,"<a href=\"ProxyController.php.html#89\">Sparefoot\\PitaService\\Controller\\ProxyController::AuthenticateProxyAuthorization<\/a>"],[0,7,"<a href=\"ProxyController.php.html#110\">Sparefoot\\PitaService\\Controller\\ProxyController::proxyRequest<\/a>"],[0,1,"<a href=\"ProxyController.php.html#153\">Sparefoot\\PitaService\\Controller\\ProxyController::infoAction<\/a>"],[0,2,"<a href=\"ProxyController.php.html#167\">Sparefoot\\PitaService\\Controller\\ProxyController::debugAction<\/a>"],[0,3,"<a href=\"PublicController.php.html#23\">Sparefoot\\PitaService\\Controller\\PublicController::searchServicesAction<\/a>"],[0,5,"<a href=\"PublicController.php.html#58\">Sparefoot\\PitaService\\Controller\\PublicController::bookUnitAction<\/a>"],[0,2,"<a href=\"PublicController.php.html#122\">Sparefoot\\PitaService\\Controller\\PublicController::searchAction<\/a>"],[0,8,"<a href=\"PublicController.php.html#147\">Sparefoot\\PitaService\\Controller\\PublicController::hourlyAlertsAction<\/a>"],[0,1,"<a href=\"QuickclientController.php.html#17\">Sparefoot\\PitaService\\Controller\\QuickclientController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"QuickclientController.php.html#28\">Sparefoot\\PitaService\\Controller\\QuickclientController::indexAction<\/a>"],[0,3,"<a href=\"QuickclientController.php.html#38\">Sparefoot\\PitaService\\Controller\\QuickclientController::listAction<\/a>"],[0,3,"<a href=\"QuickclientController.php.html#76\">Sparefoot\\PitaService\\Controller\\QuickclientController::renderAction<\/a>"],[0,1,"<a href=\"QuickclientController.php.html#110\">Sparefoot\\PitaService\\Controller\\QuickclientController::paramsAction<\/a>"],[0,1,"<a href=\"QuickclientController.php.html#126\">Sparefoot\\PitaService\\Controller\\QuickclientController::facilityAction<\/a>"],[0,4,"<a href=\"QuickclientController.php.html#143\">Sparefoot\\PitaService\\Controller\\QuickclientController::facilitiesAction<\/a>"],[0,1,"<a href=\"QuickclientController.php.html#171\">Sparefoot\\PitaService\\Controller\\QuickclientController::_facSort<\/a>"],[0,3,"<a href=\"QuickclientController.php.html#180\">Sparefoot\\PitaService\\Controller\\QuickclientController::unitsAction<\/a>"],[0,1,"<a href=\"QuickclientController.php.html#208\">Sparefoot\\PitaService\\Controller\\QuickclientController::unitAction<\/a>"],[0,11,"<a href=\"QuickclientController.php.html#224\">Sparefoot\\PitaService\\Controller\\QuickclientController::executeAction<\/a>"],[0,5,"<a href=\"QuickclientController.php.html#284\">Sparefoot\\PitaService\\Controller\\QuickclientController::toArray<\/a>"],[0,1,"<a href=\"QuickformController.php.html#15\">Sparefoot\\PitaService\\Controller\\QuickformController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"QuickformController.php.html#26\">Sparefoot\\PitaService\\Controller\\QuickformController::index<\/a>"],[0,1,"<a href=\"QuickformController.php.html#36\">Sparefoot\\PitaService\\Controller\\QuickformController::listAction<\/a>"],[0,1,"<a href=\"QuickformController.php.html#48\">Sparefoot\\PitaService\\Controller\\QuickformController::renderAction<\/a>"],[0,1,"<a href=\"QuickjobController.php.html#16\">Sparefoot\\PitaService\\Controller\\QuickjobController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"QuickjobController.php.html#27\">Sparefoot\\PitaService\\Controller\\QuickjobController::index<\/a>"],[0,1,"<a href=\"QuickjobController.php.html#37\">Sparefoot\\PitaService\\Controller\\QuickjobController::renderAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#23\">Sparefoot\\PitaService\\Controller\\QuickrepController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#35\">Sparefoot\\PitaService\\Controller\\QuickrepController::indexAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#45\">Sparefoot\\PitaService\\Controller\\QuickrepController::listAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#55\">Sparefoot\\PitaService\\Controller\\QuickrepController::renderdashAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#65\">Sparefoot\\PitaService\\Controller\\QuickrepController::renderAction<\/a>"],[0,2,"<a href=\"QuickrepController.php.html#75\">Sparefoot\\PitaService\\Controller\\QuickrepController::updateinputsAction<\/a>"],[0,3,"<a href=\"QuickrepController.php.html#97\">Sparefoot\\PitaService\\Controller\\QuickrepController::tableAction<\/a>"],[0,4,"<a href=\"QuickrepController.php.html#124\">Sparefoot\\PitaService\\Controller\\QuickrepController::chartAction<\/a>"],[0,2,"<a href=\"QuickrepController.php.html#154\">Sparefoot\\PitaService\\Controller\\QuickrepController::sqlAction<\/a>"],[0,1,"<a href=\"QuickrepController.php.html#174\">Sparefoot\\PitaService\\Controller\\QuickrepController::exportAction<\/a>"],[0,7,"<a href=\"QuickrepController.php.html#195\">Sparefoot\\PitaService\\Controller\\QuickrepController::hourlyAlertsAction<\/a>"],[0,2,"<a href=\"QuicksoapController.php.html#22\">Sparefoot\\PitaService\\Controller\\QuicksoapController::indexAction<\/a>"],[0,2,"<a href=\"QuicksoapController.php.html#35\">Sparefoot\\PitaService\\Controller\\QuicksoapController::wsdlAction<\/a>"],[0,1,"<a href=\"QuicksoapController.php.html#50\">Sparefoot\\PitaService\\Controller\\QuicksoapController::handleSoapRequest<\/a>"],[0,2,"<a href=\"QuicksoapController.php.html#77\">Sparefoot\\PitaService\\Controller\\QuicksoapController::generateWsdlResponse<\/a>"],[0,2,"<a href=\"QuicktaggerController.php.html#19\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"QuicktaggerController.php.html#72\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::indexAction<\/a>"],[0,1,"<a href=\"QuicktaggerController.php.html#85\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::integrationAction<\/a>"],[0,2,"<a href=\"QuicktaggerController.php.html#104\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::saveorderAction<\/a>"],[0,3,"<a href=\"QuicktaggerController.php.html#125\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::savenewAction<\/a>"],[0,7,"<a href=\"QuicktaggerController.php.html#158\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::saveexistingAction<\/a>"],[0,2,"<a href=\"QuicktaggerController.php.html#204\">Sparefoot\\PitaService\\Controller\\QuicktaggerController::removeAction<\/a>"],[0,1,"<a href=\"ReferralsController.php.html#15\">Sparefoot\\PitaService\\Controller\\ReferralsController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"ReferralsController.php.html#27\">Sparefoot\\PitaService\\Controller\\ReferralsController::indexAction<\/a>"],[0,2,"<a href=\"ReferralsController.php.html#40\">Sparefoot\\PitaService\\Controller\\ReferralsController::toggleconvertedAction<\/a>"],[0,2,"<a href=\"ReferralsController.php.html#59\">Sparefoot\\PitaService\\Controller\\ReferralsController::toggleclaimedAction<\/a>"],[0,1,"<a href=\"ReferralsController.php.html#78\">Sparefoot\\PitaService\\Controller\\ReferralsController::savenotesAction<\/a>"],[0,2,"<a href=\"ReportsController.php.html#22\">Sparefoot\\PitaService\\Controller\\ReportsController::initBeforeControllerAction<\/a>"],[0,2,"<a href=\"ReportsController.php.html#43\">Sparefoot\\PitaService\\Controller\\ReportsController::indexAction<\/a>"],[0,10,"<a href=\"ReportsController.php.html#66\">Sparefoot\\PitaService\\Controller\\ReportsController::renderAction<\/a>"],[0,1,"<a href=\"ReportsController.php.html#128\">Sparefoot\\PitaService\\Controller\\ReportsController::updateInputsAction<\/a>"],[0,3,"<a href=\"ReportsController.php.html#152\">Sparefoot\\PitaService\\Controller\\ReportsController::tableAction<\/a>"],[0,2,"<a href=\"ReportsController.php.html#179\">Sparefoot\\PitaService\\Controller\\ReportsController::sqlAction<\/a>"],[0,1,"<a href=\"ReportsController.php.html#198\">Sparefoot\\PitaService\\Controller\\ReportsController::exportAction<\/a>"],[0,2,"<a href=\"ReviewsController.php.html#16\">Sparefoot\\PitaService\\Controller\\ReviewsController::initBeforeControllerAction<\/a>"],[0,6,"<a href=\"ReviewsController.php.html#35\">Sparefoot\\PitaService\\Controller\\ReviewsController::indexAction<\/a>"],[0,1,"<a href=\"ReviewsController.php.html#84\">Sparefoot\\PitaService\\Controller\\ReviewsController::getQueryAction<\/a>"],[0,4,"<a href=\"ReviewsController.php.html#102\">Sparefoot\\PitaService\\Controller\\ReviewsController::deleteReviewAction<\/a>"],[0,2,"<a href=\"ReviewsController.php.html#126\">Sparefoot\\PitaService\\Controller\\ReviewsController::getReviewAction<\/a>"],[0,5,"<a href=\"ReviewsController.php.html#156\">Sparefoot\\PitaService\\Controller\\ReviewsController::saveReviewAction<\/a>"],[0,1,"<a href=\"ReviewsController.php.html#200\">Sparefoot\\PitaService\\Controller\\ReviewsController::approvalsAction<\/a>"],[0,3,"<a href=\"ReviewsController.php.html#213\">Sparefoot\\PitaService\\Controller\\ReviewsController::setStatusAction<\/a>"],[0,2,"<a href=\"ReviewsController.php.html#244\">Sparefoot\\PitaService\\Controller\\ReviewsController::editReviewMessageAction<\/a>"],[0,1,"<a href=\"RewardsController.php.html#15\">Sparefoot\\PitaService\\Controller\\RewardsController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"RewardsController.php.html#25\">Sparefoot\\PitaService\\Controller\\RewardsController::indexAction<\/a>"],[0,5,"<a href=\"RewardsController.php.html#37\">Sparefoot\\PitaService\\Controller\\RewardsController::newkiindAction<\/a>"],[0,1,"<a href=\"RewardsController.php.html#84\">Sparefoot\\PitaService\\Controller\\RewardsController::reviewkiindAction<\/a>"],[0,2,"<a href=\"RewardsController.php.html#96\">Sparefoot\\PitaService\\Controller\\RewardsController::browsekiindAction<\/a>"],[0,1,"<a href=\"RewardsController.php.html#114\">Sparefoot\\PitaService\\Controller\\RewardsController::listAction<\/a>"],[0,1,"<a href=\"SalesController.php.html#17\">Sparefoot\\PitaService\\Controller\\SalesController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"SalesController.php.html#27\">Sparefoot\\PitaService\\Controller\\SalesController::indexAction<\/a>"],[0,2,"<a href=\"SalesController.php.html#37\">Sparefoot\\PitaService\\Controller\\SalesController::searchAction<\/a>"],[0,1,"<a href=\"SalesController.php.html#54\">Sparefoot\\PitaService\\Controller\\SalesController::_queryData<\/a>"],[0,1,"<a href=\"SalesController.php.html#67\">Sparefoot\\PitaService\\Controller\\SalesController::_getSearchDataByZip<\/a>"],[0,1,"<a href=\"SalesController.php.html#130\">Sparefoot\\PitaService\\Controller\\SalesController::_getSearchData<\/a>"],[0,1,"<a href=\"SalesController.php.html#148\">Sparefoot\\PitaService\\Controller\\SalesController::_getClickData<\/a>"],[0,1,"<a href=\"SalesController.php.html#170\">Sparefoot\\PitaService\\Controller\\SalesController::_getReservationData<\/a>"],[0,31,"<a href=\"SalesforceController.php.html#19\">Sparefoot\\PitaService\\Controller\\SalesforceController::addAccountAction<\/a>"],[0,8,"<a href=\"SalesforceController.php.html#197\">Sparefoot\\PitaService\\Controller\\SalesforceController::_createManualFacilities<\/a>"],[0,1,"<a href=\"SearchController.php.html#15\">Sparefoot\\PitaService\\Controller\\SearchController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"SearchController.php.html#27\">Sparefoot\\PitaService\\Controller\\SearchController::indexAction<\/a>"],[0,13,"<a href=\"SearchController.php.html#37\">Sparefoot\\PitaService\\Controller\\SearchController::testAction<\/a>"],[0,6,"<a href=\"SearchController.php.html#144\">Sparefoot\\PitaService\\Controller\\SearchController::distanceAction<\/a>"],[0,1,"<a href=\"SearchController.php.html#205\">Sparefoot\\PitaService\\Controller\\SearchController::_initWeights<\/a>"],[0,2,"<a href=\"SearchController.php.html#225\">Sparefoot\\PitaService\\Controller\\SearchController::_conductSearch<\/a>"],[0,1,"<a href=\"ServiceareaController.php.html#16\">Sparefoot\\PitaService\\Controller\\ServiceareaController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"ServiceareaController.php.html#27\">Sparefoot\\PitaService\\Controller\\ServiceareaController::indexAction<\/a>"],[0,3,"<a href=\"ServiceareaController.php.html#55\">Sparefoot\\PitaService\\Controller\\ServiceareaController::getAction<\/a>"],[0,2,"<a href=\"ServiceareaController.php.html#87\">Sparefoot\\PitaService\\Controller\\ServiceareaController::saveAction<\/a>"],[0,2,"<a href=\"ServiceareaController.php.html#115\">Sparefoot\\PitaService\\Controller\\ServiceareaController::convexhullAction<\/a>"],[0,2,"<a href=\"ServiceareaController.php.html#142\">Sparefoot\\PitaService\\Controller\\ServiceareaController::simplifyAction<\/a>"],[0,2,"<a href=\"ServiceareaController.php.html#167\">Sparefoot\\PitaService\\Controller\\ServiceareaController::getServiceAreaWktByFacilityId<\/a>"],[0,1,"<a href=\"ServiceareaController.php.html#177\">Sparefoot\\PitaService\\Controller\\ServiceareaController::postLocationService<\/a>"],[0,1,"<a href=\"SoftwarepartnerController.php.html#15\">Sparefoot\\PitaService\\Controller\\SoftwarepartnerController::initBeforeControllerAction<\/a>"],[0,6,"<a href=\"SoftwarepartnerController.php.html#25\">Sparefoot\\PitaService\\Controller\\SoftwarepartnerController::indexAction<\/a>"],[0,1,"<a href=\"SphinxsearchController.php.html#15\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"SphinxsearchController.php.html#29\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::indexAction<\/a>"],[0,2,"<a href=\"SphinxsearchController.php.html#39\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::testAction<\/a>"],[0,6,"<a href=\"SphinxsearchController.php.html#66\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::distanceAction<\/a>"],[0,1,"<a href=\"SphinxsearchController.php.html#124\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::_initWeights<\/a>"],[0,3,"<a href=\"SphinxsearchController.php.html#144\">Sparefoot\\PitaService\\Controller\\SphinxsearchController::_conductSearch<\/a>"],[0,1,"<a href=\"StatementsController.php.html#20\">Sparefoot\\PitaService\\Controller\\StatementsController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"StatementsController.php.html#34\">Sparefoot\\PitaService\\Controller\\StatementsController::indexAction<\/a>"],[0,2,"<a href=\"StatementsController.php.html#51\">Sparefoot\\PitaService\\Controller\\StatementsController::createAction<\/a>"],[0,4,"<a href=\"StatementsController.php.html#84\">Sparefoot\\PitaService\\Controller\\StatementsController::generateAction<\/a>"],[0,3,"<a href=\"StatementsController.php.html#123\">Sparefoot\\PitaService\\Controller\\StatementsController::_export<\/a>"],[0,16,"<a href=\"StatementsController.php.html#153\">Sparefoot\\PitaService\\Controller\\StatementsController::_exportInvoice<\/a>"],[0,1,"<a href=\"StatementsController.php.html#245\">Sparefoot\\PitaService\\Controller\\StatementsController::_processReport<\/a>"],[0,3,"<a href=\"StatementsController.php.html#265\">Sparefoot\\PitaService\\Controller\\StatementsController::cancelInvoiceAction<\/a>"],[0,6,"<a href=\"StatementsController.php.html#289\">Sparefoot\\PitaService\\Controller\\StatementsController::invoiceAction<\/a>"],[0,3,"<a href=\"StatementsController.php.html#357\">Sparefoot\\PitaService\\Controller\\StatementsController::pdfAction<\/a>"],[0,6,"<a href=\"StatementsController.php.html#382\">Sparefoot\\PitaService\\Controller\\StatementsController::emailpreviewAction<\/a>"],[0,6,"<a href=\"StatementsController.php.html#430\">Sparefoot\\PitaService\\Controller\\StatementsController::getInvoiceAction<\/a>"],[0,2,"<a href=\"TableauController.php.html#21\">Sparefoot\\PitaService\\Controller\\TableauController::setErrorMessage<\/a>"],[0,2,"<a href=\"TableauController.php.html#29\">Sparefoot\\PitaService\\Controller\\TableauController::setSuccessMessage<\/a>"],[0,3,"<a href=\"TableauController.php.html#37\">Sparefoot\\PitaService\\Controller\\TableauController::initView<\/a>"],[0,1,"<a href=\"TableauController.php.html#59\">Sparefoot\\PitaService\\Controller\\TableauController::initBeforeControllerAction<\/a>"],[0,4,"<a href=\"TableauController.php.html#74\">Sparefoot\\PitaService\\Controller\\TableauController::indexAction<\/a>"],[0,1,"<a href=\"TableauController.php.html#105\">Sparefoot\\PitaService\\Controller\\TableauController::addAction<\/a>"],[0,3,"<a href=\"TableauController.php.html#118\">Sparefoot\\PitaService\\Controller\\TableauController::editAction<\/a>"],[0,7,"<a href=\"TableauController.php.html#140\">Sparefoot\\PitaService\\Controller\\TableauController::saveAction<\/a>"],[0,3,"<a href=\"TableauController.php.html#175\">Sparefoot\\PitaService\\Controller\\TableauController::deleteAction<\/a>"],[0,1,"<a href=\"TableauController.php.html#198\">Sparefoot\\PitaService\\Controller\\TableauController::manageKeysAction<\/a>"],[0,3,"<a href=\"TableauController.php.html#211\">Sparefoot\\PitaService\\Controller\\TableauController::editKeyAction<\/a>"],[0,5,"<a href=\"TableauController.php.html#233\">Sparefoot\\PitaService\\Controller\\TableauController::saveKeyAction<\/a>"],[0,4,"<a href=\"TableauController.php.html#270\">Sparefoot\\PitaService\\Controller\\TableauController::deleteKeyAction<\/a>"],[0,3,"<a href=\"TableauController.php.html#297\">Sparefoot\\PitaService\\Controller\\TableauController::massEditKeyAction<\/a>"],[0,11,"<a href=\"TableauController.php.html#322\">Sparefoot\\PitaService\\Controller\\TableauController::saveMassEditAction<\/a>"],[0,1,"<a href=\"TableauController.php.html#371\">Sparefoot\\PitaService\\Controller\\TableauController::manageTeamsAction<\/a>"],[0,2,"<a href=\"TableauController.php.html#384\">Sparefoot\\PitaService\\Controller\\TableauController::addTeamAction<\/a>"],[0,1,"<a href=\"TestController.php.html#16\">Sparefoot\\PitaService\\Controller\\TestController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"TestController.php.html#28\">Sparefoot\\PitaService\\Controller\\TestController::indexAction<\/a>"],[0,2,"<a href=\"TestController.php.html#38\">Sparefoot\\PitaService\\Controller\\TestController::testsAction<\/a>"],[0,3,"<a href=\"TestController.php.html#63\">Sparefoot\\PitaService\\Controller\\TestController::manageAction<\/a>"],[0,3,"<a href=\"TestController.php.html#91\">Sparefoot\\PitaService\\Controller\\TestController::manageuiAction<\/a>"],[0,1,"<a href=\"TestController.php.html#119\">Sparefoot\\PitaService\\Controller\\TestController::stopAction<\/a>"],[0,1,"<a href=\"TestController.php.html#131\">Sparefoot\\PitaService\\Controller\\TestController::stopuiAction<\/a>"],[0,6,"<a href=\"TestController.php.html#145\">Sparefoot\\PitaService\\Controller\\TestController::modifytestAction<\/a>"],[0,2,"<a href=\"TestController.php.html#180\">Sparefoot\\PitaService\\Controller\\TestController::ajaxcreatesearchAction<\/a>"],[0,2,"<a href=\"TestController.php.html#207\">Sparefoot\\PitaService\\Controller\\TestController::ajaxcreateuiAction<\/a>"],[0,1,"<a href=\"TestController.php.html#231\">Sparefoot\\PitaService\\Controller\\TestController::_saveWeight<\/a>"],[0,4,"<a href=\"TestController.php.html#245\">Sparefoot\\PitaService\\Controller\\TestController::_saveTestVariation<\/a>"],[0,3,"<a href=\"ToolsController.php.html#19\">Sparefoot\\PitaService\\Controller\\ToolsController::initBeforeControllerAction<\/a>"],[0,2,"<a href=\"ToolsController.php.html#85\">Sparefoot\\PitaService\\Controller\\ToolsController::indexAction<\/a>"],[0,1,"<a href=\"ToolsController.php.html#107\">Sparefoot\\PitaService\\Controller\\ToolsController::urlBuilderAction<\/a>"],[0,2,"<a href=\"ToolsController.php.html#119\">Sparefoot\\PitaService\\Controller\\ToolsController::perfectPhotosSelectorAction<\/a>"],[0,2,"<a href=\"ToolsController.php.html#140\">Sparefoot\\PitaService\\Controller\\ToolsController::perfectPhotosSelectorPostAction<\/a>"],[0,15,"<a href=\"ToolsController.php.html#173\">Sparefoot\\PitaService\\Controller\\ToolsController::noEmailListAction<\/a>"],[0,16,"<a href=\"ToolsController.php.html#265\">Sparefoot\\PitaService\\Controller\\ToolsController::searchKeyChangerAction<\/a>"],[0,11,"<a href=\"ToolsController.php.html#390\">Sparefoot\\PitaService\\Controller\\ToolsController::twilioReportsAction<\/a>"],[0,11,"<a href=\"ToolsController.php.html#476\">Sparefoot\\PitaService\\Controller\\ToolsController::createTwilioNumberAction<\/a>"],[0,5,"<a href=\"ToolsController.php.html#545\">Sparefoot\\PitaService\\Controller\\ToolsController::purchaseTwilioNumbersAction<\/a>"],[0,9,"<a href=\"ToolsController.php.html#614\">Sparefoot\\PitaService\\Controller\\ToolsController::siteContentEditorAction<\/a>"],[0,12,"<a href=\"ToolsController.php.html#706\">Sparefoot\\PitaService\\Controller\\ToolsController::siteContentCreatorAction<\/a>"],[0,7,"<a href=\"ToolsController.php.html#837\">Sparefoot\\PitaService\\Controller\\ToolsController::siteContentImportAction<\/a>"],[0,13,"<a href=\"ToolsController.php.html#924\">Sparefoot\\PitaService\\Controller\\ToolsController::siteContentCreatorImportAction<\/a>"],[0,13,"<a href=\"ToolsController.php.html#1055\">Sparefoot\\PitaService\\Controller\\ToolsController::accountingMapAction<\/a>"],[0,3,"<a href=\"ToolsController.php.html#1160\">Sparefoot\\PitaService\\Controller\\ToolsController::cabinetAction<\/a>"],[0,11,"<a href=\"ToolsController.php.html#1188\">Sparefoot\\PitaService\\Controller\\ToolsController::featureFlagsAction<\/a>"],[0,10,"<a href=\"ToolsController.php.html#1241\">Sparefoot\\PitaService\\Controller\\ToolsController::handleFeatureFlagSave<\/a>"],[0,2,"<a href=\"ToolsController.php.html#1287\">Sparefoot\\PitaService\\Controller\\ToolsController::addFeatureFlagAction<\/a>"],[0,2,"<a href=\"ToolsController.php.html#1305\">Sparefoot\\PitaService\\Controller\\ToolsController::deleteFeatureFlagAction<\/a>"],[0,3,"<a href=\"ToolsController.php.html#1323\">Sparefoot\\PitaService\\Controller\\ToolsController::pressPageAction<\/a>"],[0,3,"<a href=\"ToolsController.php.html#1350\">Sparefoot\\PitaService\\Controller\\ToolsController::pressPageEditorAction<\/a>"],[0,1,"<a href=\"ToolsController.php.html#1379\">Sparefoot\\PitaService\\Controller\\ToolsController::platformSearchAction<\/a>"],[0,16,"<a href=\"ToolsController.php.html#1389\">Sparefoot\\PitaService\\Controller\\ToolsController::platformQueryAction<\/a>"],[0,1,"<a href=\"TriController.php.html#17\">Sparefoot\\PitaService\\Controller\\TriController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"TriController.php.html#27\">Sparefoot\\PitaService\\Controller\\TriController::index<\/a>"],[0,1,"<a href=\"TriController.php.html#37\">Sparefoot\\PitaService\\Controller\\TriController::fortuneAction<\/a>"],[0,1,"<a href=\"UserController.php.html#18\">Sparefoot\\PitaService\\Controller\\UserController::__construct<\/a>"],[0,16,"<a href=\"UserController.php.html#27\">Sparefoot\\PitaService\\Controller\\UserController::initBeforeControllerAction<\/a>"],[0,1,"<a href=\"UserController.php.html#65\">Sparefoot\\PitaService\\Controller\\UserController::indexAction<\/a>"],[0,8,"<a href=\"UserController.php.html#73\">Sparefoot\\PitaService\\Controller\\UserController::_listAction<\/a>"],[0,1,"<a href=\"UserController.php.html#120\">Sparefoot\\PitaService\\Controller\\UserController::myfootAction<\/a>"],[0,1,"<a href=\"UserController.php.html#128\">Sparefoot\\PitaService\\Controller\\UserController::pitaAction<\/a>"],[0,1,"<a href=\"UserController.php.html#136\">Sparefoot\\PitaService\\Controller\\UserController::portalAction<\/a>"],[0,9,"<a href=\"UserController.php.html#144\">Sparefoot\\PitaService\\Controller\\UserController::loadusersAction<\/a>"],[0,13,"<a href=\"UserController.php.html#202\">Sparefoot\\PitaService\\Controller\\UserController::editinfoAction<\/a>"],[0,5,"<a href=\"UserController.php.html#252\">Sparefoot\\PitaService\\Controller\\UserController::ajaxcreateAction<\/a>"],[0,4,"<a href=\"UserController.php.html#291\">Sparefoot\\PitaService\\Controller\\UserController::_createPitaUser<\/a>"],[0,3,"<a href=\"UserController.php.html#330\">Sparefoot\\PitaService\\Controller\\UserController::_createPortalUser<\/a>"],[0,14,"<a href=\"UserController.php.html#359\">Sparefoot\\PitaService\\Controller\\UserController::ajaxcreatemyfootAction<\/a>"],[0,2,"<a href=\"UserController.php.html#452\">Sparefoot\\PitaService\\Controller\\UserController::isEmailValid<\/a>"],[0,2,"<a href=\"UserController.php.html#463\">Sparefoot\\PitaService\\Controller\\UserController::_restrictFacilityAccess<\/a>"],[0,3,"<a href=\"UserController.php.html#473\">Sparefoot\\PitaService\\Controller\\UserController::_addFacilityContact<\/a>"],[0,4,"<a href=\"UserController.php.html#490\">Sparefoot\\PitaService\\Controller\\UserController::_buildUser<\/a>"],[0,1,"<a href=\"UserController.php.html#519\">Sparefoot\\PitaService\\Controller\\UserController::createAction<\/a>"],[0,5,"<a href=\"UserController.php.html#587\">Sparefoot\\PitaService\\Controller\\UserController::ajaxpasswordAction<\/a>"],[0,7,"<a href=\"UserController.php.html#618\">Sparefoot\\PitaService\\Controller\\UserController::passwordAction<\/a>"],[0,10,"<a href=\"UserController.php.html#652\">Sparefoot\\PitaService\\Controller\\UserController::changeattributesAction<\/a>"],[0,4,"<a href=\"UserController.php.html#722\">Sparefoot\\PitaService\\Controller\\UserController::addstmtrecipientsAction<\/a>"],[0,4,"<a href=\"UserController.php.html#756\">Sparefoot\\PitaService\\Controller\\UserController::removestmtrecipientsAction<\/a>"],[0,8,"<a href=\"UserController.php.html#790\">Sparefoot\\PitaService\\Controller\\UserController::_export<\/a>"],[0,10,"<a href=\"UserController.php.html#866\">Sparefoot\\PitaService\\Controller\\UserController::_emailRecipientExport<\/a>"],[0,1,"<a href=\"UserController.php.html#934\">Sparefoot\\PitaService\\Controller\\UserController::_getDefaultQuery<\/a>"],[0,8,"<a href=\"UserController.php.html#941\">Sparefoot\\PitaService\\Controller\\UserController::_processQuery<\/a>"],[0,1,"<a href=\"UserController.php.html#961\">Sparefoot\\PitaService\\Controller\\UserController::_processReport<\/a>"],[0,24,"<a href=\"UserController.php.html#974\">Sparefoot\\PitaService\\Controller\\UserController::_processEmailRecipientReport<\/a>"],[0,2,"<a href=\"UserController.php.html#1084\">Sparefoot\\PitaService\\Controller\\UserController::_addAcctMgmtUser<\/a>"],[0,8,"<a href=\"UserController.php.html#1101\">Sparefoot\\PitaService\\Controller\\UserController::_acctMgmtRecipients<\/a>"],[0,11,"<a href=\"UserController.php.html#1150\">Sparefoot\\PitaService\\Controller\\UserController::_facilityContactRecipients<\/a>"],[0,2,"<a href=\"UserController.php.html#1203\">Sparefoot\\PitaService\\Controller\\UserController::_getActiveAcctIds<\/a>"],[0,2,"<a href=\"UserController.php.html#1231\">Sparefoot\\PitaService\\Controller\\UserController::_getActiveListingAvailIds<\/a>"],[0,18,"<a href=\"UserController.php.html#1259\">Sparefoot\\PitaService\\Controller\\UserController::createusersAction<\/a>"],[0,1,"<a href=\"UserController.php.html#1376\">Sparefoot\\PitaService\\Controller\\UserController::tokenAction<\/a>"],[0,2,"<a href=\"UtilitiesController.php.html#26\">Sparefoot\\PitaService\\Controller\\UtilitiesController::initBeforeControllerAction<\/a>"],[0,2,"<a href=\"UtilitiesController.php.html#47\">Sparefoot\\PitaService\\Controller\\UtilitiesController::indexAction<\/a>"],[0,10,"<a href=\"UtilitiesController.php.html#69\">Sparefoot\\PitaService\\Controller\\UtilitiesController::renderAction<\/a>"],[0,3,"<a href=\"UtilitiesController.php.html#142\">Sparefoot\\PitaService\\Controller\\UtilitiesController::updateInputsAction<\/a>"],[0,2,"<a href=\"YellowpagesController.php.html#24\">Sparefoot\\PitaService\\Controller\\YellowpagesController::initBeforeControllerAction<\/a>"],[0,5,"<a href=\"YellowpagesController.php.html#58\">Sparefoot\\PitaService\\Controller\\YellowpagesController::indexAction<\/a>"],[0,4,"<a href=\"YellowpagesController.php.html#103\">Sparefoot\\PitaService\\Controller\\YellowpagesController::citygridAction<\/a>"],[0,3,"<a href=\"YellowpagesController.php.html#148\">Sparefoot\\PitaService\\Controller\\YellowpagesController::citygridfacilityAction<\/a>"],[0,3,"<a href=\"YellowpagesController.php.html#199\">Sparefoot\\PitaService\\Controller\\YellowpagesController::superpagesAction<\/a>"],[0,4,"<a href=\"YellowpagesController.php.html#233\">Sparefoot\\PitaService\\Controller\\YellowpagesController::superpagesfacilityAction<\/a>"],[0,1,"<a href=\"YellowpagesController.php.html#263\">Sparefoot\\PitaService\\Controller\\YellowpagesController::allowiypAction<\/a>"],[0,7,"<a href=\"YellowpagesController.php.html#277\">Sparefoot\\PitaService\\Controller\\YellowpagesController::accountonAction<\/a>"],[0,7,"<a href=\"YellowpagesController.php.html#313\">Sparefoot\\PitaService\\Controller\\YellowpagesController::accountoffAction<\/a>"],[0,1,"<a href=\"YellowpagesController.php.html#359\">Sparefoot\\PitaService\\Controller\\YellowpagesController::localdotcomAction<\/a>"],[0,3,"<a href=\"YellowpagesController.php.html#365\">Sparefoot\\PitaService\\Controller\\YellowpagesController::_initTrueDateRange<\/a>"],[0,1,"<a href=\"YellowpagesController.php.html#385\">Sparefoot\\PitaService\\Controller\\YellowpagesController::getTrueBeginDate<\/a>"],[0,1,"<a href=\"YellowpagesController.php.html#390\">Sparefoot\\PitaService\\Controller\\YellowpagesController::getTrueEndDate<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

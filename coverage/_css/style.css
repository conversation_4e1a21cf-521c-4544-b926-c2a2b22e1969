body {
 font-family: sans-serif;
 font-size: 1em;
 font-kerning: normal;
 font-variant-ligatures: common-ligatures;
 text-rendering: optimizeLegibility;
 padding-top: 10px;
}

.popover {
 max-width: none;
}

.octicon {
 margin-right:.25em;
 vertical-align: baseline;
 width: 0.75em;
}

.table-bordered>thead>tr>td {
 border-bottom-width: 1px;
}

.table tbody>tr>td, .table thead>tr>td {
 padding-top: 3px;
 padding-bottom: 3px;
}

.table-condensed tbody>tr>td {
 padding-top: 0;
 padding-bottom: 0;
}

.table .progress {
 margin-bottom: inherit;
}

.table-borderless th, .table-borderless td {
 border: 0 !important;
}

.table tbody tr.covered-by-large-tests, li.covered-by-large-tests, tr.success, td.success, li.success, span.success {
 background-color: #dff0d8;
}

.table tbody tr.covered-by-medium-tests, li.covered-by-medium-tests {
 background-color: #c3e3b5;
}

.table tbody tr.covered-by-small-tests, li.covered-by-small-tests {
 background-color: #99cb84;
}

.table tbody tr.warning, .table tbody td.warning, li.warning, span.warning {
 background-color: #fcf8e3;
}

.table tbody tr.danger, .table tbody td.danger, li.danger, span.danger {
 background-color: #f2dede;
}

.table tbody td.info {
 background-color: #d9edf7;
}

td.big {
 vertical-align: middle;
 width: 117px;
}

td.small {
}

td.codeLine {
 font-family: "Source Code Pro", "SFMono-Regular", Menlo, Monaco, Consolas, "Liberation Mono", "Courier New", monospace;
 white-space: pre-wrap;
}

td span.comment {
 color: #888a85;
}

td span.default {
 color: #2e3436;
}

td span.html {
 color: #888a85;
}

td span.keyword {
 color: #2e3436;
 font-weight: bold;
}

pre span.string {
 color: #2e3436;
}

span.success, span.warning, span.danger {
 margin-right: 2px;
 padding-left: 10px;
 padding-right: 10px;
 text-align: center;
}

#toplink {
 position: fixed;
 left: 5px;
 bottom: 5px;
 outline: 0;
}

svg text {
 font-family: "Lucida Grande", "Lucida Sans Unicode", Verdana, Arial, Helvetica, sans-serif;
 font-size: 11px;
 color: #666;
 fill: #666;
}

.scrollbox {
 height:245px;
 overflow-x:scroll;
 overflow-y:scroll;
}

table + .structure-heading {
 border-top: 1px solid lightgrey;
 padding-top: 0.5em;
}

.legend {
 font-weight: bold;
 margin-right: 2px;
 padding-left: 10px;
 padding-right: 10px;
 text-align: center;
}

.covered-by-small-tests {
 background-color: #99cb84;
}

.covered-by-medium-tests {
 background-color: #c3e3b5;
}

.covered-by-large-tests {
 background-color: #dff0d8;
}

.not-covered {
 background-color: #f2dede;
}

.not-coverable {
 background-color: #fcf8e3;
}

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/EventSubscriber</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="index.html">EventSubscriber</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ControllerInitSubscriber.php.html#10">Sparefoot\PitaService\EventSubscriber\ControllerInitSubscriber</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryAccessSubscriber.php.html#10">Sparefoot\PitaService\EventSubscriber\InventoryAccessSubscriber</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeSubscriber.php.html#11">Sparefoot\PitaService\EventSubscriber\MaintenanceModeSubscriber</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="InventoryAccessSubscriber.php.html#10">Sparefoot\PitaService\EventSubscriber\InventoryAccessSubscriber</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ControllerInitSubscriber.php.html#10">Sparefoot\PitaService\EventSubscriber\ControllerInitSubscriber</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="MaintenanceModeSubscriber.php.html#11">Sparefoot\PitaService\EventSubscriber\MaintenanceModeSubscriber</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ControllerInitSubscriber.php.html#12"><abbr title="Sparefoot\PitaService\EventSubscriber\ControllerInitSubscriber::getSubscribedEvents">getSubscribedEvents</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ControllerInitSubscriber.php.html#19"><abbr title="Sparefoot\PitaService\EventSubscriber\ControllerInitSubscriber::onKernelController">onKernelController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryAccessSubscriber.php.html#41"><abbr title="Sparefoot\PitaService\EventSubscriber\InventoryAccessSubscriber::getSubscribedEvents">getSubscribedEvents</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InventoryAccessSubscriber.php.html#48"><abbr title="Sparefoot\PitaService\EventSubscriber\InventoryAccessSubscriber::onController">onController</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeSubscriber.php.html#16"><abbr title="Sparefoot\PitaService\EventSubscriber\MaintenanceModeSubscriber::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeSubscriber.php.html#22"><abbr title="Sparefoot\PitaService\EventSubscriber\MaintenanceModeSubscriber::onKernelRequest">onKernelRequest</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MaintenanceModeSubscriber.php.html#57"><abbr title="Sparefoot\PitaService\EventSubscriber\MaintenanceModeSubscriber::getSubscribedEvents">getSubscribedEvents</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="InventoryAccessSubscriber.php.html#48"><abbr title="Sparefoot\PitaService\EventSubscriber\InventoryAccessSubscriber::onController">onController</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ControllerInitSubscriber.php.html#19"><abbr title="Sparefoot\PitaService\EventSubscriber\ControllerInitSubscriber::onKernelController">onKernelController</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="MaintenanceModeSubscriber.php.html#22"><abbr title="Sparefoot\PitaService\EventSubscriber\MaintenanceModeSubscriber::onKernelRequest">onKernelRequest</abbr></a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([3,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([7,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"ControllerInitSubscriber.php.html#10\">Sparefoot\\PitaService\\EventSubscriber\\ControllerInitSubscriber<\/a>"],[0,7,"<a href=\"InventoryAccessSubscriber.php.html#10\">Sparefoot\\PitaService\\EventSubscriber\\InventoryAccessSubscriber<\/a>"],[0,6,"<a href=\"MaintenanceModeSubscriber.php.html#11\">Sparefoot\\PitaService\\EventSubscriber\\MaintenanceModeSubscriber<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ControllerInitSubscriber.php.html#12\">Sparefoot\\PitaService\\EventSubscriber\\ControllerInitSubscriber::getSubscribedEvents<\/a>"],[0,5,"<a href=\"ControllerInitSubscriber.php.html#19\">Sparefoot\\PitaService\\EventSubscriber\\ControllerInitSubscriber::onKernelController<\/a>"],[0,1,"<a href=\"InventoryAccessSubscriber.php.html#41\">Sparefoot\\PitaService\\EventSubscriber\\InventoryAccessSubscriber::getSubscribedEvents<\/a>"],[0,6,"<a href=\"InventoryAccessSubscriber.php.html#48\">Sparefoot\\PitaService\\EventSubscriber\\InventoryAccessSubscriber::onController<\/a>"],[0,1,"<a href=\"MaintenanceModeSubscriber.php.html#16\">Sparefoot\\PitaService\\EventSubscriber\\MaintenanceModeSubscriber::__construct<\/a>"],[0,4,"<a href=\"MaintenanceModeSubscriber.php.html#22\">Sparefoot\\PitaService\\EventSubscriber\\MaintenanceModeSubscriber::onKernelRequest<\/a>"],[0,1,"<a href=\"MaintenanceModeSubscriber.php.html#57\">Sparefoot\\PitaService\\EventSubscriber\\MaintenanceModeSubscriber::getSubscribedEvents<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickRep/Report/Cpapercentrollout</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Report</a></li>
         <li class="breadcrumb-item"><a href="index.html">Cpapercentrollout</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AcceptanceTimeline.php.html#7">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AcceptanceTimeline</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountList.php.html#7">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountList</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountRollup.php.html#7">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountRollup</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BidAmounts.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\BidAmounts</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Emails.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\Emails</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarketOverrides.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MarketOverrides</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MoveInFeeComparison.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MoveInFeeComparison</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaleAccounts.php.html#7">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\StaleAccounts</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BidAmounts.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\BidAmounts</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="MarketOverrides.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MarketOverrides</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="MoveInFeeComparison.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MoveInFeeComparison</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="Emails.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\Emails</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AcceptanceTimeline.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AcceptanceTimeline::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AcceptanceTimeline.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AcceptanceTimeline::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AcceptanceTimeline.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AcceptanceTimeline::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AcceptanceTimeline.php.html#24"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AcceptanceTimeline::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AcceptanceTimeline.php.html#29"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AcceptanceTimeline::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AcceptanceTimeline.php.html#53"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AcceptanceTimeline::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountList.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountList::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountList.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountList::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountList.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountList::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountList.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountList::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountList.php.html#31"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountList::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountRollup.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountRollup::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountRollup.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountRollup::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountRollup.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountRollup::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountRollup.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountRollup::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountRollup.php.html#30"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\AccountRollup::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BidAmounts.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\BidAmounts::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BidAmounts.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\BidAmounts::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BidAmounts.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\BidAmounts::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BidAmounts.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\BidAmounts::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BidAmounts.php.html#57"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\BidAmounts::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Emails.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\Emails::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Emails.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\Emails::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Emails.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\Emails::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Emails.php.html#27"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\Emails::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Emails.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\Emails::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarketOverrides.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MarketOverrides::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarketOverrides.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MarketOverrides::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarketOverrides.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MarketOverrides::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarketOverrides.php.html#27"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MarketOverrides::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MarketOverrides.php.html#58"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MarketOverrides::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MoveInFeeComparison.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MoveInFeeComparison::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MoveInFeeComparison.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MoveInFeeComparison::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MoveInFeeComparison.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MoveInFeeComparison::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MoveInFeeComparison.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MoveInFeeComparison::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MoveInFeeComparison.php.html#65"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MoveInFeeComparison::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaleAccounts.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\StaleAccounts::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaleAccounts.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\StaleAccounts::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaleAccounts.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\StaleAccounts::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaleAccounts.php.html#28"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\StaleAccounts::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StaleAccounts.php.html#33"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\StaleAccounts::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BidAmounts.php.html#57"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\BidAmounts::getSql">getSql</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MarketOverrides.php.html#58"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MarketOverrides::getSql">getSql</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MoveInFeeComparison.php.html#65"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\MoveInFeeComparison::getSql">getSql</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="Emails.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cpapercentrollout\Emails::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([8,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([41,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"AcceptanceTimeline.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AcceptanceTimeline<\/a>"],[0,5,"<a href=\"AccountList.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountList<\/a>"],[0,5,"<a href=\"AccountRollup.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountRollup<\/a>"],[0,7,"<a href=\"BidAmounts.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\BidAmounts<\/a>"],[0,6,"<a href=\"Emails.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\Emails<\/a>"],[0,7,"<a href=\"MarketOverrides.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MarketOverrides<\/a>"],[0,7,"<a href=\"MoveInFeeComparison.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MoveInFeeComparison<\/a>"],[0,5,"<a href=\"StaleAccounts.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\StaleAccounts<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AcceptanceTimeline.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AcceptanceTimeline::getCategory<\/a>"],[0,1,"<a href=\"AcceptanceTimeline.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AcceptanceTimeline::getName<\/a>"],[0,1,"<a href=\"AcceptanceTimeline.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AcceptanceTimeline::getDescription<\/a>"],[0,1,"<a href=\"AcceptanceTimeline.php.html#24\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AcceptanceTimeline::getInputs<\/a>"],[0,1,"<a href=\"AcceptanceTimeline.php.html#29\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AcceptanceTimeline::getCharts<\/a>"],[0,1,"<a href=\"AcceptanceTimeline.php.html#53\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AcceptanceTimeline::getSql<\/a>"],[0,1,"<a href=\"AccountList.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountList::getCategory<\/a>"],[0,1,"<a href=\"AccountList.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountList::getName<\/a>"],[0,1,"<a href=\"AccountList.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountList::getDescription<\/a>"],[0,1,"<a href=\"AccountList.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountList::getInputs<\/a>"],[0,1,"<a href=\"AccountList.php.html#31\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountList::getSql<\/a>"],[0,1,"<a href=\"AccountRollup.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountRollup::getCategory<\/a>"],[0,1,"<a href=\"AccountRollup.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountRollup::getName<\/a>"],[0,1,"<a href=\"AccountRollup.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountRollup::getDescription<\/a>"],[0,1,"<a href=\"AccountRollup.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountRollup::getInputs<\/a>"],[0,1,"<a href=\"AccountRollup.php.html#30\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\AccountRollup::getSql<\/a>"],[0,1,"<a href=\"BidAmounts.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\BidAmounts::getCategory<\/a>"],[0,1,"<a href=\"BidAmounts.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\BidAmounts::getName<\/a>"],[0,1,"<a href=\"BidAmounts.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\BidAmounts::getDescription<\/a>"],[0,1,"<a href=\"BidAmounts.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\BidAmounts::getInputs<\/a>"],[0,3,"<a href=\"BidAmounts.php.html#57\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\BidAmounts::getSql<\/a>"],[0,1,"<a href=\"Emails.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\Emails::getCategory<\/a>"],[0,1,"<a href=\"Emails.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\Emails::getName<\/a>"],[0,1,"<a href=\"Emails.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\Emails::getDescription<\/a>"],[0,1,"<a href=\"Emails.php.html#27\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\Emails::getInputs<\/a>"],[0,2,"<a href=\"Emails.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\Emails::getSql<\/a>"],[0,1,"<a href=\"MarketOverrides.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MarketOverrides::getCategory<\/a>"],[0,1,"<a href=\"MarketOverrides.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MarketOverrides::getName<\/a>"],[0,1,"<a href=\"MarketOverrides.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MarketOverrides::getDescription<\/a>"],[0,1,"<a href=\"MarketOverrides.php.html#27\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MarketOverrides::getInputs<\/a>"],[0,3,"<a href=\"MarketOverrides.php.html#58\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MarketOverrides::getSql<\/a>"],[0,1,"<a href=\"MoveInFeeComparison.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MoveInFeeComparison::getCategory<\/a>"],[0,1,"<a href=\"MoveInFeeComparison.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MoveInFeeComparison::getName<\/a>"],[0,1,"<a href=\"MoveInFeeComparison.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MoveInFeeComparison::getDescription<\/a>"],[0,1,"<a href=\"MoveInFeeComparison.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MoveInFeeComparison::getInputs<\/a>"],[0,3,"<a href=\"MoveInFeeComparison.php.html#65\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\MoveInFeeComparison::getSql<\/a>"],[0,1,"<a href=\"StaleAccounts.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\StaleAccounts::getCategory<\/a>"],[0,1,"<a href=\"StaleAccounts.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\StaleAccounts::getName<\/a>"],[0,1,"<a href=\"StaleAccounts.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\StaleAccounts::getDescription<\/a>"],[0,1,"<a href=\"StaleAccounts.php.html#28\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\StaleAccounts::getInputs<\/a>"],[0,1,"<a href=\"StaleAccounts.php.html#33\">Sparefoot\\PitaService\\QuickRep\\Report\\Cpapercentrollout\\StaleAccounts::getSql<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

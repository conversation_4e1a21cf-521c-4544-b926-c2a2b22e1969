<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickRep/Report/MyFoot</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Report</a></li>
         <li class="breadcrumb-item"><a href="index.html">MyFoot</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccountMTDBookings.php.html#7">Sparefoot\PitaService\QuickRep\Report\MyFoot\AccountMTDBookings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountMTDBookingsSitelink.php.html#13">Sparefoot\PitaService\QuickRep\Report\MyFoot\AccountMTDBookingsSitelink</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompetitorAmenities.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\CompetitorAmenities</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomCompetitorUnitPrices.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\CustomCompetitorUnitPrices</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExpandedInsights.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\ExpandedInsights</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#7">Sparefoot\PitaService\QuickRep\Report\MyFoot\ExportCubesmartBookings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityImpressionsAndClicks.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityImpressionsAndClicks</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityMetrics.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityMetrics</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityRegionalPricePromo.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityRegionalPricePromo</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityWebPerformance.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityWebPerformance</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeeEstimator.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\FeeEstimator</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MonthlyStatementReportByFacility.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\MonthlyStatementReportByFacility</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NationalPriceIndex.php.html#7">Sparefoot\PitaService\QuickRep\Report\MyFoot\NationalPriceIndex</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCode.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCode</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCodeAggregated.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCodeAggregated</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalAdminFees.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\RegionalAdminFees</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsByTimestamp.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\ReviewsByTimestamp</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CompetitorAmenities.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\CompetitorAmenities</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="CustomCompetitorUnitPrices.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\CustomCompetitorUnitPrices</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ExpandedInsights.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\ExpandedInsights</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="FacilityRegionalPricePromo.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityRegionalPricePromo</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="FacilityWebPerformance.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityWebPerformance</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PriceReportByZipCode.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCode</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="PriceReportByZipCodeAggregated.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCodeAggregated</a></td><td class="text-right">30</td></tr>
       <tr><td><a href="RegionalAdminFees.php.html#8">Sparefoot\PitaService\QuickRep\Report\MyFoot\RegionalAdminFees</a></td><td class="text-right">30</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AccountMTDBookings.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\AccountMTDBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountMTDBookings.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\AccountMTDBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountMTDBookings.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\AccountMTDBookings::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountMTDBookingsSitelink.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\AccountMTDBookingsSitelink::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountMTDBookingsSitelink.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\AccountMTDBookingsSitelink::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AccountMTDBookingsSitelink.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\AccountMTDBookingsSitelink::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompetitorAmenities.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CompetitorAmenities::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompetitorAmenities.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CompetitorAmenities::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompetitorAmenities.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CompetitorAmenities::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CompetitorAmenities.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CompetitorAmenities::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomCompetitorUnitPrices.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CustomCompetitorUnitPrices::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomCompetitorUnitPrices.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CustomCompetitorUnitPrices::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomCompetitorUnitPrices.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CustomCompetitorUnitPrices::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CustomCompetitorUnitPrices.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CustomCompetitorUnitPrices::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExpandedInsights.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExpandedInsights::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExpandedInsights.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExpandedInsights::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExpandedInsights.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExpandedInsights::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExpandedInsights.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExpandedInsights::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExportCubesmartBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExportCubesmartBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExportCubesmartBookings::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#24"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExportCubesmartBookings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#28"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExportCubesmartBookings::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityImpressionsAndClicks.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityImpressionsAndClicks::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityImpressionsAndClicks.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityImpressionsAndClicks::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityImpressionsAndClicks.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityImpressionsAndClicks::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityImpressionsAndClicks.php.html#30"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityImpressionsAndClicks::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityMetrics.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityMetrics::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityMetrics.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityMetrics::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityMetrics.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityMetrics::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityMetrics.php.html#30"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityMetrics::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityRegionalPricePromo.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityRegionalPricePromo::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityRegionalPricePromo.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityRegionalPricePromo::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityRegionalPricePromo.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityRegionalPricePromo::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityRegionalPricePromo.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityRegionalPricePromo::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityWebPerformance.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityWebPerformance::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityWebPerformance.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityWebPerformance::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityWebPerformance.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityWebPerformance::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilityWebPerformance.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityWebPerformance::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeeEstimator.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FeeEstimator::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeeEstimator.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FeeEstimator::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeeEstimator.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FeeEstimator::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FeeEstimator.php.html#30"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FeeEstimator::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MonthlyStatementReportByFacility.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\MonthlyStatementReportByFacility::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MonthlyStatementReportByFacility.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\MonthlyStatementReportByFacility::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MonthlyStatementReportByFacility.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\MonthlyStatementReportByFacility::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MonthlyStatementReportByFacility.php.html#38"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\MonthlyStatementReportByFacility::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NationalPriceIndex.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\NationalPriceIndex::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NationalPriceIndex.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\NationalPriceIndex::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NationalPriceIndex.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\NationalPriceIndex::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCode.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCode::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCode.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCode::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCode.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCode::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCode.php.html#29"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCode::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCodeAggregated.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCodeAggregated::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCodeAggregated.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCodeAggregated::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCodeAggregated.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCodeAggregated::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PriceReportByZipCodeAggregated.php.html#29"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCodeAggregated::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalAdminFees.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\RegionalAdminFees::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalAdminFees.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\RegionalAdminFees::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalAdminFees.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\RegionalAdminFees::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RegionalAdminFees.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\RegionalAdminFees::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsByTimestamp.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ReviewsByTimestamp::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsByTimestamp.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ReviewsByTimestamp::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsByTimestamp.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ReviewsByTimestamp::prepareParameters">prepareParameters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsByTimestamp.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ReviewsByTimestamp::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReviewsByTimestamp.php.html#35"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ReviewsByTimestamp::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="CompetitorAmenities.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CompetitorAmenities::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="CustomCompetitorUnitPrices.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\CustomCompetitorUnitPrices::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExpandedInsights.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\ExpandedInsights::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FacilityRegionalPricePromo.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityRegionalPricePromo::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FacilityWebPerformance.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\FacilityWebPerformance::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PriceReportByZipCode.php.html#29"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCode::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="PriceReportByZipCodeAggregated.php.html#29"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\PriceReportByZipCodeAggregated::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RegionalAdminFees.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\MyFoot\RegionalAdminFees::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([17,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([67,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,3,"<a href=\"AccountMTDBookings.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\AccountMTDBookings<\/a>"],[0,3,"<a href=\"AccountMTDBookingsSitelink.php.html#13\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\AccountMTDBookingsSitelink<\/a>"],[0,5,"<a href=\"CompetitorAmenities.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CompetitorAmenities<\/a>"],[0,5,"<a href=\"CustomCompetitorUnitPrices.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CustomCompetitorUnitPrices<\/a>"],[0,5,"<a href=\"ExpandedInsights.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExpandedInsights<\/a>"],[0,5,"<a href=\"ExportCubesmartBookings.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExportCubesmartBookings<\/a>"],[0,4,"<a href=\"FacilityImpressionsAndClicks.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityImpressionsAndClicks<\/a>"],[0,4,"<a href=\"FacilityMetrics.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityMetrics<\/a>"],[0,5,"<a href=\"FacilityRegionalPricePromo.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityRegionalPricePromo<\/a>"],[0,5,"<a href=\"FacilityWebPerformance.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityWebPerformance<\/a>"],[0,4,"<a href=\"FeeEstimator.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FeeEstimator<\/a>"],[0,4,"<a href=\"MonthlyStatementReportByFacility.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\MonthlyStatementReportByFacility<\/a>"],[0,3,"<a href=\"NationalPriceIndex.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\NationalPriceIndex<\/a>"],[0,5,"<a href=\"PriceReportByZipCode.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCode<\/a>"],[0,5,"<a href=\"PriceReportByZipCodeAggregated.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCodeAggregated<\/a>"],[0,5,"<a href=\"RegionalAdminFees.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\RegionalAdminFees<\/a>"],[0,5,"<a href=\"ReviewsByTimestamp.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ReviewsByTimestamp<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AccountMTDBookings.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\AccountMTDBookings::getCategory<\/a>"],[0,1,"<a href=\"AccountMTDBookings.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\AccountMTDBookings::getName<\/a>"],[0,1,"<a href=\"AccountMTDBookings.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\AccountMTDBookings::getSql<\/a>"],[0,1,"<a href=\"AccountMTDBookingsSitelink.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\AccountMTDBookingsSitelink::getCategory<\/a>"],[0,1,"<a href=\"AccountMTDBookingsSitelink.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\AccountMTDBookingsSitelink::getName<\/a>"],[0,1,"<a href=\"AccountMTDBookingsSitelink.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\AccountMTDBookingsSitelink::getSql<\/a>"],[0,1,"<a href=\"CompetitorAmenities.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CompetitorAmenities::getCategory<\/a>"],[0,1,"<a href=\"CompetitorAmenities.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CompetitorAmenities::getName<\/a>"],[0,1,"<a href=\"CompetitorAmenities.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CompetitorAmenities::getInputs<\/a>"],[0,2,"<a href=\"CompetitorAmenities.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CompetitorAmenities::getSql<\/a>"],[0,1,"<a href=\"CustomCompetitorUnitPrices.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CustomCompetitorUnitPrices::getCategory<\/a>"],[0,1,"<a href=\"CustomCompetitorUnitPrices.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CustomCompetitorUnitPrices::getName<\/a>"],[0,1,"<a href=\"CustomCompetitorUnitPrices.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CustomCompetitorUnitPrices::getInputs<\/a>"],[0,2,"<a href=\"CustomCompetitorUnitPrices.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\CustomCompetitorUnitPrices::getSql<\/a>"],[0,1,"<a href=\"ExpandedInsights.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExpandedInsights::getCategory<\/a>"],[0,1,"<a href=\"ExpandedInsights.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExpandedInsights::getName<\/a>"],[0,1,"<a href=\"ExpandedInsights.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExpandedInsights::getInputs<\/a>"],[0,2,"<a href=\"ExpandedInsights.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExpandedInsights::getSql<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExportCubesmartBookings::getCategory<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExportCubesmartBookings::getName<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExportCubesmartBookings::getDescription<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#24\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExportCubesmartBookings::getInputs<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#28\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ExportCubesmartBookings::getSql<\/a>"],[0,1,"<a href=\"FacilityImpressionsAndClicks.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityImpressionsAndClicks::getCategory<\/a>"],[0,1,"<a href=\"FacilityImpressionsAndClicks.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityImpressionsAndClicks::getName<\/a>"],[0,1,"<a href=\"FacilityImpressionsAndClicks.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityImpressionsAndClicks::getInputs<\/a>"],[0,1,"<a href=\"FacilityImpressionsAndClicks.php.html#30\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityImpressionsAndClicks::getSql<\/a>"],[0,1,"<a href=\"FacilityMetrics.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityMetrics::getCategory<\/a>"],[0,1,"<a href=\"FacilityMetrics.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityMetrics::getName<\/a>"],[0,1,"<a href=\"FacilityMetrics.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityMetrics::getInputs<\/a>"],[0,1,"<a href=\"FacilityMetrics.php.html#30\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityMetrics::getSql<\/a>"],[0,1,"<a href=\"FacilityRegionalPricePromo.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityRegionalPricePromo::getCategory<\/a>"],[0,1,"<a href=\"FacilityRegionalPricePromo.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityRegionalPricePromo::getName<\/a>"],[0,1,"<a href=\"FacilityRegionalPricePromo.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityRegionalPricePromo::getInputs<\/a>"],[0,2,"<a href=\"FacilityRegionalPricePromo.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityRegionalPricePromo::getSql<\/a>"],[0,1,"<a href=\"FacilityWebPerformance.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityWebPerformance::getCategory<\/a>"],[0,1,"<a href=\"FacilityWebPerformance.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityWebPerformance::getName<\/a>"],[0,1,"<a href=\"FacilityWebPerformance.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityWebPerformance::getInputs<\/a>"],[0,2,"<a href=\"FacilityWebPerformance.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FacilityWebPerformance::getSql<\/a>"],[0,1,"<a href=\"FeeEstimator.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FeeEstimator::getCategory<\/a>"],[0,1,"<a href=\"FeeEstimator.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FeeEstimator::getName<\/a>"],[0,1,"<a href=\"FeeEstimator.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FeeEstimator::getInputs<\/a>"],[0,1,"<a href=\"FeeEstimator.php.html#30\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\FeeEstimator::getSql<\/a>"],[0,1,"<a href=\"MonthlyStatementReportByFacility.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\MonthlyStatementReportByFacility::getCategory<\/a>"],[0,1,"<a href=\"MonthlyStatementReportByFacility.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\MonthlyStatementReportByFacility::getName<\/a>"],[0,1,"<a href=\"MonthlyStatementReportByFacility.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\MonthlyStatementReportByFacility::getInputs<\/a>"],[0,1,"<a href=\"MonthlyStatementReportByFacility.php.html#38\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\MonthlyStatementReportByFacility::getSql<\/a>"],[0,1,"<a href=\"NationalPriceIndex.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\NationalPriceIndex::getCategory<\/a>"],[0,1,"<a href=\"NationalPriceIndex.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\NationalPriceIndex::getName<\/a>"],[0,1,"<a href=\"NationalPriceIndex.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\NationalPriceIndex::getSql<\/a>"],[0,1,"<a href=\"PriceReportByZipCode.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCode::getCategory<\/a>"],[0,1,"<a href=\"PriceReportByZipCode.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCode::getName<\/a>"],[0,1,"<a href=\"PriceReportByZipCode.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCode::getInputs<\/a>"],[0,2,"<a href=\"PriceReportByZipCode.php.html#29\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCode::getSql<\/a>"],[0,1,"<a href=\"PriceReportByZipCodeAggregated.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCodeAggregated::getCategory<\/a>"],[0,1,"<a href=\"PriceReportByZipCodeAggregated.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCodeAggregated::getName<\/a>"],[0,1,"<a href=\"PriceReportByZipCodeAggregated.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCodeAggregated::getInputs<\/a>"],[0,2,"<a href=\"PriceReportByZipCodeAggregated.php.html#29\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\PriceReportByZipCodeAggregated::getSql<\/a>"],[0,1,"<a href=\"RegionalAdminFees.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\RegionalAdminFees::getCategory<\/a>"],[0,1,"<a href=\"RegionalAdminFees.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\RegionalAdminFees::getName<\/a>"],[0,1,"<a href=\"RegionalAdminFees.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\RegionalAdminFees::getInputs<\/a>"],[0,2,"<a href=\"RegionalAdminFees.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\RegionalAdminFees::getSql<\/a>"],[0,1,"<a href=\"ReviewsByTimestamp.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ReviewsByTimestamp::getCategory<\/a>"],[0,1,"<a href=\"ReviewsByTimestamp.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ReviewsByTimestamp::getName<\/a>"],[0,1,"<a href=\"ReviewsByTimestamp.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ReviewsByTimestamp::prepareParameters<\/a>"],[0,1,"<a href=\"ReviewsByTimestamp.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ReviewsByTimestamp::getInputs<\/a>"],[0,1,"<a href=\"ReviewsByTimestamp.php.html#35\">Sparefoot\\PitaService\\QuickRep\\Report\\MyFoot\\ReviewsByTimestamp::getSql<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

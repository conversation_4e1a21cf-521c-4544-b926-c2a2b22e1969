<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickRep/Report/Phone</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Report</a></li>
         <li class="breadcrumb-item"><a href="index.html">Phone</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AceAgentCancelationDetails.php.html#9">Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationDetails</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationRate.php.html#8">Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationRate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentConversionRate.php.html#8">Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentConversionRate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenFacilityPhoneNumbers.php.html#8">Sparefoot\PitaService\QuickRep\Report\Phone\HiddenFacilityPhoneNumbers</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InboundPhoneCalls.php.html#9">Sparefoot\PitaService\QuickRep\Report\Phone\InboundPhoneCalls</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LostBookingOpportunities.php.html#8">Sparefoot\PitaService\QuickRep\Report\Phone\LostBookingOpportunities</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmails.php.html#8">Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmails</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmailsByAgent.php.html#8">Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmailsByAgent</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="HiddenFacilityPhoneNumbers.php.html#8">Sparefoot\PitaService\QuickRep\Report\Phone\HiddenFacilityPhoneNumbers</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="InboundPhoneCalls.php.html#9">Sparefoot\PitaService\QuickRep\Report\Phone\InboundPhoneCalls</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AceAgentCancelationDetails.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationDetails::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationDetails.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationDetails::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationDetails.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationDetails::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationDetails.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationDetails::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationDetails.php.html#48"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationDetails::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationRate.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationRate::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationRate.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationRate::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationRate.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationRate::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationRate.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationRate::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentCancelationRate.php.html#35"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentCancelationRate::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentConversionRate.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentConversionRate::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentConversionRate.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentConversionRate::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentConversionRate.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentConversionRate::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentConversionRate.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentConversionRate::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentConversionRate.php.html#35"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentConversionRate::prepareParameters">prepareParameters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentConversionRate.php.html#42"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentConversionRate::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AceAgentConversionRate.php.html#62"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\AceAgentConversionRate::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenFacilityPhoneNumbers.php.html#13"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\HiddenFacilityPhoneNumbers::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenFacilityPhoneNumbers.php.html#18"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\HiddenFacilityPhoneNumbers::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenFacilityPhoneNumbers.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\HiddenFacilityPhoneNumbers::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenFacilityPhoneNumbers.php.html#28"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\HiddenFacilityPhoneNumbers::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenFacilityPhoneNumbers.php.html#38"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\HiddenFacilityPhoneNumbers::prepareParameters">prepareParameters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenFacilityPhoneNumbers.php.html#59"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\HiddenFacilityPhoneNumbers::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InboundPhoneCalls.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\InboundPhoneCalls::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InboundPhoneCalls.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\InboundPhoneCalls::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InboundPhoneCalls.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\InboundPhoneCalls::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InboundPhoneCalls.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\InboundPhoneCalls::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InboundPhoneCalls.php.html#41"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\InboundPhoneCalls::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LostBookingOpportunities.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\LostBookingOpportunities::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LostBookingOpportunities.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\LostBookingOpportunities::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LostBookingOpportunities.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\LostBookingOpportunities::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LostBookingOpportunities.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\LostBookingOpportunities::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="LostBookingOpportunities.php.html#35"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\LostBookingOpportunities::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmails.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmails::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmails.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmails::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmails.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmails::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmails.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmails::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmails.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmails::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmailsByAgent.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmailsByAgent::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmailsByAgent.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmailsByAgent::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmailsByAgent.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmailsByAgent::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmailsByAgent.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmailsByAgent::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmailsByAgent.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmailsByAgent::prepareParameters">prepareParameters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StarredEmailsByAgent.php.html#44"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\StarredEmailsByAgent::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="HiddenFacilityPhoneNumbers.php.html#38"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\HiddenFacilityPhoneNumbers::prepareParameters">prepareParameters</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="InboundPhoneCalls.php.html#41"><abbr title="Sparefoot\PitaService\QuickRep\Report\Phone\InboundPhoneCalls::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([8,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([44,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,5,"<a href=\"AceAgentCancelationDetails.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationDetails<\/a>"],[0,5,"<a href=\"AceAgentCancelationRate.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationRate<\/a>"],[0,7,"<a href=\"AceAgentConversionRate.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentConversionRate<\/a>"],[0,10,"<a href=\"HiddenFacilityPhoneNumbers.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\HiddenFacilityPhoneNumbers<\/a>"],[0,6,"<a href=\"InboundPhoneCalls.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\InboundPhoneCalls<\/a>"],[0,5,"<a href=\"LostBookingOpportunities.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\LostBookingOpportunities<\/a>"],[0,5,"<a href=\"StarredEmails.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmails<\/a>"],[0,6,"<a href=\"StarredEmailsByAgent.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmailsByAgent<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AceAgentCancelationDetails.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationDetails::getCategory<\/a>"],[0,1,"<a href=\"AceAgentCancelationDetails.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationDetails::getName<\/a>"],[0,1,"<a href=\"AceAgentCancelationDetails.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationDetails::getDescription<\/a>"],[0,1,"<a href=\"AceAgentCancelationDetails.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationDetails::getInputs<\/a>"],[0,1,"<a href=\"AceAgentCancelationDetails.php.html#48\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationDetails::getSql<\/a>"],[0,1,"<a href=\"AceAgentCancelationRate.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationRate::getCategory<\/a>"],[0,1,"<a href=\"AceAgentCancelationRate.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationRate::getName<\/a>"],[0,1,"<a href=\"AceAgentCancelationRate.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationRate::getDescription<\/a>"],[0,1,"<a href=\"AceAgentCancelationRate.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationRate::getInputs<\/a>"],[0,1,"<a href=\"AceAgentCancelationRate.php.html#35\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentCancelationRate::getSql<\/a>"],[0,1,"<a href=\"AceAgentConversionRate.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentConversionRate::getCategory<\/a>"],[0,1,"<a href=\"AceAgentConversionRate.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentConversionRate::getName<\/a>"],[0,1,"<a href=\"AceAgentConversionRate.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentConversionRate::getDescription<\/a>"],[0,1,"<a href=\"AceAgentConversionRate.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentConversionRate::getInputs<\/a>"],[0,1,"<a href=\"AceAgentConversionRate.php.html#35\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentConversionRate::prepareParameters<\/a>"],[0,1,"<a href=\"AceAgentConversionRate.php.html#42\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentConversionRate::getCharts<\/a>"],[0,1,"<a href=\"AceAgentConversionRate.php.html#62\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\AceAgentConversionRate::getSql<\/a>"],[0,1,"<a href=\"HiddenFacilityPhoneNumbers.php.html#13\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\HiddenFacilityPhoneNumbers::getCategory<\/a>"],[0,1,"<a href=\"HiddenFacilityPhoneNumbers.php.html#18\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\HiddenFacilityPhoneNumbers::getName<\/a>"],[0,1,"<a href=\"HiddenFacilityPhoneNumbers.php.html#23\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\HiddenFacilityPhoneNumbers::getDescription<\/a>"],[0,1,"<a href=\"HiddenFacilityPhoneNumbers.php.html#28\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\HiddenFacilityPhoneNumbers::getInputs<\/a>"],[0,5,"<a href=\"HiddenFacilityPhoneNumbers.php.html#38\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\HiddenFacilityPhoneNumbers::prepareParameters<\/a>"],[0,1,"<a href=\"HiddenFacilityPhoneNumbers.php.html#59\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\HiddenFacilityPhoneNumbers::getSql<\/a>"],[0,1,"<a href=\"InboundPhoneCalls.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\InboundPhoneCalls::getCategory<\/a>"],[0,1,"<a href=\"InboundPhoneCalls.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\InboundPhoneCalls::getName<\/a>"],[0,1,"<a href=\"InboundPhoneCalls.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\InboundPhoneCalls::getDescription<\/a>"],[0,1,"<a href=\"InboundPhoneCalls.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\InboundPhoneCalls::getInputs<\/a>"],[0,2,"<a href=\"InboundPhoneCalls.php.html#41\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\InboundPhoneCalls::getSql<\/a>"],[0,1,"<a href=\"LostBookingOpportunities.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\LostBookingOpportunities::getCategory<\/a>"],[0,1,"<a href=\"LostBookingOpportunities.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\LostBookingOpportunities::getName<\/a>"],[0,1,"<a href=\"LostBookingOpportunities.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\LostBookingOpportunities::getDescription<\/a>"],[0,1,"<a href=\"LostBookingOpportunities.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\LostBookingOpportunities::getInputs<\/a>"],[0,1,"<a href=\"LostBookingOpportunities.php.html#35\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\LostBookingOpportunities::getSql<\/a>"],[0,1,"<a href=\"StarredEmails.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmails::getCategory<\/a>"],[0,1,"<a href=\"StarredEmails.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmails::getName<\/a>"],[0,1,"<a href=\"StarredEmails.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmails::getDescription<\/a>"],[0,1,"<a href=\"StarredEmails.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmails::getInputs<\/a>"],[0,1,"<a href=\"StarredEmails.php.html#39\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmails::getSql<\/a>"],[0,1,"<a href=\"StarredEmailsByAgent.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmailsByAgent::getCategory<\/a>"],[0,1,"<a href=\"StarredEmailsByAgent.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmailsByAgent::getName<\/a>"],[0,1,"<a href=\"StarredEmailsByAgent.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmailsByAgent::getDescription<\/a>"],[0,1,"<a href=\"StarredEmailsByAgent.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmailsByAgent::getInputs<\/a>"],[0,1,"<a href=\"StarredEmailsByAgent.php.html#39\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmailsByAgent::prepareParameters<\/a>"],[0,1,"<a href=\"StarredEmailsByAgent.php.html#44\">Sparefoot\\PitaService\\QuickRep\\Report\\Phone\\StarredEmailsByAgent::getSql<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

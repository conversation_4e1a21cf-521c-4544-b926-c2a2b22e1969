<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickRep/Report/Billing</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Report</a></li>
         <li class="breadcrumb-item"><a href="index.html">Billing</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BillableInvoicesByStatementBatch.php.html#9">Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatch</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatchUsingFacilityMap.php.html#9">Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatchUsingFacilityMap</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunErrors.php.html#8">Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunErrors</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunStatus.php.html#8">Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunStatus</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvalidBillableEntities.php.html#7">Sparefoot\PitaService\QuickRep\Report\Billing\InvalidBillableEntities</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#9">Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementBatchRollUpUsingFacilityMap.php.html#8">Sparefoot\PitaService\QuickRep\Report\Billing\StatementBatchRollUpUsingFacilityMap</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBillableInstances.php.html#8">Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBookings.php.html#8">Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBookings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbilledBookings.php.html#8">Sparefoot\PitaService\QuickRep\Report\Billing\UnbilledBookings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnprocessedStatements.php.html#8">Sparefoot\PitaService\QuickRep\Report\Billing\UnprocessedStatements</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="UnbillableBillableInstances.php.html#8">Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#9">Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="UnbillableBookings.php.html#8">Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBookings</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="InvalidBillableEntities.php.html#7">Sparefoot\PitaService\QuickRep\Report\Billing\InvalidBillableEntities</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatch.php.html#9">Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatch</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatchUsingFacilityMap.php.html#9">Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatchUsingFacilityMap</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BillableInvoicesByStatementBatch.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatch::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatch.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatch::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatch.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatch::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatch.php.html#31"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatch::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatch.php.html#54"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatch::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatchUsingFacilityMap.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatchUsingFacilityMap::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatchUsingFacilityMap.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatchUsingFacilityMap::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatchUsingFacilityMap.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatchUsingFacilityMap::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatchUsingFacilityMap.php.html#31"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatchUsingFacilityMap::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatchUsingFacilityMap.php.html#54"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatchUsingFacilityMap::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunErrors.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunErrors::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunErrors.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunErrors::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunErrors.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunErrors::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunErrors.php.html#30"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunErrors::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunErrors.php.html#45"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunErrors::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunStatus.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunStatus::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunStatus.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunStatus::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunStatus.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunStatus::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunStatus.php.html#30"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunStatus::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BillingRunStatus.php.html#45"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillingRunStatus::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvalidBillableEntities.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\InvalidBillableEntities::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvalidBillableEntities.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\InvalidBillableEntities::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvalidBillableEntities.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\InvalidBillableEntities::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvalidBillableEntities.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\InvalidBillableEntities::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvalidBillableEntities.php.html#31"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\InvalidBillableEntities::getAlerts">getAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="InvalidBillableEntities.php.html#60"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\InvalidBillableEntities::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#31"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#51"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement::getAlerts">getAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#98"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#173"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement::_isReportRunDay">_isReportRunDay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementBatchRollUpUsingFacilityMap.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\StatementBatchRollUpUsingFacilityMap::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementBatchRollUpUsingFacilityMap.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\StatementBatchRollUpUsingFacilityMap::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementBatchRollUpUsingFacilityMap.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\StatementBatchRollUpUsingFacilityMap::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementBatchRollUpUsingFacilityMap.php.html#30"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\StatementBatchRollUpUsingFacilityMap::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StatementBatchRollUpUsingFacilityMap.php.html#45"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\StatementBatchRollUpUsingFacilityMap::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBillableInstances.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBillableInstances.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBillableInstances.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBillableInstances.php.html#32"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBillableInstances.php.html#45"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances::getAlerts">getAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBillableInstances.php.html#109"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances::_isReportRunDay">_isReportRunDay</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBillableInstances.php.html#119"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBookings.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBookings.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBookings.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBookings::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBookings.php.html#32"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBookings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBookings.php.html#41"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBookings::getAlerts">getAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbillableBookings.php.html#82"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBookings::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbilledBookings.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbilledBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbilledBookings.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbilledBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbilledBookings.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbilledBookings::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbilledBookings.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbilledBookings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnbilledBookings.php.html#41"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbilledBookings::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnprocessedStatements.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnprocessedStatements::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnprocessedStatements.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnprocessedStatements::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnprocessedStatements.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnprocessedStatements::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnprocessedStatements.php.html#31"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnprocessedStatements::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnprocessedStatements.php.html#49"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnprocessedStatements::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="UnbillableBillableInstances.php.html#45"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBillableInstances::getAlerts">getAlerts</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="OutlierAccountsOnStatement.php.html#51"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\OutlierAccountsOnStatement::getAlerts">getAlerts</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="UnbillableBookings.php.html#41"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\UnbillableBookings::getAlerts">getAlerts</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatch.php.html#54"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatch::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="BillableInvoicesByStatementBatchUsingFacilityMap.php.html#54"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\BillableInvoicesByStatementBatchUsingFacilityMap::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="InvalidBillableEntities.php.html#31"><abbr title="Sparefoot\PitaService\QuickRep\Report\Billing\InvalidBillableEntities::getAlerts">getAlerts</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([11,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([61,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"BillableInvoicesByStatementBatch.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatch<\/a>"],[0,6,"<a href=\"BillableInvoicesByStatementBatchUsingFacilityMap.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatchUsingFacilityMap<\/a>"],[0,5,"<a href=\"BillingRunErrors.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunErrors<\/a>"],[0,5,"<a href=\"BillingRunStatus.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunStatus<\/a>"],[0,7,"<a href=\"InvalidBillableEntities.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\InvalidBillableEntities<\/a>"],[0,11,"<a href=\"OutlierAccountsOnStatement.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\OutlierAccountsOnStatement<\/a>"],[0,5,"<a href=\"StatementBatchRollUpUsingFacilityMap.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\StatementBatchRollUpUsingFacilityMap<\/a>"],[0,13,"<a href=\"UnbillableBillableInstances.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBillableInstances<\/a>"],[0,8,"<a href=\"UnbillableBookings.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBookings<\/a>"],[0,5,"<a href=\"UnbilledBookings.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbilledBookings<\/a>"],[0,5,"<a href=\"UnprocessedStatements.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnprocessedStatements<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BillableInvoicesByStatementBatch.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatch::getCategory<\/a>"],[0,1,"<a href=\"BillableInvoicesByStatementBatch.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatch::getName<\/a>"],[0,1,"<a href=\"BillableInvoicesByStatementBatch.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatch::getDescription<\/a>"],[0,1,"<a href=\"BillableInvoicesByStatementBatch.php.html#31\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatch::getInputs<\/a>"],[0,2,"<a href=\"BillableInvoicesByStatementBatch.php.html#54\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatch::getSql<\/a>"],[0,1,"<a href=\"BillableInvoicesByStatementBatchUsingFacilityMap.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatchUsingFacilityMap::getCategory<\/a>"],[0,1,"<a href=\"BillableInvoicesByStatementBatchUsingFacilityMap.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatchUsingFacilityMap::getName<\/a>"],[0,1,"<a href=\"BillableInvoicesByStatementBatchUsingFacilityMap.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatchUsingFacilityMap::getDescription<\/a>"],[0,1,"<a href=\"BillableInvoicesByStatementBatchUsingFacilityMap.php.html#31\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatchUsingFacilityMap::getInputs<\/a>"],[0,2,"<a href=\"BillableInvoicesByStatementBatchUsingFacilityMap.php.html#54\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillableInvoicesByStatementBatchUsingFacilityMap::getSql<\/a>"],[0,1,"<a href=\"BillingRunErrors.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunErrors::getCategory<\/a>"],[0,1,"<a href=\"BillingRunErrors.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunErrors::getName<\/a>"],[0,1,"<a href=\"BillingRunErrors.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunErrors::getDescription<\/a>"],[0,1,"<a href=\"BillingRunErrors.php.html#30\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunErrors::getInputs<\/a>"],[0,1,"<a href=\"BillingRunErrors.php.html#45\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunErrors::getSql<\/a>"],[0,1,"<a href=\"BillingRunStatus.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunStatus::getCategory<\/a>"],[0,1,"<a href=\"BillingRunStatus.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunStatus::getName<\/a>"],[0,1,"<a href=\"BillingRunStatus.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunStatus::getDescription<\/a>"],[0,1,"<a href=\"BillingRunStatus.php.html#30\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunStatus::getInputs<\/a>"],[0,1,"<a href=\"BillingRunStatus.php.html#45\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\BillingRunStatus::getSql<\/a>"],[0,1,"<a href=\"InvalidBillableEntities.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\InvalidBillableEntities::getCategory<\/a>"],[0,1,"<a href=\"InvalidBillableEntities.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\InvalidBillableEntities::getName<\/a>"],[0,1,"<a href=\"InvalidBillableEntities.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\InvalidBillableEntities::getDescription<\/a>"],[0,1,"<a href=\"InvalidBillableEntities.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\InvalidBillableEntities::getInputs<\/a>"],[0,2,"<a href=\"InvalidBillableEntities.php.html#31\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\InvalidBillableEntities::getAlerts<\/a>"],[0,1,"<a href=\"InvalidBillableEntities.php.html#60\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\InvalidBillableEntities::getSql<\/a>"],[0,1,"<a href=\"OutlierAccountsOnStatement.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\OutlierAccountsOnStatement::getCategory<\/a>"],[0,1,"<a href=\"OutlierAccountsOnStatement.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\OutlierAccountsOnStatement::getName<\/a>"],[0,1,"<a href=\"OutlierAccountsOnStatement.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\OutlierAccountsOnStatement::getDescription<\/a>"],[0,1,"<a href=\"OutlierAccountsOnStatement.php.html#31\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\OutlierAccountsOnStatement::getInputs<\/a>"],[0,5,"<a href=\"OutlierAccountsOnStatement.php.html#51\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\OutlierAccountsOnStatement::getAlerts<\/a>"],[0,1,"<a href=\"OutlierAccountsOnStatement.php.html#98\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\OutlierAccountsOnStatement::getSql<\/a>"],[0,1,"<a href=\"OutlierAccountsOnStatement.php.html#173\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\OutlierAccountsOnStatement::_isReportRunDay<\/a>"],[0,1,"<a href=\"StatementBatchRollUpUsingFacilityMap.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\StatementBatchRollUpUsingFacilityMap::getCategory<\/a>"],[0,1,"<a href=\"StatementBatchRollUpUsingFacilityMap.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\StatementBatchRollUpUsingFacilityMap::getName<\/a>"],[0,1,"<a href=\"StatementBatchRollUpUsingFacilityMap.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\StatementBatchRollUpUsingFacilityMap::getDescription<\/a>"],[0,1,"<a href=\"StatementBatchRollUpUsingFacilityMap.php.html#30\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\StatementBatchRollUpUsingFacilityMap::getInputs<\/a>"],[0,1,"<a href=\"StatementBatchRollUpUsingFacilityMap.php.html#45\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\StatementBatchRollUpUsingFacilityMap::getSql<\/a>"],[0,1,"<a href=\"UnbillableBillableInstances.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBillableInstances::getCategory<\/a>"],[0,1,"<a href=\"UnbillableBillableInstances.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBillableInstances::getName<\/a>"],[0,1,"<a href=\"UnbillableBillableInstances.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBillableInstances::getDescription<\/a>"],[0,1,"<a href=\"UnbillableBillableInstances.php.html#32\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBillableInstances::getInputs<\/a>"],[0,7,"<a href=\"UnbillableBillableInstances.php.html#45\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBillableInstances::getAlerts<\/a>"],[0,1,"<a href=\"UnbillableBillableInstances.php.html#109\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBillableInstances::_isReportRunDay<\/a>"],[0,1,"<a href=\"UnbillableBillableInstances.php.html#119\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBillableInstances::getSql<\/a>"],[0,1,"<a href=\"UnbillableBookings.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBookings::getCategory<\/a>"],[0,1,"<a href=\"UnbillableBookings.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBookings::getName<\/a>"],[0,1,"<a href=\"UnbillableBookings.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBookings::getDescription<\/a>"],[0,1,"<a href=\"UnbillableBookings.php.html#32\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBookings::getInputs<\/a>"],[0,3,"<a href=\"UnbillableBookings.php.html#41\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBookings::getAlerts<\/a>"],[0,1,"<a href=\"UnbillableBookings.php.html#82\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbillableBookings::getSql<\/a>"],[0,1,"<a href=\"UnbilledBookings.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbilledBookings::getCategory<\/a>"],[0,1,"<a href=\"UnbilledBookings.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbilledBookings::getName<\/a>"],[0,1,"<a href=\"UnbilledBookings.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbilledBookings::getDescription<\/a>"],[0,1,"<a href=\"UnbilledBookings.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbilledBookings::getInputs<\/a>"],[0,1,"<a href=\"UnbilledBookings.php.html#41\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnbilledBookings::getSql<\/a>"],[0,1,"<a href=\"UnprocessedStatements.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnprocessedStatements::getCategory<\/a>"],[0,1,"<a href=\"UnprocessedStatements.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnprocessedStatements::getName<\/a>"],[0,1,"<a href=\"UnprocessedStatements.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnprocessedStatements::getDescription<\/a>"],[0,1,"<a href=\"UnprocessedStatements.php.html#31\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnprocessedStatements::getInputs<\/a>"],[0,1,"<a href=\"UnprocessedStatements.php.html#49\">Sparefoot\\PitaService\\QuickRep\\Report\\Billing\\UnprocessedStatements::getSql<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

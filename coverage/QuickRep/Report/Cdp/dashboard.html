<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickRep/Report/Cdp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Report</a></li>
         <li class="breadcrumb-item"><a href="index.html">Cdp</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BomiusCdpLog.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusCdpLog</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BomiusResultsByAccount.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusResultsByAccount</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CDPvsNonCDPRates.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cdp\CDPvsNonCDPRates</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpAutorunHealthCheck.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\CdpAutorunHealthCheck</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisabledAccounts.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisabledAccounts</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisputesByFacility.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisputesByFacility</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMatchReasons.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMatchReasons</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMissesByFacility.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMissesByFacility</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpResultsByAccount.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\CdpResultsByAccount</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CheckForErrors.php.html#7">Sparefoot\PitaService\QuickRep\Report\Cdp\CheckForErrors</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Dashboard.php.html#10">Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailConfirmationDisputes.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\EmailConfirmationDisputes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\ExportCubesmartBookings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithLowMatchRate.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cdp\FacilitiesWithLowMatchRate</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexAccounts.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\IndexAccounts</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchReasonCounts.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\MatchReasonCounts</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RecentCdpLog.php.html#7">Sparefoot\PitaService\QuickRep\Report\Cdp\RecentCdpLog</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Dashboard.php.html#10">Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="CDPvsNonCDPRates.php.html#9">Sparefoot\PitaService\QuickRep\Report\Cdp\CDPvsNonCDPRates</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="CdpAutorunHealthCheck.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\CdpAutorunHealthCheck</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="CheckForErrors.php.html#7">Sparefoot\PitaService\QuickRep\Report\Cdp\CheckForErrors</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="CdpDisabledAccounts.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisabledAccounts</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="IndexAccounts.php.html#8">Sparefoot\PitaService\QuickRep\Report\Cdp\IndexAccounts</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BomiusCdpLog.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusCdpLog::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BomiusCdpLog.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusCdpLog::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BomiusCdpLog.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusCdpLog::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BomiusCdpLog.php.html#28"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusCdpLog::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BomiusResultsByAccount.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusResultsByAccount::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BomiusResultsByAccount.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusResultsByAccount::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BomiusResultsByAccount.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusResultsByAccount::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BomiusResultsByAccount.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusResultsByAccount::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BomiusResultsByAccount.php.html#35"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\BomiusResultsByAccount::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CDPvsNonCDPRates.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CDPvsNonCDPRates::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CDPvsNonCDPRates.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CDPvsNonCDPRates::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CDPvsNonCDPRates.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CDPvsNonCDPRates::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CDPvsNonCDPRates.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CDPvsNonCDPRates::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CDPvsNonCDPRates.php.html#35"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CDPvsNonCDPRates::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CDPvsNonCDPRates.php.html#54"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CDPvsNonCDPRates::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpAutorunHealthCheck.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpAutorunHealthCheck::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpAutorunHealthCheck.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpAutorunHealthCheck::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpAutorunHealthCheck.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpAutorunHealthCheck::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpAutorunHealthCheck.php.html#27"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpAutorunHealthCheck::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpAutorunHealthCheck.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpAutorunHealthCheck::getAlerts">getAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpAutorunHealthCheck.php.html#69"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpAutorunHealthCheck::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisabledAccounts.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisabledAccounts::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisabledAccounts.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisabledAccounts::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisabledAccounts.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisabledAccounts::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisabledAccounts.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisabledAccounts::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisabledAccounts.php.html#36"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisabledAccounts::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisputesByFacility.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisputesByFacility::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisputesByFacility.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisputesByFacility::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisputesByFacility.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisputesByFacility::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpDisputesByFacility.php.html#28"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisputesByFacility::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMatchReasons.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMatchReasons::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMatchReasons.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMatchReasons::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMatchReasons.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMatchReasons::showInProduction">showInProduction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMatchReasons.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMatchReasons::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMatchReasons.php.html#36"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMatchReasons::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMatchReasons.php.html#43"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMatchReasons::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMissesByFacility.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMissesByFacility::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMissesByFacility.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMissesByFacility::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMissesByFacility.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMissesByFacility::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpMissesByFacility.php.html#28"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpMissesByFacility::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpResultsByAccount.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpResultsByAccount::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpResultsByAccount.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpResultsByAccount::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpResultsByAccount.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpResultsByAccount::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpResultsByAccount.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpResultsByAccount::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CdpResultsByAccount.php.html#35"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpResultsByAccount::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CheckForErrors.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CheckForErrors::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CheckForErrors.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CheckForErrors::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CheckForErrors.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CheckForErrors::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CheckForErrors.php.html#24"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CheckForErrors::getAlerts">getAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CheckForErrors.php.html#57"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CheckForErrors::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Dashboard.php.html#12"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Dashboard.php.html#17"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Dashboard.php.html#22"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Dashboard.php.html#29"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Dashboard.php.html#83"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::_getCdpEnabledIntegrationTypes">_getCdpEnabledIntegrationTypes</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Dashboard.php.html#98"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::getCallbackMap">getCallbackMap</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Dashboard.php.html#143"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="Dashboard.php.html#185"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailConfirmationDisputes.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\EmailConfirmationDisputes::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailConfirmationDisputes.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\EmailConfirmationDisputes::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailConfirmationDisputes.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\EmailConfirmationDisputes::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailConfirmationDisputes.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\EmailConfirmationDisputes::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EmailConfirmationDisputes.php.html#35"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\EmailConfirmationDisputes::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\ExportCubesmartBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\ExportCubesmartBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\ExportCubesmartBookings::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\ExportCubesmartBookings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportCubesmartBookings.php.html#33"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\ExportCubesmartBookings::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithLowMatchRate.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\FacilitiesWithLowMatchRate::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithLowMatchRate.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\FacilitiesWithLowMatchRate::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithLowMatchRate.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\FacilitiesWithLowMatchRate::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithLowMatchRate.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\FacilitiesWithLowMatchRate::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithLowMatchRate.php.html#44"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\FacilitiesWithLowMatchRate::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexAccounts.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\IndexAccounts::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexAccounts.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\IndexAccounts::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexAccounts.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\IndexAccounts::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexAccounts.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\IndexAccounts::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="IndexAccounts.php.html#32"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\IndexAccounts::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#35"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#134"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType::_getIntegrations">_getIntegrations</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#155"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchReasonCounts.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchReasonCounts::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchReasonCounts.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchReasonCounts::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchReasonCounts.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchReasonCounts::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchReasonCounts.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchReasonCounts::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchReasonCounts.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchReasonCounts::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RecentCdpLog.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\RecentCdpLog::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RecentCdpLog.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\RecentCdpLog::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RecentCdpLog.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\RecentCdpLog::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RecentCdpLog.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\RecentCdpLog::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="Dashboard.php.html#185"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::getSql">getSql</abbr></a></td><td class="text-right">342</td></tr>
       <tr><td><a href="MatchRateByIntegrationType.php.html#155"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\MatchRateByIntegrationType::getSql">getSql</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="CDPvsNonCDPRates.php.html#54"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CDPvsNonCDPRates::getSql">getSql</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="CdpAutorunHealthCheck.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpAutorunHealthCheck::getAlerts">getAlerts</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CheckForErrors.php.html#24"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CheckForErrors::getAlerts">getAlerts</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CdpDisabledAccounts.php.html#36"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\CdpDisabledAccounts::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="Dashboard.php.html#98"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\Dashboard::getCallbackMap">getCallbackMap</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="IndexAccounts.php.html#32"><abbr title="Sparefoot\PitaService\QuickRep\Report\Cdp\IndexAccounts::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([18,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([94,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"BomiusCdpLog.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusCdpLog<\/a>"],[0,5,"<a href=\"BomiusResultsByAccount.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusResultsByAccount<\/a>"],[0,11,"<a href=\"CDPvsNonCDPRates.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CDPvsNonCDPRates<\/a>"],[0,8,"<a href=\"CdpAutorunHealthCheck.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpAutorunHealthCheck<\/a>"],[0,6,"<a href=\"CdpDisabledAccounts.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisabledAccounts<\/a>"],[0,4,"<a href=\"CdpDisputesByFacility.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisputesByFacility<\/a>"],[0,6,"<a href=\"CdpMatchReasons.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMatchReasons<\/a>"],[0,4,"<a href=\"CdpMissesByFacility.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMissesByFacility<\/a>"],[0,5,"<a href=\"CdpResultsByAccount.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpResultsByAccount<\/a>"],[0,7,"<a href=\"CheckForErrors.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CheckForErrors<\/a>"],[0,26,"<a href=\"Dashboard.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\Dashboard<\/a>"],[0,5,"<a href=\"EmailConfirmationDisputes.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\EmailConfirmationDisputes<\/a>"],[0,5,"<a href=\"ExportCubesmartBookings.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\ExportCubesmartBookings<\/a>"],[0,5,"<a href=\"FacilitiesWithLowMatchRate.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\FacilitiesWithLowMatchRate<\/a>"],[0,6,"<a href=\"IndexAccounts.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\IndexAccounts<\/a>"],[0,13,"<a href=\"MatchRateByIntegrationType.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchRateByIntegrationType<\/a>"],[0,5,"<a href=\"MatchReasonCounts.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchReasonCounts<\/a>"],[0,4,"<a href=\"RecentCdpLog.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\RecentCdpLog<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BomiusCdpLog.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusCdpLog::getCategory<\/a>"],[0,1,"<a href=\"BomiusCdpLog.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusCdpLog::getName<\/a>"],[0,1,"<a href=\"BomiusCdpLog.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusCdpLog::getInputs<\/a>"],[0,1,"<a href=\"BomiusCdpLog.php.html#28\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusCdpLog::getSql<\/a>"],[0,1,"<a href=\"BomiusResultsByAccount.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusResultsByAccount::getCategory<\/a>"],[0,1,"<a href=\"BomiusResultsByAccount.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusResultsByAccount::getName<\/a>"],[0,1,"<a href=\"BomiusResultsByAccount.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusResultsByAccount::getDescription<\/a>"],[0,1,"<a href=\"BomiusResultsByAccount.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusResultsByAccount::getInputs<\/a>"],[0,1,"<a href=\"BomiusResultsByAccount.php.html#35\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\BomiusResultsByAccount::getSql<\/a>"],[0,1,"<a href=\"CDPvsNonCDPRates.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CDPvsNonCDPRates::getCategory<\/a>"],[0,1,"<a href=\"CDPvsNonCDPRates.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CDPvsNonCDPRates::getName<\/a>"],[0,1,"<a href=\"CDPvsNonCDPRates.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CDPvsNonCDPRates::getDescription<\/a>"],[0,1,"<a href=\"CDPvsNonCDPRates.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CDPvsNonCDPRates::getInputs<\/a>"],[0,1,"<a href=\"CDPvsNonCDPRates.php.html#35\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CDPvsNonCDPRates::getCharts<\/a>"],[0,6,"<a href=\"CDPvsNonCDPRates.php.html#54\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CDPvsNonCDPRates::getSql<\/a>"],[0,1,"<a href=\"CdpAutorunHealthCheck.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpAutorunHealthCheck::getCategory<\/a>"],[0,1,"<a href=\"CdpAutorunHealthCheck.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpAutorunHealthCheck::getName<\/a>"],[0,1,"<a href=\"CdpAutorunHealthCheck.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpAutorunHealthCheck::getDescription<\/a>"],[0,1,"<a href=\"CdpAutorunHealthCheck.php.html#27\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpAutorunHealthCheck::getInputs<\/a>"],[0,3,"<a href=\"CdpAutorunHealthCheck.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpAutorunHealthCheck::getAlerts<\/a>"],[0,1,"<a href=\"CdpAutorunHealthCheck.php.html#69\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpAutorunHealthCheck::getSql<\/a>"],[0,1,"<a href=\"CdpDisabledAccounts.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisabledAccounts::getCategory<\/a>"],[0,1,"<a href=\"CdpDisabledAccounts.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisabledAccounts::getName<\/a>"],[0,1,"<a href=\"CdpDisabledAccounts.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisabledAccounts::getDescription<\/a>"],[0,1,"<a href=\"CdpDisabledAccounts.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisabledAccounts::getInputs<\/a>"],[0,2,"<a href=\"CdpDisabledAccounts.php.html#36\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisabledAccounts::getSql<\/a>"],[0,1,"<a href=\"CdpDisputesByFacility.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisputesByFacility::getCategory<\/a>"],[0,1,"<a href=\"CdpDisputesByFacility.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisputesByFacility::getName<\/a>"],[0,1,"<a href=\"CdpDisputesByFacility.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisputesByFacility::getInputs<\/a>"],[0,1,"<a href=\"CdpDisputesByFacility.php.html#28\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpDisputesByFacility::getSql<\/a>"],[0,1,"<a href=\"CdpMatchReasons.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMatchReasons::getCategory<\/a>"],[0,1,"<a href=\"CdpMatchReasons.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMatchReasons::getName<\/a>"],[0,1,"<a href=\"CdpMatchReasons.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMatchReasons::showInProduction<\/a>"],[0,1,"<a href=\"CdpMatchReasons.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMatchReasons::getDescription<\/a>"],[0,1,"<a href=\"CdpMatchReasons.php.html#36\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMatchReasons::getInputs<\/a>"],[0,1,"<a href=\"CdpMatchReasons.php.html#43\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMatchReasons::getSql<\/a>"],[0,1,"<a href=\"CdpMissesByFacility.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMissesByFacility::getCategory<\/a>"],[0,1,"<a href=\"CdpMissesByFacility.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMissesByFacility::getName<\/a>"],[0,1,"<a href=\"CdpMissesByFacility.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMissesByFacility::getInputs<\/a>"],[0,1,"<a href=\"CdpMissesByFacility.php.html#28\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpMissesByFacility::getSql<\/a>"],[0,1,"<a href=\"CdpResultsByAccount.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpResultsByAccount::getCategory<\/a>"],[0,1,"<a href=\"CdpResultsByAccount.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpResultsByAccount::getName<\/a>"],[0,1,"<a href=\"CdpResultsByAccount.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpResultsByAccount::getDescription<\/a>"],[0,1,"<a href=\"CdpResultsByAccount.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpResultsByAccount::getInputs<\/a>"],[0,1,"<a href=\"CdpResultsByAccount.php.html#35\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CdpResultsByAccount::getSql<\/a>"],[0,1,"<a href=\"CheckForErrors.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CheckForErrors::getCategory<\/a>"],[0,1,"<a href=\"CheckForErrors.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CheckForErrors::getName<\/a>"],[0,1,"<a href=\"CheckForErrors.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CheckForErrors::getInputs<\/a>"],[0,3,"<a href=\"CheckForErrors.php.html#24\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CheckForErrors::getAlerts<\/a>"],[0,1,"<a href=\"CheckForErrors.php.html#57\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\CheckForErrors::getSql<\/a>"],[0,1,"<a href=\"Dashboard.php.html#12\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\Dashboard::getCategory<\/a>"],[0,1,"<a href=\"Dashboard.php.html#17\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\Dashboard::getName<\/a>"],[0,1,"<a href=\"Dashboard.php.html#22\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\Dashboard::getDescription<\/a>"],[0,1,"<a href=\"Dashboard.php.html#29\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\Dashboard::getInputs<\/a>"],[0,1,"<a href=\"Dashboard.php.html#83\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\Dashboard::_getCdpEnabledIntegrationTypes<\/a>"],[0,2,"<a href=\"Dashboard.php.html#98\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\Dashboard::getCallbackMap<\/a>"],[0,1,"<a href=\"Dashboard.php.html#143\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\Dashboard::getCharts<\/a>"],[0,18,"<a href=\"Dashboard.php.html#185\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\Dashboard::getSql<\/a>"],[0,1,"<a href=\"EmailConfirmationDisputes.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\EmailConfirmationDisputes::getCategory<\/a>"],[0,1,"<a href=\"EmailConfirmationDisputes.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\EmailConfirmationDisputes::getName<\/a>"],[0,1,"<a href=\"EmailConfirmationDisputes.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\EmailConfirmationDisputes::getDescription<\/a>"],[0,1,"<a href=\"EmailConfirmationDisputes.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\EmailConfirmationDisputes::getInputs<\/a>"],[0,1,"<a href=\"EmailConfirmationDisputes.php.html#35\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\EmailConfirmationDisputes::getSql<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\ExportCubesmartBookings::getCategory<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\ExportCubesmartBookings::getName<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\ExportCubesmartBookings::getDescription<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\ExportCubesmartBookings::getInputs<\/a>"],[0,1,"<a href=\"ExportCubesmartBookings.php.html#33\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\ExportCubesmartBookings::getSql<\/a>"],[0,1,"<a href=\"FacilitiesWithLowMatchRate.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\FacilitiesWithLowMatchRate::getCategory<\/a>"],[0,1,"<a href=\"FacilitiesWithLowMatchRate.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\FacilitiesWithLowMatchRate::getName<\/a>"],[0,1,"<a href=\"FacilitiesWithLowMatchRate.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\FacilitiesWithLowMatchRate::getDescription<\/a>"],[0,1,"<a href=\"FacilitiesWithLowMatchRate.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\FacilitiesWithLowMatchRate::getInputs<\/a>"],[0,1,"<a href=\"FacilitiesWithLowMatchRate.php.html#44\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\FacilitiesWithLowMatchRate::getSql<\/a>"],[0,1,"<a href=\"IndexAccounts.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\IndexAccounts::getCategory<\/a>"],[0,1,"<a href=\"IndexAccounts.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\IndexAccounts::getName<\/a>"],[0,1,"<a href=\"IndexAccounts.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\IndexAccounts::getDescription<\/a>"],[0,1,"<a href=\"IndexAccounts.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\IndexAccounts::getInputs<\/a>"],[0,2,"<a href=\"IndexAccounts.php.html#32\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\IndexAccounts::getSql<\/a>"],[0,1,"<a href=\"MatchRateByIntegrationType.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchRateByIntegrationType::getCategory<\/a>"],[0,1,"<a href=\"MatchRateByIntegrationType.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchRateByIntegrationType::getName<\/a>"],[0,1,"<a href=\"MatchRateByIntegrationType.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchRateByIntegrationType::getDescription<\/a>"],[0,1,"<a href=\"MatchRateByIntegrationType.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchRateByIntegrationType::getInputs<\/a>"],[0,1,"<a href=\"MatchRateByIntegrationType.php.html#35\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchRateByIntegrationType::getCharts<\/a>"],[0,1,"<a href=\"MatchRateByIntegrationType.php.html#134\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchRateByIntegrationType::_getIntegrations<\/a>"],[0,7,"<a href=\"MatchRateByIntegrationType.php.html#155\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchRateByIntegrationType::getSql<\/a>"],[0,1,"<a href=\"MatchReasonCounts.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchReasonCounts::getCategory<\/a>"],[0,1,"<a href=\"MatchReasonCounts.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchReasonCounts::getName<\/a>"],[0,1,"<a href=\"MatchReasonCounts.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchReasonCounts::getDescription<\/a>"],[0,1,"<a href=\"MatchReasonCounts.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchReasonCounts::getInputs<\/a>"],[0,1,"<a href=\"MatchReasonCounts.php.html#39\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\MatchReasonCounts::getSql<\/a>"],[0,1,"<a href=\"RecentCdpLog.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\RecentCdpLog::getCategory<\/a>"],[0,1,"<a href=\"RecentCdpLog.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\RecentCdpLog::getName<\/a>"],[0,1,"<a href=\"RecentCdpLog.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\RecentCdpLog::getInputs<\/a>"],[0,1,"<a href=\"RecentCdpLog.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Cdp\\RecentCdpLog::getSql<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/service/src/QuickRep/Report/PriceSwitchesTool.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="index.html">Report</a></li>
         <li class="breadcrumb-item active">PriceSwitchesTool.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;21</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;6</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="Sparefoot\PitaService\QuickRep\Report\PriceSwitchesTool">PriceSwitchesTool</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;21</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;6</div></td>
       <td class="danger small">42</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#11"><abbr title="getCategory()">getCategory</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#16"><abbr title="getName()">getName</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#21"><abbr title="showInProduction()">showInProduction</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#26"><abbr title="getDescription()">getDescription</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#34"><abbr title="getInputs()">getInputs</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;14</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#52"><abbr title="getSql(array $parameters)">getSql</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Input\TextInput</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">PriceSwitchesTool</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">Report</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getCategory</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'Account&nbsp;Management'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getName</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'Pricing&nbsp;Switches&nbsp;Tool'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">showInProduction</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getDescription</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'&lt;b&gt;This&nbsp;report&nbsp;shows&nbsp;how&nbsp;changes&nbsp;in&nbsp;residual&nbsp;and&nbsp;CPA&nbsp;affect&nbsp;ranking.&nbsp;The&nbsp;Recommended&nbsp;Bid/Residual&nbsp;is&nbsp;found&nbsp;as&nbsp;a&nbsp;function&nbsp;of&nbsp;the&nbsp;CPA&nbsp;Floor,&nbsp;Residual&nbsp;Floor,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;the&nbsp;coverage&nbsp;in&nbsp;an&nbsp;area,&nbsp;and&nbsp;the&nbsp;number&nbsp;of&nbsp;move&nbsp;ins&nbsp;for&nbsp;the&nbsp;facility&nbsp;in&nbsp;the&nbsp;past&nbsp;year.&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getInputs</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'ACCOUNT_ID'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">EntityIteratorSelectInput</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'Account'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'SfAccountID'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'nameAndIdLabel'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">\Genesis_Service_Account</span><span class="default">::</span><span class="default">load</span><span class="keyword">(</span><span class="default">\Genesis_Db_Order</span><span class="default">::</span><span class="default">asc</span><span class="keyword">(</span><span class="default">'name'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">true</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">false</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'low_cpa'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">TextInput</span><span class="keyword">(</span><span class="default">'CPA&nbsp;Floor&nbsp;($):'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'low_resid'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">TextInput</span><span class="keyword">(</span><span class="default">'Residual&nbsp;Floor&nbsp;(Example&nbsp;Format&nbsp;25%):'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getSql</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$parameters</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">&lt;&lt;&lt;SQL</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;call&nbsp;db_analytics.pricing_switches_account_level_quickrep(:ACCOUNT_ID,&nbsp;:low_cpa,&nbsp;:low_resid&nbsp;/&nbsp;100);</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">/*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;drop&nbsp;procedure&nbsp;if&nbsp;exists&nbsp;facility_rankings;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">delimiter&nbsp;||</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;procedure&nbsp;facility_rankings&nbsp;(facility_param&nbsp;int,&nbsp;new_bid&nbsp;decimal(6,2),&nbsp;new_resid&nbsp;decimal(6,2),&nbsp;city_param&nbsp;varchar(50),&nbsp;zip_param&nbsp;char(5))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">begin</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Getting&nbsp;the&nbsp;distance&nbsp;portion&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;facility_distances_and_yield_for_zip;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;facility_distances_and_yield_for_zip&nbsp;as&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;plk.distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.algorithm_distance_values&nbsp;adv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.pair_location_key_fac&nbsp;plk</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;adv.location_key&nbsp;=&nbsp;plk.location_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;ceiling(plk.distance)&nbsp;=&nbsp;adv.distance</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;adv.location_key&nbsp;=&nbsp;zip_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;facs_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;plk.facility_key&nbsp;=&nbsp;fy.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;facility_distances_and_yield_for_zip&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Getting&nbsp;Rid&nbsp;of&nbsp;Parking&nbsp;Spaces&nbsp;and&nbsp;RV&nbsp;Storage&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;facility_distances_and_yield_no_car_for_zip;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;facility_distances_and_yield_no_car_for_zip&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;max(case&nbsp;when&nbsp;avail_type_of_space_id&nbsp;not&nbsp;in&nbsp;(4,11)&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;as&nbsp;non_parking_space</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_for_zip&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_avail_space_2&nbsp;las</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;las.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;active&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;publish&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;approved&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;having&nbsp;max(case&nbsp;when&nbsp;avail_type_of_space_id&nbsp;not&nbsp;in&nbsp;(4,11)&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="default">####################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Calculating&nbsp;Current&nbsp;Search&nbsp;Ranks&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="default">####################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;current_ranking_for_zip;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;current_ranking_for_zip&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;+&nbsp;yield&nbsp;as&nbsp;score,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@curRank&nbsp;:=&nbsp;@curRank&nbsp;+&nbsp;1&nbsp;as&nbsp;current_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_no_car_for_zip&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;(select&nbsp;@curRank&nbsp;:=&nbsp;0)&nbsp;rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;value&nbsp;+&nbsp;yield&nbsp;desc</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;current_ranking_for_zip&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"><span class="default">###############################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Grabbing&nbsp;the&nbsp;ranking&nbsp;if&nbsp;the&nbsp;bid&nbsp;or&nbsp;residual&nbsp;percent&nbsp;changes&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="default">###############################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;cpa_cpa;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;cpa_cpa&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;listing_avail_id&nbsp;as&nbsp;facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;((case&nbsp;when&nbsp;avg_small_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*&nbsp;.23&nbsp;*&nbsp;.66&nbsp;+&nbsp;(case&nbsp;when&nbsp;avg_medium_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*.35&nbsp;*&nbsp;1&nbsp;+</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(case&nbsp;when&nbsp;avg_large_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*&nbsp;.42&nbsp;*&nbsp;1.66)&nbsp;*&nbsp;new_bid&nbsp;as&nbsp;cpa</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.facility_yields&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;listing_avail_id&nbsp;=&nbsp;facility_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;new_facs_yields_cpa;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;new_facs_yields_cpa</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;la.listing_avail_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(use_override_cpa,&nbsp;greatest(fy.cpa,override_cpa),&nbsp;cpa.cpa)&nbsp;as&nbsp;cpa,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_move_in_rate,&nbsp;greatest(move_in_rate,&nbsp;override_move_in_rate),move_in_rate),&nbsp;@MOVE_IN_RATE_FLOOR),@MOVE_IN_RATE_FLOOR)&nbsp;as&nbsp;mir,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_submit_rate,&nbsp;greatest(submit_rate,override_submit_rate),&nbsp;submit_rate),@SUBMIT_RATE_FLOOR),@SUBMIT_RATE_FLOOR)&nbsp;as&nbsp;sr,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(use_override_cpa,&nbsp;greatest(cpa.cpa,override_cpa),&nbsp;cpa.cpa)&nbsp;*&nbsp;ifnull(greatest(if(use_override_move_in_rate,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;greatest(move_in_rate,&nbsp;override_move_in_rate),move_in_rate),&nbsp;@MOVE_IN_RATE_FLOOR),@MOVE_IN_RATE_FLOOR)&nbsp;*&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_submit_rate,&nbsp;greatest(submit_rate,override_submit_rate),&nbsp;submit_rate),@SUBMIT_RATE_FLOOR),@SUBMIT_RATE_FLOOR)as&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;l.latitude,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;l.longitude</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_avail&nbsp;la</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(listing_avail_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;f&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;(f.facility_key=la.listing_avail_id&nbsp;and&nbsp;f.live_on_network&nbsp;and&nbsp;not&nbsp;is_directory)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.location&nbsp;l</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(location_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;cpa_cpa&nbsp;cpa</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.listing_avail_id&nbsp;=&nbsp;cpa.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">###########################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Getting&nbsp;The&nbsp;New&nbsp;Ranking&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="default">###########################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">update&nbsp;facility_distances_and_yield_no_car_for_zip&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_facs_yields_cpa&nbsp;ny</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.facility_key&nbsp;=&nbsp;ny.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;set&nbsp;fy.yield&nbsp;=&nbsp;ny.yield</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;new_ranking_for_zip_cpa;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;new_ranking_for_zip_cpa&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;+&nbsp;yield&nbsp;as&nbsp;score,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@curRank&nbsp;:=&nbsp;@curRank&nbsp;+&nbsp;1&nbsp;as&nbsp;current_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_no_car_for_zip&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;(select&nbsp;@curRank&nbsp;:=&nbsp;0)&nbsp;rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;value&nbsp;+&nbsp;yield&nbsp;desc</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;new_ranking_for_zip_cpa&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="default">################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Calculating&nbsp;the&nbsp;New&nbsp;Yeild&nbsp;for&nbsp;Residual&nbsp;Folks&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="default">################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;residual_cpa;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;residual_cpa&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;listing_avail_id&nbsp;as&nbsp;facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;round((0.23&nbsp;*&nbsp;ifnull(avg_small_unit_price,&nbsp;0)&nbsp;*&nbsp;revenue_realization_months&nbsp;+&nbsp;0.35&nbsp;*&nbsp;ifnull(avg_medium_unit_price,&nbsp;0)&nbsp;*&nbsp;revenue_realization_months&nbsp;+&nbsp;0.42&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(avg_large_unit_price,0)&nbsp;*&nbsp;revenue_realization_months)&nbsp;*&nbsp;new_resid,&nbsp;2)&nbsp;as&nbsp;cpa</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.npv_model_2014_june_refresh&nbsp;npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;hold_month&nbsp;=&nbsp;date_format(now(),'%Y-%m-01')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;listing_avail_id&nbsp;=&nbsp;facility_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;new_facs_yields_residual;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;new_facs_yields_residual</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;la.listing_avail_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(use_override_cpa,&nbsp;greatest(cpa.cpa,override_cpa),&nbsp;cpa.cpa)&nbsp;as&nbsp;cpa,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_move_in_rate,&nbsp;greatest(move_in_rate,&nbsp;override_move_in_rate),move_in_rate),&nbsp;@MOVE_IN_RATE_FLOOR),@MOVE_IN_RATE_FLOOR)&nbsp;as&nbsp;mir,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_submit_rate,&nbsp;greatest(submit_rate,override_submit_rate),&nbsp;submit_rate),@SUBMIT_RATE_FLOOR),@SUBMIT_RATE_FLOOR)&nbsp;as&nbsp;sr,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(use_override_cpa,&nbsp;greatest(cpa.cpa,override_cpa),&nbsp;cpa.cpa)&nbsp;*&nbsp;ifnull(greatest(if(use_override_move_in_rate,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;greatest(move_in_rate,&nbsp;override_move_in_rate),move_in_rate),&nbsp;@MOVE_IN_RATE_FLOOR),@MOVE_IN_RATE_FLOOR)&nbsp;*&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_submit_rate,&nbsp;greatest(submit_rate,override_submit_rate),&nbsp;submit_rate),@SUBMIT_RATE_FLOOR),@SUBMIT_RATE_FLOOR)as&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;l.latitude,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;l.longitude</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_avail&nbsp;la</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(listing_avail_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;f&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;(f.facility_key=la.listing_avail_id&nbsp;and&nbsp;f.live_on_network&nbsp;and&nbsp;not&nbsp;is_directory)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.location&nbsp;l</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="240" href="#240">240</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(location_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="241" href="#241">241</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;residual_cpa&nbsp;cpa</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="242" href="#242">242</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.listing_avail_id&nbsp;=&nbsp;cpa.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="243" href="#243">243</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="244" href="#244">244</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="245" href="#245">245</a></td><td class="col-11 codeLine"><span class="default">#############################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="246" href="#246">246</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Getting&nbsp;New&nbsp;Residual&nbsp;Rank&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="247" href="#247">247</a></td><td class="col-11 codeLine"><span class="default">#############################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="248" href="#248">248</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="249" href="#249">249</a></td><td class="col-11 codeLine"><span class="default">update&nbsp;facility_distances_and_yield_no_car_for_zip&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="250" href="#250">250</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_facs_yields_residual&nbsp;ny</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="251" href="#251">251</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.facility_key&nbsp;=&nbsp;ny.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="252" href="#252">252</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;set&nbsp;fy.yield&nbsp;=&nbsp;ny.yield</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="253" href="#253">253</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="254" href="#254">254</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="255" href="#255">255</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;new_ranking_for_zip_residual;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="256" href="#256">256</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;new_ranking_for_zip_residual&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="257" href="#257">257</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="258" href="#258">258</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="259" href="#259">259</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="260" href="#260">260</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="261" href="#261">261</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="262" href="#262">262</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;+&nbsp;yield&nbsp;as&nbsp;score,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="263" href="#263">263</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@curRank&nbsp;:=&nbsp;@curRank&nbsp;+&nbsp;1&nbsp;as&nbsp;current_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="264" href="#264">264</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_no_car_for_zip&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="265" href="#265">265</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="266" href="#266">266</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="267" href="#267">267</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;(select&nbsp;@curRank&nbsp;:=&nbsp;0)&nbsp;rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="268" href="#268">268</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;value&nbsp;+&nbsp;yield&nbsp;desc</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="269" href="#269">269</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="270" href="#270">270</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="271" href="#271">271</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="272" href="#272">272</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;new_ranking_for_zip_residual&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="273" href="#273">273</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="274" href="#274">274</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="275" href="#275">275</a></td><td class="col-11 codeLine"><span class="default">#####################################################################################################################################################################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="276" href="#276">276</a></td><td class="col-11 codeLine"><span class="default">#####################################################################################################################################################################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="277" href="#277">277</a></td><td class="col-11 codeLine"><span class="default">##&nbsp;Getting&nbsp;This&nbsp;All&nbsp;on&nbsp;the&nbsp;City&nbsp;Level&nbsp;###############################################################################################################################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="278" href="#278">278</a></td><td class="col-11 codeLine"><span class="default">#####################################################################################################################################################################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="279" href="#279">279</a></td><td class="col-11 codeLine"><span class="default">#####################################################################################################################################################################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="280" href="#280">280</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="281" href="#281">281</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;facility_distances_and_yield_for_city;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="282" href="#282">282</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;facility_distances_and_yield_for_city&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="283" href="#283">283</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="284" href="#284">284</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="285" href="#285">285</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;plk.distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="286" href="#286">286</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="287" href="#287">287</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="288" href="#288">288</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.algorithm_distance_values&nbsp;adv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="289" href="#289">289</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.pair_location_key_fac&nbsp;plk</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="290" href="#290">290</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;adv.location_key&nbsp;=&nbsp;plk.location_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="291" href="#291">291</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;ceiling(plk.distance)&nbsp;=&nbsp;adv.distance</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="292" href="#292">292</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;adv.location_key&nbsp;=&nbsp;city_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="293" href="#293">293</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;facs_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="294" href="#294">294</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;plk.facility_key&nbsp;=&nbsp;fy.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="295" href="#295">295</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="296" href="#296">296</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="297" href="#297">297</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="298" href="#298">298</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;facility_distances_and_yield_for_city&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="299" href="#299">299</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="300" href="#300">300</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="301" href="#301">301</a></td><td class="col-11 codeLine"><span class="default">################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="302" href="#302">302</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Getting&nbsp;Rid&nbsp;of&nbsp;Parking&nbsp;Spaces&nbsp;and&nbsp;RV&nbsp;Storage&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="303" href="#303">303</a></td><td class="col-11 codeLine"><span class="default">################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="304" href="#304">304</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="305" href="#305">305</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;facility_distances_and_yield_no_car_for_city;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="306" href="#306">306</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;facility_distances_and_yield_no_car_for_city&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="307" href="#307">307</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="308" href="#308">308</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="309" href="#309">309</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="310" href="#310">310</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="311" href="#311">311</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="312" href="#312">312</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;max(case&nbsp;when&nbsp;avail_type_of_space_id&nbsp;not&nbsp;in&nbsp;(4,11)&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;as&nbsp;non_parking_space</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="313" href="#313">313</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_for_city&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="314" href="#314">314</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_avail_space_2&nbsp;las</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="315" href="#315">315</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;las.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="316" href="#316">316</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;active&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="317" href="#317">317</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;publish&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="318" href="#318">318</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;approved&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="319" href="#319">319</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="320" href="#320">320</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;having&nbsp;max(case&nbsp;when&nbsp;avail_type_of_space_id&nbsp;not&nbsp;in&nbsp;(4,11)&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="321" href="#321">321</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="322" href="#322">322</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="323" href="#323">323</a></td><td class="col-11 codeLine"><span class="default">####################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="324" href="#324">324</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Calculating&nbsp;Current&nbsp;Search&nbsp;Ranks&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="325" href="#325">325</a></td><td class="col-11 codeLine"><span class="default">####################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="326" href="#326">326</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="327" href="#327">327</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;current_ranking_for_city;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="328" href="#328">328</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;current_ranking_for_city&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="329" href="#329">329</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="330" href="#330">330</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="331" href="#331">331</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="332" href="#332">332</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="333" href="#333">333</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="334" href="#334">334</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="335" href="#335">335</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;+&nbsp;yield&nbsp;as&nbsp;score,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="336" href="#336">336</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@curRank&nbsp;:=&nbsp;@curRank&nbsp;+&nbsp;1&nbsp;as&nbsp;current_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="337" href="#337">337</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_no_car_for_city&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="338" href="#338">338</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="339" href="#339">339</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="340" href="#340">340</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;(select&nbsp;@curRank&nbsp;:=&nbsp;0)&nbsp;rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="341" href="#341">341</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;value&nbsp;+&nbsp;yield&nbsp;desc</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="342" href="#342">342</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="343" href="#343">343</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="344" href="#344">344</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="345" href="#345">345</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;current_ranking_for_city&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="346" href="#346">346</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="347" href="#347">347</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="348" href="#348">348</a></td><td class="col-11 codeLine"><span class="default">###########################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="349" href="#349">349</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Getting&nbsp;The&nbsp;New&nbsp;Ranking&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="350" href="#350">350</a></td><td class="col-11 codeLine"><span class="default">###########################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="351" href="#351">351</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="352" href="#352">352</a></td><td class="col-11 codeLine"><span class="default">update&nbsp;facility_distances_and_yield_no_car_for_city&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="353" href="#353">353</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_facs_yields_cpa&nbsp;ny</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="354" href="#354">354</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.facility_key&nbsp;=&nbsp;ny.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="355" href="#355">355</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;set&nbsp;fy.yield&nbsp;=&nbsp;ny.yield</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="356" href="#356">356</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="357" href="#357">357</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="358" href="#358">358</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;new_ranking_for_city_cpa;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="359" href="#359">359</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;new_ranking_for_city_cpa&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="360" href="#360">360</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="361" href="#361">361</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="362" href="#362">362</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="363" href="#363">363</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="364" href="#364">364</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="365" href="#365">365</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;+&nbsp;yield&nbsp;as&nbsp;score,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="366" href="#366">366</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@curRank&nbsp;:=&nbsp;@curRank&nbsp;+&nbsp;1&nbsp;as&nbsp;current_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="367" href="#367">367</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_no_car_for_city&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="368" href="#368">368</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="369" href="#369">369</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="370" href="#370">370</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;(select&nbsp;@curRank&nbsp;:=&nbsp;0)&nbsp;rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="371" href="#371">371</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;value&nbsp;+&nbsp;yield&nbsp;desc</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="372" href="#372">372</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="373" href="#373">373</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="374" href="#374">374</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="375" href="#375">375</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;new_ranking_for_city_cpa&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="376" href="#376">376</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="377" href="#377">377</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="378" href="#378">378</a></td><td class="col-11 codeLine"><span class="default">################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="379" href="#379">379</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Calculating&nbsp;the&nbsp;New&nbsp;Yeild&nbsp;for&nbsp;Residual&nbsp;Folks&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="380" href="#380">380</a></td><td class="col-11 codeLine"><span class="default">################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="381" href="#381">381</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="382" href="#382">382</a></td><td class="col-11 codeLine"><span class="default">#############################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="383" href="#383">383</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Getting&nbsp;New&nbsp;Residual&nbsp;Rank&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="384" href="#384">384</a></td><td class="col-11 codeLine"><span class="default">#############################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="385" href="#385">385</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="386" href="#386">386</a></td><td class="col-11 codeLine"><span class="default">update&nbsp;facility_distances_and_yield_no_car_for_city&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="387" href="#387">387</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_facs_yields_residual&nbsp;ny</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="388" href="#388">388</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.facility_key&nbsp;=&nbsp;ny.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="389" href="#389">389</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;set&nbsp;fy.yield&nbsp;=&nbsp;ny.yield</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="390" href="#390">390</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="391" href="#391">391</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="392" href="#392">392</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;new_ranking_for_city_residual;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="393" href="#393">393</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;new_ranking_for_city_residual&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="394" href="#394">394</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="395" href="#395">395</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="396" href="#396">396</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="397" href="#397">397</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="398" href="#398">398</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="399" href="#399">399</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;+&nbsp;yield&nbsp;as&nbsp;score,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="400" href="#400">400</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@curRank&nbsp;:=&nbsp;@curRank&nbsp;+&nbsp;1&nbsp;as&nbsp;current_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="401" href="#401">401</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_no_car_for_city&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="402" href="#402">402</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="403" href="#403">403</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="404" href="#404">404</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;(select&nbsp;@curRank&nbsp;:=&nbsp;0)&nbsp;rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="405" href="#405">405</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;value&nbsp;+&nbsp;yield&nbsp;desc</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="406" href="#406">406</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="407" href="#407">407</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="408" href="#408">408</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="409" href="#409">409</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;new_ranking_for_city_residual&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="410" href="#410">410</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="411" href="#411">411</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="412" href="#412">412</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;zip_rankings_current_and_new;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="413" href="#413">413</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;zip_rankings_current_and_new&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="414" href="#414">414</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="415" href="#415">415</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;n.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="416" href="#416">416</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;n.current_rank&nbsp;as&nbsp;current_zip_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="417" href="#417">417</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;c.current_rank&nbsp;as&nbsp;new_cpa_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="418" href="#418">418</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;r.current_rank&nbsp;as&nbsp;new_residual_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="419" href="#419">419</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;current_ranking_for_zip&nbsp;n</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="420" href="#420">420</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_ranking_for_zip_cpa&nbsp;c</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="421" href="#421">421</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;n.facility_key&nbsp;=&nbsp;c.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="422" href="#422">422</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;n.facility_key&nbsp;=&nbsp;facility_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="423" href="#423">423</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_ranking_for_zip_residual&nbsp;r</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="424" href="#424">424</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;n.facility_key&nbsp;=&nbsp;r.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="425" href="#425">425</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="426" href="#426">426</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="427" href="#427">427</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;city_rankings_current_and_new;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="428" href="#428">428</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;city_rankings_current_and_new&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="429" href="#429">429</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="430" href="#430">430</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;n.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="431" href="#431">431</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;n.current_rank&nbsp;as&nbsp;current_city_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="432" href="#432">432</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;c.current_rank&nbsp;as&nbsp;new_cpa_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="433" href="#433">433</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;r.current_rank&nbsp;as&nbsp;new_residual_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="434" href="#434">434</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;current_ranking_for_city&nbsp;n</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="435" href="#435">435</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_ranking_for_city_cpa&nbsp;c</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="436" href="#436">436</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;n.facility_key&nbsp;=&nbsp;c.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="437" href="#437">437</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;n.facility_key&nbsp;=&nbsp;facility_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="438" href="#438">438</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_ranking_for_city_residual&nbsp;r</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="439" href="#439">439</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;n.facility_key&nbsp;=&nbsp;r.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="440" href="#440">440</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="441" href="#441">441</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="442" href="#442">442</a></td><td class="col-11 codeLine"><span class="default">####################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="443" href="#443">443</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Grabbing&nbsp;Rank&nbsp;for&nbsp;Recomended&nbsp;Bid&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="444" href="#444">444</a></td><td class="col-11 codeLine"><span class="default">####################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="445" href="#445">445</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="446" href="#446">446</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;recomended_bid_cpa;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="447" href="#447">447</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;recomended_bid_cpa&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="448" href="#448">448</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="449" href="#449">449</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="450" href="#450">450</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;pricing_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="451" href="#451">451</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;case&nbsp;when&nbsp;pricing_key&nbsp;=&nbsp;'CPA_tiered'&nbsp;then&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="452" href="#452">452</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;((case&nbsp;when&nbsp;avg_small_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*&nbsp;.23&nbsp;*&nbsp;.66&nbsp;+&nbsp;(case&nbsp;when&nbsp;avg_medium_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*.35&nbsp;*&nbsp;1&nbsp;+</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="453" href="#453">453</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(case&nbsp;when&nbsp;avg_large_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*&nbsp;.42&nbsp;*&nbsp;1.66)&nbsp;*&nbsp;recomended_bid&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="454" href="#454">454</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when&nbsp;pricing_key&nbsp;=&nbsp;'residual'&nbsp;then&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="455" href="#455">455</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;round((0.23&nbsp;*&nbsp;ifnull(avg_small_unit_price,&nbsp;0)&nbsp;*&nbsp;revenue_realization_months&nbsp;+&nbsp;0.35&nbsp;*&nbsp;ifnull(avg_medium_unit_price,&nbsp;0)&nbsp;*&nbsp;revenue_realization_months&nbsp;+&nbsp;0.42&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="456" href="#456">456</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(avg_large_unit_price,0)&nbsp;*&nbsp;revenue_realization_months)&nbsp;*&nbsp;recomended_bid,&nbsp;2)&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="457" href="#457">457</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;end&nbsp;as&nbsp;cpa</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="458" href="#458">458</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="459" href="#459">459</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;suggested_bids&nbsp;sb</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="460" href="#460">460</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.listing_avail_id&nbsp;=&nbsp;sb.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="461" href="#461">461</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.npv_model_2014_june_refresh&nbsp;npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="462" href="#462">462</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;hold_month&nbsp;=&nbsp;date_format(now(),'%Y-%m-01')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="463" href="#463">463</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="464" href="#464">464</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="465" href="#465">465</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;new_facs_yields_recomended;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="466" href="#466">466</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;new_facs_yields_recomended</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="467" href="#467">467</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="468" href="#468">468</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;la.listing_avail_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="469" href="#469">469</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(use_override_cpa,&nbsp;greatest(cpa.cpa,override_cpa),&nbsp;cpa.cpa)&nbsp;*&nbsp;ifnull(greatest(if(use_override_move_in_rate,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="470" href="#470">470</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;greatest(move_in_rate,&nbsp;override_move_in_rate),move_in_rate),&nbsp;@MOVE_IN_RATE_FLOOR),@MOVE_IN_RATE_FLOOR)&nbsp;*&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="471" href="#471">471</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_submit_rate,&nbsp;greatest(submit_rate,override_submit_rate),&nbsp;submit_rate),@SUBMIT_RATE_FLOOR),@SUBMIT_RATE_FLOOR)as&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="472" href="#472">472</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;l.latitude,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="473" href="#473">473</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;l.longitude</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="474" href="#474">474</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="475" href="#475">475</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_avail&nbsp;la</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="476" href="#476">476</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(listing_avail_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="477" href="#477">477</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;f&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="478" href="#478">478</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;(f.facility_key=la.listing_avail_id&nbsp;and&nbsp;f.live_on_network&nbsp;and&nbsp;not&nbsp;is_directory)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="479" href="#479">479</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.location&nbsp;l</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="480" href="#480">480</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(location_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="481" href="#481">481</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;recomended_bid_cpa&nbsp;cpa</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="482" href="#482">482</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.listing_avail_id&nbsp;=&nbsp;cpa.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="483" href="#483">483</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="484" href="#484">484</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="485" href="#485">485</a></td><td class="col-11 codeLine"><span class="default">######################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="486" href="#486">486</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Getting&nbsp;Recomended&nbsp;Ranking&nbsp;for&nbsp;Zip&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="487" href="#487">487</a></td><td class="col-11 codeLine"><span class="default">######################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="488" href="#488">488</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="489" href="#489">489</a></td><td class="col-11 codeLine"><span class="default">update&nbsp;facility_distances_and_yield_no_car_for_zip&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="490" href="#490">490</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_facs_yields_recomended&nbsp;ny</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="491" href="#491">491</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.facility_key&nbsp;=&nbsp;ny.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="492" href="#492">492</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;fy.facility_key&nbsp;=&nbsp;facility_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="493" href="#493">493</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;set&nbsp;fy.yield&nbsp;=&nbsp;ny.yield</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="494" href="#494">494</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="495" href="#495">495</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="496" href="#496">496</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;recomended_ranking_for_zip;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="497" href="#497">497</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;recomended_ranking_for_zip&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="498" href="#498">498</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="499" href="#499">499</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="500" href="#500">500</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="501" href="#501">501</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="502" href="#502">502</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="503" href="#503">503</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;+&nbsp;yield&nbsp;as&nbsp;score,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="504" href="#504">504</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@curRank&nbsp;:=&nbsp;@curRank&nbsp;+&nbsp;1&nbsp;as&nbsp;current_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="505" href="#505">505</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_no_car_for_zip&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="506" href="#506">506</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="507" href="#507">507</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="508" href="#508">508</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;(select&nbsp;@curRank&nbsp;:=&nbsp;0)&nbsp;rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="509" href="#509">509</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;value&nbsp;+&nbsp;yield&nbsp;desc</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="510" href="#510">510</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="511" href="#511">511</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="512" href="#512">512</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="513" href="#513">513</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;recomended_ranking_for_zip&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="514" href="#514">514</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="515" href="#515">515</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="516" href="#516">516</a></td><td class="col-11 codeLine"><span class="default">#######################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="517" href="#517">517</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Getting&nbsp;Recomended&nbsp;Ranking&nbsp;for&nbsp;City&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="518" href="#518">518</a></td><td class="col-11 codeLine"><span class="default">#######################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="519" href="#519">519</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="520" href="#520">520</a></td><td class="col-11 codeLine"><span class="default">update&nbsp;facility_distances_and_yield_no_car_for_city&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="521" href="#521">521</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;new_facs_yields_recomended&nbsp;ny</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="522" href="#522">522</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;fy.facility_key&nbsp;=&nbsp;ny.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="523" href="#523">523</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;fy.facility_key&nbsp;=&nbsp;facility_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="524" href="#524">524</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;set&nbsp;fy.yield&nbsp;=&nbsp;ny.yield</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="525" href="#525">525</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="526" href="#526">526</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="527" href="#527">527</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;recomended_ranking_for_city;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="528" href="#528">528</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;recomended_ranking_for_city&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="529" href="#529">529</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="530" href="#530">530</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="531" href="#531">531</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="532" href="#532">532</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="533" href="#533">533</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;distance,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="534" href="#534">534</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value&nbsp;+&nbsp;yield&nbsp;as&nbsp;score,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="535" href="#535">535</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;@curRank&nbsp;:=&nbsp;@curRank&nbsp;+&nbsp;1&nbsp;as&nbsp;current_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="536" href="#536">536</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;facility_distances_and_yield_no_car_for_city&nbsp;dy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="537" href="#537">537</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="538" href="#538">538</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;dy.facility_key&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="539" href="#539">539</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;(select&nbsp;@curRank&nbsp;:=&nbsp;0)&nbsp;rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="540" href="#540">540</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;value&nbsp;+&nbsp;yield&nbsp;desc</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="541" href="#541">541</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="542" href="#542">542</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="543" href="#543">543</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="544" href="#544">544</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;recomended_ranking_for_city&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="545" href="#545">545</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="546" href="#546">546</a></td><td class="col-11 codeLine"><span class="default">##############################################################################################################################################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="547" href="#547">547</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Next&nbsp;I&nbsp;need&nbsp;to&nbsp;answer&nbsp;the&nbsp;question:&nbsp;&quot;What's&nbsp;the&nbsp;lowest&nbsp;bid&nbsp;on&nbsp;the&nbsp;facility's&nbsp;alternate&nbsp;pricing&nbsp;model&nbsp;where&nbsp;they&nbsp;can&nbsp;maintain&nbsp;their&nbsp;current&nbsp;rank&nbsp;in&nbsp;both&nbsp;the&nbsp;city&nbsp;and&nbsp;zip?&quot;&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="548" href="#548">548</a></td><td class="col-11 codeLine"><span class="default">##############################################################################################################################################################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="549" href="#549">549</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="550" href="#550">550</a></td><td class="col-11 codeLine"><span class="default">######################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="551" href="#551">551</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Grabbing&nbsp;the&nbsp;Zip&nbsp;Level&nbsp;Minimum&nbsp;Bid&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="552" href="#552">552</a></td><td class="col-11 codeLine"><span class="default">######################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="553" href="#553">553</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="554" href="#554">554</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;zip_ranking_to_beat;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="555" href="#555">555</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;zip_ranking_to_beat&nbsp;as&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="556" href="#556">556</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="557" href="#557">557</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="558" href="#558">558</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="559" href="#559">559</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;current_rank&nbsp;+&nbsp;1&nbsp;as&nbsp;rank_to_beat</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="560" href="#560">560</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;current_ranking_for_zip</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="561" href="#561">561</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;facility_key&nbsp;=&nbsp;facility_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="562" href="#562">562</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="563" href="#563">563</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="564" href="#564">564</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;zip_min_yield;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="565" href="#565">565</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;zip_min_yield&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="566" href="#566">566</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="567" href="#567">567</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_param&nbsp;as&nbsp;listing_avail_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="568" href="#568">568</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;score&nbsp;as&nbsp;score_to_beat,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="569" href="#569">569</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;zr.score&nbsp;-&nbsp;tb.value&nbsp;as&nbsp;yield_needed</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="570" href="#570">570</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;current_ranking_for_zip&nbsp;zr</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="571" href="#571">571</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;zip_ranking_to_beat&nbsp;tb</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="572" href="#572">572</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;zr.current_rank&nbsp;=&nbsp;rank_to_beat</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="573" href="#573">573</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="574" href="#574">574</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="575" href="#575">575</a></td><td class="col-11 codeLine"><span class="default">##########################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="576" href="#576">576</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Figuring&nbsp;out&nbsp;what&nbsp;bid/residual&nbsp;this&nbsp;is&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="577" href="#577">577</a></td><td class="col-11 codeLine"><span class="default">##########################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="578" href="#578">578</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="579" href="#579">579</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;cpa_needed_to_maintain_rank_in_zip;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="580" href="#580">580</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;cpa_needed_to_maintain_rank_in_zip&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="581" href="#581">581</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="582" href="#582">582</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;my.listing_avail_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="583" href="#583">583</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield_needed/&nbsp;(ifnull(greatest(if(use_override_move_in_rate,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="584" href="#584">584</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;greatest(move_in_rate,&nbsp;override_move_in_rate),move_in_rate),&nbsp;@MOVE_IN_RATE_FLOOR),@MOVE_IN_RATE_FLOOR)&nbsp;*&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="585" href="#585">585</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_submit_rate,&nbsp;greatest(submit_rate,override_submit_rate),&nbsp;submit_rate),@SUBMIT_RATE_FLOOR),@SUBMIT_RATE_FLOOR))&nbsp;as&nbsp;CPA_needed</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="586" href="#586">586</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;zip_min_yield&nbsp;my</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="587" href="#587">587</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="588" href="#588">588</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;my.listing_avail_id&nbsp;=&nbsp;fy.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="589" href="#589">589</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="590" href="#590">590</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="591" href="#591">591</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;bid_or_residual_needed_to_maintain_rank_in_zip;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="592" href="#592">592</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;bid_or_residual_needed_to_maintain_rank_in_zip&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="593" href="#593">593</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="594" href="#594">594</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="595" href="#595">595</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;case&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="596" href="#596">596</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when&nbsp;pricing_key&nbsp;=&nbsp;'residual'&nbsp;then&nbsp;ceiling(CPA_needed&nbsp;/&nbsp;((case&nbsp;when&nbsp;avg_small_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*&nbsp;.23&nbsp;*&nbsp;.66&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="597" href="#597">597</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;+&nbsp;(case&nbsp;when&nbsp;avg_medium_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*.35&nbsp;*&nbsp;1&nbsp;+&nbsp;(case&nbsp;when&nbsp;avg_large_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*&nbsp;.42&nbsp;*&nbsp;1.66))&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="598" href="#598">598</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when&nbsp;pricing_key&nbsp;=&nbsp;'CPA_TIERED'&nbsp;then&nbsp;round(ceiling(cpa_needed&nbsp;*&nbsp;100&nbsp;/(0.23&nbsp;*&nbsp;ifnull(avg_small_unit_price,&nbsp;0)&nbsp;*&nbsp;revenue_realization_months&nbsp;+&nbsp;0.35&nbsp;*&nbsp;ifnull(avg_medium_unit_price,&nbsp;0)&nbsp;*&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="599" href="#599">599</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revenue_realization_months&nbsp;+&nbsp;0.42&nbsp;*&nbsp;ifnull(avg_large_unit_price,0)*&nbsp;revenue_realization_months))&nbsp;/&nbsp;100,2)&nbsp;end&nbsp;as&nbsp;min_bid_to_retain_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="600" href="#600">600</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;cpa_needed_to_maintain_rank_in_zip&nbsp;mr</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="601" href="#601">601</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="602" href="#602">602</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;mr.listing_avail_id&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="603" href="#603">603</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="604" href="#604">604</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;df.facility_key&nbsp;=&nbsp;fy.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="605" href="#605">605</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.npv_model_2014_june_refresh&nbsp;npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="606" href="#606">606</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;hold_month&nbsp;=&nbsp;date_format(now(),'%Y-%m-01')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="607" href="#607">607</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="608" href="#608">608</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="609" href="#609">609</a></td><td class="col-11 codeLine"><span class="default">##############################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="610" href="#610">610</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Now&nbsp;the&nbsp;city&nbsp;level&nbsp;min&nbsp;bid&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="611" href="#611">611</a></td><td class="col-11 codeLine"><span class="default">##############################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="612" href="#612">612</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="613" href="#613">613</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;city_ranking_to_beat;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="614" href="#614">614</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;city_ranking_to_beat&nbsp;as&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="615" href="#615">615</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="616" href="#616">616</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="617" href="#617">617</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;value,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="618" href="#618">618</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;current_rank&nbsp;+&nbsp;1&nbsp;as&nbsp;rank_to_beat</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="619" href="#619">619</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;current_ranking_for_city</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="620" href="#620">620</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;facility_key&nbsp;=&nbsp;facility_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="621" href="#621">621</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="622" href="#622">622</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="623" href="#623">623</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;city_min_yield;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="624" href="#624">624</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;city_min_yield&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="625" href="#625">625</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="626" href="#626">626</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_param&nbsp;as&nbsp;listing_avail_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="627" href="#627">627</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;score&nbsp;as&nbsp;score_to_beat,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="628" href="#628">628</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;zr.score&nbsp;-&nbsp;tb.value&nbsp;as&nbsp;yield_needed</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="629" href="#629">629</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;current_ranking_for_city&nbsp;zr</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="630" href="#630">630</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;city_ranking_to_beat&nbsp;tb</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="631" href="#631">631</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;zr.current_rank&nbsp;=&nbsp;rank_to_beat</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="632" href="#632">632</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="633" href="#633">633</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="634" href="#634">634</a></td><td class="col-11 codeLine"><span class="default">##########################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="635" href="#635">635</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Figuring&nbsp;out&nbsp;what&nbsp;bid/residual&nbsp;this&nbsp;is&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="636" href="#636">636</a></td><td class="col-11 codeLine"><span class="default">##########################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="637" href="#637">637</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="638" href="#638">638</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;cpa_needed_to_maintain_rank_in_city;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="639" href="#639">639</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;cpa_needed_to_maintain_rank_in_city&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="640" href="#640">640</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="641" href="#641">641</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;my.listing_avail_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="642" href="#642">642</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;yield_needed/&nbsp;(ifnull(greatest(if(use_override_move_in_rate,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="643" href="#643">643</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;greatest(move_in_rate,&nbsp;override_move_in_rate),move_in_rate),&nbsp;@MOVE_IN_RATE_FLOOR),@MOVE_IN_RATE_FLOOR)&nbsp;*&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="644" href="#644">644</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_submit_rate,&nbsp;greatest(submit_rate,override_submit_rate),&nbsp;submit_rate),@SUBMIT_RATE_FLOOR),@SUBMIT_RATE_FLOOR))&nbsp;as&nbsp;CPA_needed</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="645" href="#645">645</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;city_min_yield&nbsp;my</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="646" href="#646">646</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="647" href="#647">647</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;my.listing_avail_id&nbsp;=&nbsp;fy.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="648" href="#648">648</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="649" href="#649">649</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="650" href="#650">650</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;bid_or_residual_needed_to_maintain_rank_in_city;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="651" href="#651">651</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;bid_or_residual_needed_to_maintain_rank_in_city&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="652" href="#652">652</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="653" href="#653">653</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="654" href="#654">654</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;case&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="655" href="#655">655</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when&nbsp;pricing_key&nbsp;=&nbsp;'residual'&nbsp;then&nbsp;ceiling(CPA_needed&nbsp;/&nbsp;((case&nbsp;when&nbsp;avg_small_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*&nbsp;.23&nbsp;*&nbsp;.66&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="656" href="#656">656</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;+&nbsp;(case&nbsp;when&nbsp;avg_medium_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*.35&nbsp;*&nbsp;1&nbsp;+&nbsp;(case&nbsp;when&nbsp;avg_large_unit_price&nbsp;is&nbsp;not&nbsp;null&nbsp;then&nbsp;1&nbsp;else&nbsp;0&nbsp;end)&nbsp;*&nbsp;.42&nbsp;*&nbsp;1.66))&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="657" href="#657">657</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when&nbsp;pricing_key&nbsp;=&nbsp;'CPA_TIERED'&nbsp;then&nbsp;round(ceiling(cpa_needed&nbsp;*&nbsp;100&nbsp;/(0.23&nbsp;*&nbsp;ifnull(avg_small_unit_price,&nbsp;0)&nbsp;*&nbsp;revenue_realization_months&nbsp;+&nbsp;0.35&nbsp;*&nbsp;ifnull(avg_medium_unit_price,&nbsp;0)&nbsp;*&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="658" href="#658">658</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revenue_realization_months&nbsp;+&nbsp;0.42&nbsp;*&nbsp;ifnull(avg_large_unit_price,0)*&nbsp;revenue_realization_months))&nbsp;/&nbsp;100,2)&nbsp;end&nbsp;as&nbsp;min_bid_to_retain_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="659" href="#659">659</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;cpa_needed_to_maintain_rank_in_city&nbsp;mr</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="660" href="#660">660</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="661" href="#661">661</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;mr.listing_avail_id&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="662" href="#662">662</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="663" href="#663">663</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;df.facility_key&nbsp;=&nbsp;fy.listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="664" href="#664">664</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.npv_model_2014_june_refresh&nbsp;npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="665" href="#665">665</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;hold_month&nbsp;=&nbsp;date_format(now(),'%Y-%m-01')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="666" href="#666">666</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="667" href="#667">667</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="668" href="#668">668</a></td><td class="col-11 codeLine"><span class="default">update&nbsp;pricing_switches_quickrep_table&nbsp;qt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="669" href="#669">669</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;zip_rankings_current_and_new&nbsp;zr</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="670" href="#670">670</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;qt.facility_key&nbsp;=&nbsp;zr.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="671" href="#671">671</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;city_rankings_current_and_new&nbsp;cr</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="672" href="#672">672</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;qt.facility_key&nbsp;=&nbsp;cr.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="673" href="#673">673</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;bid_or_residual_needed_to_maintain_rank_in_zip&nbsp;mz</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="674" href="#674">674</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;qt.facility_key&nbsp;=&nbsp;mz.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="675" href="#675">675</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;bid_or_residual_needed_to_maintain_rank_in_city&nbsp;cz</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="676" href="#676">676</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;qt.facility_key&nbsp;=&nbsp;cz.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="677" href="#677">677</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;recomended_ranking_for_zip&nbsp;rrz</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="678" href="#678">678</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;qt.facility_key&nbsp;=&nbsp;rrz.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="679" href="#679">679</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;recomended_ranking_for_city&nbsp;rrc</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="680" href="#680">680</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;qt.facility_key&nbsp;=&nbsp;rrc.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="681" href="#681">681</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;suggested_bids&nbsp;sb</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="682" href="#682">682</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;qt.facility_key&nbsp;=&nbsp;sb.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="683" href="#683">683</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;set&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="684" href="#684">684</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.current_zip_rank&nbsp;=&nbsp;zr.current_zip_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="685" href="#685">685</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.current_city_rank&nbsp;=&nbsp;cr.current_city_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="686" href="#686">686</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.low_cpa_rank_zip&nbsp;=&nbsp;zr.new_cpa_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="687" href="#687">687</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.low_cpa_rank_city&nbsp;=&nbsp;cr.new_cpa_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="688" href="#688">688</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.low_resid_rank_zip&nbsp;=&nbsp;zr.new_residual_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="689" href="#689">689</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.low_resid_rank_city&nbsp;=&nbsp;cr.new_residual_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="690" href="#690">690</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.maintain_rank&nbsp;=&nbsp;greatest(mz.min_bid_to_retain_rank,&nbsp;cz.min_bid_to_retain_rank),</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="691" href="#691">691</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.recomended_bid&nbsp;=&nbsp;sb.recomended_bid,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="692" href="#692">692</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.recomended_rank_zip&nbsp;=&nbsp;rrz.current_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="693" href="#693">693</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;qt.recomended_rank_city&nbsp;=&nbsp;rrc.current_rank</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="694" href="#694">694</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="695" href="#695">695</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="696" href="#696">696</a></td><td class="col-11 codeLine"><span class="default">end&nbsp;||</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="697" href="#697">697</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="698" href="#698">698</a></td><td class="col-11 codeLine"><span class="default">delimiter&nbsp;;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="699" href="#699">699</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="700" href="#700">700</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;procedure&nbsp;if&nbsp;exists&nbsp;pricing_switches_quickrep_cursor_procedure;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="701" href="#701">701</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="702" href="#702">702</a></td><td class="col-11 codeLine"><span class="default">delimiter&nbsp;||</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="703" href="#703">703</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="704" href="#704">704</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;procedure&nbsp;pricing_switches_quickrep_cursor_procedure(new_bid&nbsp;decimal(6,2),&nbsp;new_resid&nbsp;decimal(6,2))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="705" href="#705">705</a></td><td class="col-11 codeLine"><span class="default">begin</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="706" href="#706">706</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;declare&nbsp;no_more_rows&nbsp;boolean&nbsp;default&nbsp;false;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="707" href="#707">707</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;declare&nbsp;param_facility_key&nbsp;int;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="708" href="#708">708</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;declare&nbsp;facility_cursor&nbsp;cursor&nbsp;for</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="709" href="#709">709</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="710" href="#710">710</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;pricing_switches_quickrep_table</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="711" href="#711">711</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="712" href="#712">712</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="713" href="#713">713</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;declare&nbsp;continue&nbsp;handler&nbsp;for&nbsp;not&nbsp;found&nbsp;set&nbsp;no_more_rows&nbsp;=&nbsp;TRUE;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="714" href="#714">714</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;open&nbsp;facility_cursor;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="715" href="#715">715</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_loop:&nbsp;loop</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="716" href="#716">716</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;fetch&nbsp;facility_cursor&nbsp;into&nbsp;param_facility_key;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="717" href="#717">717</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if&nbsp;no_more_rows&nbsp;then</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="718" href="#718">718</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;commit;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="719" href="#719">719</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;close&nbsp;facility_cursor;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="720" href="#720">720</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;leave&nbsp;facility_loop;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="721" href="#721">721</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;end&nbsp;if;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="722" href="#722">722</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;call&nbsp;facility_rankings(param_facility_key,&nbsp;new_bid,&nbsp;new_resid,&nbsp;(select&nbsp;concat(city,&nbsp;'&nbsp;',&nbsp;state)&nbsp;from&nbsp;db_analytics.dim_facility&nbsp;where&nbsp;facility_key&nbsp;=&nbsp;param_facility_key),&nbsp;(select&nbsp;zip&nbsp;from&nbsp;db_analytics.dim_facility&nbsp;where&nbsp;facility_key&nbsp;=&nbsp;param_facility_key));</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="723" href="#723">723</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;end&nbsp;loop&nbsp;facility_loop;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="724" href="#724">724</a></td><td class="col-11 codeLine"><span class="default">end&nbsp;||</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="725" href="#725">725</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="726" href="#726">726</a></td><td class="col-11 codeLine"><span class="default">delimiter&nbsp;;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="727" href="#727">727</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="728" href="#728">728</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;procedure&nbsp;if&nbsp;exists&nbsp;pricing_switches_account_level_quickrep;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="729" href="#729">729</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="730" href="#730">730</a></td><td class="col-11 codeLine"><span class="default">delimiter&nbsp;||</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="731" href="#731">731</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="732" href="#732">732</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;procedure&nbsp;pricing_switches_account_level_quickrep(sf_account_id_param&nbsp;int,&nbsp;new_bid&nbsp;decimal(6,2),&nbsp;new_resid&nbsp;decimal(6,2))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="733" href="#733">733</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="734" href="#734">734</a></td><td class="col-11 codeLine"><span class="default">begin</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="735" href="#735">735</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="736" href="#736">736</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;pricing_switches_quickrep_table;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="737" href="#737">737</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;pricing_switches_quickrep_table(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="738" href="#738">738</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name&nbsp;varchar(100),</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="739" href="#739">739</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_key&nbsp;int,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="740" href="#740">740</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;current_bid&nbsp;decimal(6,2),</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="741" href="#741">741</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;residual_percent&nbsp;decimal(6,2),</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="742" href="#742">742</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;lowest_cpa&nbsp;decimal(6,2),</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="743" href="#743">743</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;current_zip_rank&nbsp;int,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="744" href="#744">744</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;current_city_rank&nbsp;int,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="745" href="#745">745</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;low_cpa_rank_zip&nbsp;int,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="746" href="#746">746</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;low_cpa_rank_city&nbsp;int,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="747" href="#747">747</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;lowest_residual&nbsp;decimal(6,2),</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="748" href="#748">748</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;low_resid_rank_zip&nbsp;int,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="749" href="#749">749</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;low_resid_rank_city&nbsp;int,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="750" href="#750">750</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;maintain_rank&nbsp;decimal(6,2),</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="751" href="#751">751</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;recomended_bid&nbsp;decimal(6,2),</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="752" href="#752">752</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;recomended_rank_zip&nbsp;int,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="753" href="#753">753</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;recomended_rank_city&nbsp;int</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="754" href="#754">754</a></td><td class="col-11 codeLine"><span class="default">);</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="755" href="#755">755</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="756" href="#756">756</a></td><td class="col-11 codeLine"><span class="default">insert&nbsp;into&nbsp;pricing_switches_quickrep_table</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="757" href="#757">757</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="758" href="#758">758</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="759" href="#759">759</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="760" href="#760">760</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;current_bid,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="761" href="#761">761</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;residual_percent&nbsp;as&nbsp;current_residual_percent,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="762" href="#762">762</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;new_bid&nbsp;as&nbsp;lowest_cpa,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="763" href="#763">763</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;current_zip_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="764" href="#764">764</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;current_city_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="765" href="#765">765</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;low_cpa_rank_zip,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="766" href="#766">766</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;low_cpa_rank_city,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="767" href="#767">767</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;round(new_resid,2)&nbsp;as&nbsp;lowest_residual,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="768" href="#768">768</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;low_resid_rank_zip,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="769" href="#769">769</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;low_resid_rank_city,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="770" href="#770">770</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;maintain_rank,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="771" href="#771">771</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;recomended_bid,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="772" href="#772">772</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;recomended_rank_zip,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="773" href="#773">773</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;null&nbsp;as&nbsp;recomended_rank_city</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="774" href="#774">774</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;db_analytics.dim_facility</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="775" href="#775">775</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;sf_account_id&nbsp;=&nbsp;sf_account_id_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="776" href="#776">776</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;live_on_network&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="777" href="#777">777</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="778" href="#778">778</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="779" href="#779">779</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="780" href="#780">780</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;pricing_switches_quickrep_table&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="781" href="#781">781</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="782" href="#782">782</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="783" href="#783">783</a></td><td class="col-11 codeLine"><span class="default">###########################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="784" href="#784">784</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Building&nbsp;Current&nbsp;Yeilds&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="785" href="#785">785</a></td><td class="col-11 codeLine"><span class="default">###########################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="786" href="#786">786</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="787" href="#787">787</a></td><td class="col-11 codeLine"><span class="default">SET&nbsp;@MOVE_IN_RATE_FLOOR=&nbsp;.19;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="788" href="#788">788</a></td><td class="col-11 codeLine"><span class="default">SET&nbsp;@SUBMIT_RATE_FLOOR=.02;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="789" href="#789">789</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="790" href="#790">790</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;facs_yields;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="791" href="#791">791</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;facs_yields</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="792" href="#792">792</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="793" href="#793">793</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;la.listing_avail_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="794" href="#794">794</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(use_override_cpa,&nbsp;greatest(fy.cpa,override_cpa),&nbsp;fy.cpa)&nbsp;as&nbsp;cpa,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="795" href="#795">795</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_move_in_rate,&nbsp;greatest(move_in_rate,&nbsp;override_move_in_rate),move_in_rate),&nbsp;@MOVE_IN_RATE_FLOOR),@MOVE_IN_RATE_FLOOR)&nbsp;as&nbsp;mir,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="796" href="#796">796</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_submit_rate,&nbsp;greatest(submit_rate,override_submit_rate),&nbsp;submit_rate),@SUBMIT_RATE_FLOOR),@SUBMIT_RATE_FLOOR)&nbsp;as&nbsp;sr,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="797" href="#797">797</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(use_override_cpa,&nbsp;greatest(fy.cpa,override_cpa),&nbsp;fy.cpa)&nbsp;*&nbsp;ifnull(greatest(if(use_override_move_in_rate,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="798" href="#798">798</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;greatest(move_in_rate,&nbsp;override_move_in_rate),move_in_rate),&nbsp;@MOVE_IN_RATE_FLOOR),@MOVE_IN_RATE_FLOOR)&nbsp;*&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="799" href="#799">799</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(greatest(if(use_override_submit_rate,&nbsp;greatest(submit_rate,override_submit_rate),&nbsp;submit_rate),@SUBMIT_RATE_FLOOR),@SUBMIT_RATE_FLOOR)as&nbsp;yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="800" href="#800">800</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;l.latitude,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="801" href="#801">801</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;l.longitude</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="802" href="#802">802</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.facility_yields&nbsp;fy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="803" href="#803">803</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_avail&nbsp;la</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="804" href="#804">804</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(listing_avail_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="805" href="#805">805</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;f&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="806" href="#806">806</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;(f.facility_key=la.listing_avail_id&nbsp;and&nbsp;f.live_on_network&nbsp;and&nbsp;not&nbsp;is_directory)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="807" href="#807">807</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.location&nbsp;l</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="808" href="#808">808</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(location_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="809" href="#809">809</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="810" href="#810">810</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="811" href="#811">811</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;listing_avail_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="812" href="#812">812</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;facs_yields&nbsp;(listing_avail_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="813" href="#813">813</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="814" href="#814">814</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="815" href="#815">815</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;creating&nbsp;a&nbsp;suggested&nbsp;bid</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="816" href="#816">816</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="817" href="#817">817</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;suggested_bids;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="818" href="#818">818</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;table&nbsp;suggested_bids&nbsp;as</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="819" href="#819">819</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="820" href="#820">820</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="821" href="#821">821</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;pricing_key,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="822" href="#822">822</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;case&nbsp;when&nbsp;pricing_key&nbsp;=&nbsp;'cpa_tiered'&nbsp;then&nbsp;round(percent_from_min_bid*(current_bid&nbsp;-&nbsp;new_bid)&nbsp;+&nbsp;new_bid,2)&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="823" href="#823">823</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when&nbsp;pricing_key&nbsp;=&nbsp;'residual'&nbsp;then&nbsp;round((percent_from_min_bid*(residual_percent&nbsp;-&nbsp;new_resid)&nbsp;+&nbsp;new_resid),4)&nbsp;end&nbsp;as&nbsp;recomended_bid,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="824" href="#824">824</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;round(percent_from_min_bid*(current_bid&nbsp;-&nbsp;new_bid)&nbsp;+&nbsp;new_bid,2)&nbsp;as&nbsp;recomended_cpa_bid,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="825" href="#825">825</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;round((percent_from_min_bid*(residual_percent&nbsp;-&nbsp;new_resid)&nbsp;+&nbsp;new_resid),4)&nbsp;as&nbsp;recomended_residual</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="826" href="#826">826</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="827" href="#827">827</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;left&nbsp;join&nbsp;facility_lieniency_estimations&nbsp;fle</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="828" href="#828">828</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;df.facility_key&nbsp;=&nbsp;fle.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="829" href="#829">829</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;live_on_network&nbsp;=&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="830" href="#830">830</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;sf_account_id&nbsp;=&nbsp;sf_account_id_param</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="831" href="#831">831</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="832" href="#832">832</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="833" href="#833">833</a></td><td class="col-11 codeLine"><span class="default">create&nbsp;index&nbsp;facility_key&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="834" href="#834">834</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;suggested_bids&nbsp;(facility_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="835" href="#835">835</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="836" href="#836">836</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="837" href="#837">837</a></td><td class="col-11 codeLine"><span class="default">call&nbsp;pricing_switches_quickrep_cursor_procedure(new_bid,&nbsp;new_resid);</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="838" href="#838">838</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="839" href="#839">839</a></td><td class="col-11 codeLine"><span class="default">select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="840" href="#840">840</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;df.facility_name&nbsp;as&nbsp;'Facility&nbsp;Name',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="841" href="#841">841</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;df.facility_key&nbsp;as&nbsp;'Facility&nbsp;ID',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="842" href="#842">842</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;df.current_bid&nbsp;as&nbsp;'Current&nbsp;Bid',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="843" href="#843">843</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;df.residual_percent&nbsp;*&nbsp;100&nbsp;as&nbsp;'Current&nbsp;Residual&nbsp;Percent',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="844" href="#844">844</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;lowest_cpa&nbsp;as&nbsp;'CPA&nbsp;Floor&nbsp;(Entered&nbsp;Above)',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="845" href="#845">845</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;concat(low_cpa_rank_zip,&nbsp;'&nbsp;(',&nbsp;current_zip_rank&nbsp;-&nbsp;low_cpa_rank_zip,&nbsp;')')&nbsp;as&nbsp;'CPA&nbsp;Floor&nbsp;Rank&nbsp;-&nbsp;Zip&nbsp;(Change&nbsp;in&nbsp;Rank)',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="846" href="#846">846</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;concat(low_cpa_rank_city,&nbsp;'&nbsp;(',&nbsp;current_city_rank&nbsp;-&nbsp;low_cpa_rank_city,&nbsp;')')&nbsp;as&nbsp;'CPA&nbsp;Floor&nbsp;Rank&nbsp;-&nbsp;City&nbsp;(Change&nbsp;in&nbsp;Rank)',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="847" href="#847">847</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;lowest_residual&nbsp;*&nbsp;100&nbsp;as&nbsp;'Residual&nbsp;Floor&nbsp;(Entered&nbsp;Above)',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="848" href="#848">848</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;concat(low_resid_rank_zip,&nbsp;'&nbsp;(',&nbsp;current_zip_rank&nbsp;-&nbsp;low_resid_rank_zip,&nbsp;')')&nbsp;as&nbsp;'Resid&nbsp;Floor&nbsp;Rank&nbsp;-&nbsp;Zip&nbsp;(Change&nbsp;in&nbsp;Rank)',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="849" href="#849">849</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;concat(low_resid_rank_city,&nbsp;'&nbsp;(',&nbsp;current_city_rank&nbsp;-&nbsp;low_resid_rank_city,&nbsp;')')&nbsp;as&nbsp;'Resid&nbsp;Floor&nbsp;Rank&nbsp;-&nbsp;City&nbsp;(Change&nbsp;in&nbsp;Rank)',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="850" href="#850">850</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;concat(maintain_rank,&nbsp;case&nbsp;when&nbsp;df.pricing_key&nbsp;=&nbsp;'CPA_TIERED'&nbsp;then&nbsp;'&nbsp;Residual'&nbsp;when&nbsp;df.pricing_key&nbsp;=&nbsp;'RESIDUAL'&nbsp;then&nbsp;'&nbsp;CPA'&nbsp;end)&nbsp;as&nbsp;'Maintain&nbsp;Rank',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="851" href="#851">851</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;case&nbsp;when&nbsp;df.pricing_key&nbsp;=&nbsp;'residual'&nbsp;then&nbsp;concat(round(sb.recomended_bid&nbsp;*&nbsp;100,2),&nbsp;'&nbsp;Residual&nbsp;Percent')&nbsp;when&nbsp;df.pricing_key&nbsp;=&nbsp;'CPA_tiered'&nbsp;then&nbsp;concat(round(sb.recomended_bid,2),&nbsp;'&nbsp;CPA&nbsp;Bid')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="852" href="#852">852</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;end&nbsp;as&nbsp;'Recommended&nbsp;Lowest&nbsp;CPA&nbsp;Bid&nbsp;/&nbsp;Residual&nbsp;Percent',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="853" href="#853">853</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;concat(recomended_rank_zip,&nbsp;'&nbsp;(',&nbsp;current_zip_rank&nbsp;-&nbsp;recomended_rank_zip,&nbsp;')')&nbsp;as&nbsp;'Recomended&nbsp;Lowest&nbsp;Bid&nbsp;Rank&nbsp;-&nbsp;Zip&nbsp;(Change&nbsp;in&nbsp;Rank)',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="854" href="#854">854</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;concat(recomended_rank_city,&nbsp;'&nbsp;(',&nbsp;current_city_rank&nbsp;-&nbsp;recomended_rank_city,&nbsp;')')&nbsp;as&nbsp;'Recomended&nbsp;Lowest&nbsp;Bid&nbsp;Rank&nbsp;-&nbsp;City&nbsp;(Change&nbsp;in&nbsp;Rank)'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="855" href="#855">855</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;pricing_switches_quickrep_table&nbsp;psq</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="856" href="#856">856</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;db_analytics.dim_facility&nbsp;df</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="857" href="#857">857</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;psq.facility_key&nbsp;=&nbsp;df.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="858" href="#858">858</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;suggested_bids&nbsp;sb</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="859" href="#859">859</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;psq.facility_key&nbsp;=&nbsp;sb.facility_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="860" href="#860">860</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="861" href="#861">861</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="862" href="#862">862</a></td><td class="col-11 codeLine"><span class="default">#####################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="863" href="#863">863</a></td><td class="col-11 codeLine"><span class="default">#&nbsp;Dropping&nbsp;All&nbsp;of&nbsp;the&nbsp;tables&nbsp;!!!!!!&nbsp;#</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="864" href="#864">864</a></td><td class="col-11 codeLine"><span class="default">#####################################</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="865" href="#865">865</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="866" href="#866">866</a></td><td class="col-11 codeLine"><span class="default">drop&nbsp;tables&nbsp;if&nbsp;exists&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="867" href="#867">867</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;pricing_switches_quickrep_table,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="868" href="#868">868</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;facs_yields,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="869" href="#869">869</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;facility_distances_and_yield_for_zip,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="870" href="#870">870</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;facility_distances_and_yield_no_car_for_zip,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="871" href="#871">871</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;current_ranking_for_zip,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="872" href="#872">872</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;cpa_cpa,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="873" href="#873">873</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;new_facs_yields_cpa,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="874" href="#874">874</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;new_ranking_for_zip_cpa,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="875" href="#875">875</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;residual_cpa,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="876" href="#876">876</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;new_facs_yields_residual,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="877" href="#877">877</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;new_ranking_for_zip_residual,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="878" href="#878">878</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;facility_distances_and_yield_for_city,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="879" href="#879">879</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;facility_distances_and_yield_no_car_for_city,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="880" href="#880">880</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;current_ranking_for_city,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="881" href="#881">881</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;new_ranking_for_city_cpa,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="882" href="#882">882</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;new_ranking_for_city_residual,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="883" href="#883">883</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;zip_rankings_current_and_new,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="884" href="#884">884</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;city_rankings_current_and_new,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="885" href="#885">885</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;zip_ranking_to_beat,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="886" href="#886">886</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;zip_min_yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="887" href="#887">887</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;cpa_needed_to_maintain_rank_in_zip,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="888" href="#888">888</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;bid_or_residual_needed_to_maintain_rank_in_zip,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="889" href="#889">889</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;city_ranking_to_beat,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="890" href="#890">890</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;city_min_yield,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="891" href="#891">891</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;cpa_needed_to_maintain_rank_in_city,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="892" href="#892">892</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;bid_or_residual_needed_to_maintain_rank_in_city,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="893" href="#893">893</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;suggested_bids,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="894" href="#894">894</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;recomended_ranking_for_city,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="895" href="#895">895</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;recomended_ranking_for_zip,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="896" href="#896">896</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;new_facs_yields_recomended,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="897" href="#897">897</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;recomended_bid_cpa</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="898" href="#898">898</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="899" href="#899">899</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="900" href="#900">900</a></td><td class="col-11 codeLine"><span class="default">end&nbsp;||</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="901" href="#901">901</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="902" href="#902">902</a></td><td class="col-11 codeLine"><span class="default">delimiter&nbsp;;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="903" href="#903">903</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="904" href="#904">904</a></td><td class="col-11 codeLine"><span class="default">*/</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="905" href="#905">905</a></td><td class="col-11 codeLine"><span class="default">SQL</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="906" href="#906">906</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="907" href="#907">907</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/service/src/QuickRep/Report/QuickrepTest.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="index.html">Report</a></li>
         <li class="breadcrumb-item active">QuickrepTest.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;57</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;8</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="Sparefoot\PitaService\QuickRep\Report\QuickrepTest">QuickrepTest</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;57</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;8</div></td>
       <td class="danger small">600</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#11"><abbr title="getCategory()">getCategory</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#16"><abbr title="getName()">getName</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#21"><abbr title="showInProduction()">showInProduction</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#26"><abbr title="getDescription()">getDescription</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#48"><abbr title="getCustomDecorator()">getCustomDecorator</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#53"><abbr title="getInputs()">getInputs</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;12</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#73"><abbr title="prepareParameters(array $parameters)">prepareParameters</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;6</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#98"><abbr title="getSql(array $parameters)">getSql</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;33</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">272</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Input\ArraySelectInput</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Input\QuerySelectInput</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">QuickrepTest</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">Report</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getCategory</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'Tests'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getName</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'Brett&nbsp;QuickRep&nbsp;Test'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">showInProduction</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">false</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getDescription</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">&quot;&lt;b&gt;&nbsp;Results&nbsp;from&nbsp;UI&nbsp;Tests&nbsp;including&nbsp;percentage&nbsp;CR&nbsp;lifts&nbsp;for&nbsp;each&nbsp;variation,&nbsp;P-values,&nbsp;and&nbsp;confidence&nbsp;intervals&nbsp;&lt;/b&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;b&gt;Column&nbsp;definitions&lt;/b&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Static&nbsp;searches:&nbsp;Consumers&nbsp;viewing&nbsp;landing&nbsp;pages&nbsp;that&nbsp;provide&nbsp;search&nbsp;results.&nbsp;&nbsp;Good&nbsp;examples&nbsp;of&nbsp;static&nbsp;search&nbsp;pages&nbsp;would&nbsp;be&nbsp;City&nbsp;pages,&nbsp;Neighborhood&nbsp;Pages,&nbsp;and&nbsp;Zip&nbsp;pages.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;These&nbsp;pages&nbsp;deliver&nbsp;search&nbsp;results&nbsp;but&nbsp;are&nbsp;different&nbsp;from&nbsp;'Queried'&nbsp;searches&nbsp;in&nbsp;that&nbsp;the&nbsp;user&nbsp;did&nbsp;not&nbsp;input&nbsp;a&nbsp;direct&nbsp;search&nbsp;query.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;Clicks&nbsp;and&nbsp;Clickers:&nbsp;Any&nbsp;drill&nbsp;down&nbsp;into&nbsp;a&nbsp;facility&nbsp;whether&nbsp;it&nbsp;be&nbsp;to&nbsp;a&nbsp;facility&nbsp;detail&nbsp;page&nbsp;or&nbsp;a&nbsp;unit&nbsp;detail&nbsp;page&nbsp;will&nbsp;count&nbsp;as&nbsp;a&nbsp;click.&nbsp;&nbsp;A&nbsp;clicker&nbsp;is&nbsp;a&nbsp;'unique&nbsp;click'&nbsp;and&nbsp;represents&nbsp;a&nbsp;searcher&nbsp;that&nbsp;has&nbsp;clicked&nbsp;into&nbsp;at&nbsp;least&nbsp;one&nbsp;facility&nbsp;from&nbsp;the&nbsp;search&nbsp;results.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;Reconciled&nbsp;Bookings:&nbsp;Any&nbsp;booking&nbsp;that&nbsp;has&nbsp;gone&nbsp;through&nbsp;a&nbsp;statement&nbsp;batch&nbsp;and&nbsp;has&nbsp;been&nbsp;reconciled.&nbsp;&nbsp;These&nbsp;bookings&nbsp;have&nbsp;a&nbsp;booking&nbsp;state&nbsp;of&nbsp;confirmed,disputed,or&nbsp;cancelled&nbsp;and&nbsp;are&nbsp;the&nbsp;denominator&nbsp;when&nbsp;calculating&nbsp;move-in&nbsp;rate&nbsp;(move-in&nbsp;rate&nbsp;=&nbsp;confirmed&nbsp;bookings&nbsp;/&nbsp;reconciled&nbsp;bookings).</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="comment">//&nbsp;use&nbsp;the&nbsp;transposed&nbsp;table&nbsp;custom&nbsp;decorator</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getCustomDecorator</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'html_transposed_table'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getInputs</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'TEST_ID'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">QuerySelectInput</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'UI&nbsp;Test'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'select&nbsp;concat(&quot;(&quot;,test_id,&quot;)&quot;,&nbsp;&quot;&nbsp;&quot;,&nbsp;label)&nbsp;as&nbsp;test_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;test_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;from&nbsp;sparefoot.tests&nbsp;t</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;test_id&nbsp;desc</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;;'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'test_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'test_name'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getDao</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">false</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'PAGE'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ArraySelectInput</span><span class="keyword">(</span><span class="default">'Limit&nbsp;to&nbsp;pages'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'SITE_CONTENT'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'City&nbsp;Pages'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'INDEX'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'Home&nbsp;Page'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">prepareParameters</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">&amp;</span><span class="default">$parameters</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getDao</span><span class="keyword">(</span><span class="keyword">)</span><span class="default">-&gt;</span><span class="default">query</span><span class="keyword">(</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'&nbsp;--&nbsp;create&nbsp;table&nbsp;of&nbsp;visit_ids&nbsp;in&nbsp;test&nbsp;experiencing&nbsp;specific&nbsp;page_type</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;drop&nbsp;table&nbsp;if&nbsp;exists&nbsp;db_analytics._ui_test_results_quickrep_filter;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;create&nbsp;&nbsp;table&nbsp;db_analytics._ui_test_results_quickrep_filter</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;pv.visit_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;from&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;join&nbsp;sparefoot.page_views&nbsp;pv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;on(&nbsp;&nbsp;&nbsp;tt.foreign_key=pv.page_view_id</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'&nbsp;AND&nbsp;pv.page_type&nbsp;in&nbsp;(:PAGE)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;tt.test_id&nbsp;=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;pv.visit_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">&nbsp;create&nbsp;unique&nbsp;index&nbsp;visit_id_i&nbsp;on&nbsp;db_analytics._ui_test_results_quickrep_filter(visit_id);</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;;'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'END_TIME'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="default">.=</span><span class="default">&nbsp;</span><span class="default">'&nbsp;23:59:59'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getSql</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$parameters</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$sql</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="default">test_data.test_name&nbsp;as&nbsp;`Test&nbsp;name`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">test_data.test_id&nbsp;as&nbsp;`Test&nbsp;ID`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">group_concat(distinct&nbsp;s.title)&nbsp;as&nbsp;`Affected&nbsp;Sites`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">test_data.variation_id&nbsp;as&nbsp;`Variation&nbsp;ID`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">test_data.variation&nbsp;as&nbsp;`Variation`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">test_data.started&nbsp;as&nbsp;Started,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">test_data.ended&nbsp;as&nbsp;Ended,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">test_data.distribution&nbsp;as&nbsp;`Distribution`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">test_data.phone_group_id&nbsp;as&nbsp;`Phone&nbsp;Group&nbsp;ID`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">test_data.unique_searchers&nbsp;as&nbsp;`Unique&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="default">test_data.unique_bookings&nbsp;as&nbsp;`Unique&nbsp;Bookings`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="default">round(test_data.npv_per_unique_searcher_includes_resid,2)&nbsp;as&nbsp;`NPV&nbsp;Per&nbsp;Unq&nbsp;Searcher`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">&nbsp;case&nbsp;when</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"><span class="default">test_data.variation&nbsp;regexp&nbsp;'control'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">then&nbsp;'n/a'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">else&nbsp;round(((test_data.npv_per_unique_searcher_includes_resid-control.npv_per_unique_searcher_includes_resid)/control.npv_per_unique_searcher_includes_resid)*100,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">end</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="default">as&nbsp;`Measured&nbsp;NPV&nbsp;per&nbsp;Unq&nbsp;Searcher&nbsp;Difference&nbsp;(%)`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="default">max(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;case&nbsp;when&nbsp;test_data.variation&nbsp;regexp&nbsp;'control'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then&nbsp;'n/a'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;abs(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="default">(test_data.rev_p_unq_search&nbsp;-&nbsp;control.rev_p_unq_search)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="default">sqrt(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">pow(control.std_dev_rev_unq_search,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">control.sample_size</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">+</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">pow(test_data.std_dev_rev_unq_search,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="default">test_data.sample_size</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;))&nbsp;-&nbsp;abs(z_score)&nbsp;&gt;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt_p_value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;end</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;as&nbsp;`Confidence&nbsp;NPV&nbsp;per&nbsp;Unq&nbsp;Searcher`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="default">if(test_data.variation&nbsp;regexp&nbsp;'control',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;'n/a',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;round((1-</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;max(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;case&nbsp;when&nbsp;test_data.variation&nbsp;regexp&nbsp;'control'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then&nbsp;'n/a'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;abs(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">(test_data.rev_p_unq_search&nbsp;-&nbsp;control.rev_p_unq_search)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="default">sqrt(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">pow(control.std_dev_rev_unq_search,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">control.sample_size</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"><span class="default">+</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="default">pow(test_data.std_dev_rev_unq_search,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="default">test_data.sample_size</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;))&nbsp;-&nbsp;abs(z_score)&nbsp;&gt;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt_p_value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;end</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;),2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;as&nbsp;`P-Value&nbsp;NPV&nbsp;per&nbsp;Unq&nbsp;Searcher`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">round(test_data.avg_npv,2)&nbsp;as&nbsp;`Avg&nbsp;Booking&nbsp;NPV`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="default">&nbsp;case&nbsp;when</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"><span class="default">test_data.variation&nbsp;regexp&nbsp;'control'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="default">then&nbsp;'n/a'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="default">else&nbsp;round(((test_data.avg_npv-control.avg_npv)/control.avg_npv)*100,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">end</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">as&nbsp;`Measured&nbsp;Avg&nbsp;Booking&nbsp;NPV&nbsp;Difference&nbsp;(%)`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="default">max(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;case&nbsp;when&nbsp;test_data.variation&nbsp;regexp&nbsp;'control'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then&nbsp;'n/a'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;abs(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"><span class="default">(test_data.avg_npv&nbsp;-&nbsp;control.avg_npv)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="default">sqrt(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"><span class="default">pow(control.std_dev_npv,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"><span class="default">control.sample_size_bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"><span class="default">+</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"><span class="default">pow(test_data.std_dev_npv,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"><span class="default">test_data.sample_size_bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;))&nbsp;-&nbsp;abs(z_score)&nbsp;&gt;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="240" href="#240">240</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt_p_value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="241" href="#241">241</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="242" href="#242">242</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="243" href="#243">243</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;end</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="244" href="#244">244</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="245" href="#245">245</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="246" href="#246">246</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="247" href="#247">247</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="248" href="#248">248</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;as&nbsp;`Confidence&nbsp;Avg&nbsp;NPV`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="249" href="#249">249</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="250" href="#250">250</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="251" href="#251">251</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="252" href="#252">252</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="253" href="#253">253</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;if(test_data.variation&nbsp;regexp&nbsp;'control',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="254" href="#254">254</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;'n/a',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="255" href="#255">255</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="256" href="#256">256</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;round((1-</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="257" href="#257">257</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="258" href="#258">258</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;max(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="259" href="#259">259</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;case&nbsp;when&nbsp;test_data.variation&nbsp;regexp&nbsp;'control'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="260" href="#260">260</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then&nbsp;'n/a'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="261" href="#261">261</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="262" href="#262">262</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="263" href="#263">263</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="264" href="#264">264</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;abs(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="265" href="#265">265</a></td><td class="col-11 codeLine"><span class="default">(test_data.avg_npv&nbsp;-&nbsp;control.avg_npv)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="266" href="#266">266</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="267" href="#267">267</a></td><td class="col-11 codeLine"><span class="default">sqrt(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="268" href="#268">268</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="269" href="#269">269</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="270" href="#270">270</a></td><td class="col-11 codeLine"><span class="default">pow(control.std_dev_npv,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="271" href="#271">271</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="272" href="#272">272</a></td><td class="col-11 codeLine"><span class="default">control.sample_size_bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="273" href="#273">273</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="274" href="#274">274</a></td><td class="col-11 codeLine"><span class="default">+</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="275" href="#275">275</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="276" href="#276">276</a></td><td class="col-11 codeLine"><span class="default">pow(test_data.std_dev_npv,2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="277" href="#277">277</a></td><td class="col-11 codeLine"><span class="default">/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="278" href="#278">278</a></td><td class="col-11 codeLine"><span class="default">test_data.sample_size_bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="279" href="#279">279</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="280" href="#280">280</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;))&nbsp;-&nbsp;abs(z_score)&nbsp;&gt;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="281" href="#281">281</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="282" href="#282">282</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt_p_value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="283" href="#283">283</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="284" href="#284">284</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="285" href="#285">285</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;end</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="286" href="#286">286</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="287" href="#287">287</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;),2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="288" href="#288">288</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="289" href="#289">289</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="290" href="#290">290</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;as&nbsp;`P-Value&nbsp;Avg&nbsp;NPV`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="291" href="#291">291</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="292" href="#292">292</a></td><td class="col-11 codeLine"><span class="default">&nbsp;test_data.ubus&nbsp;as&nbsp;`Unq&nbsp;Bookings/&nbsp;Unq&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="293" href="#293">293</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="294" href="#294">294</a></td><td class="col-11 codeLine"><span class="default">case&nbsp;when</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="295" href="#295">295</a></td><td class="col-11 codeLine"><span class="default">test_data.variation&nbsp;regexp&nbsp;'control'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="296" href="#296">296</a></td><td class="col-11 codeLine"><span class="default">then&nbsp;'n/a'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="297" href="#297">297</a></td><td class="col-11 codeLine"><span class="default">else&nbsp;round(((test_data.ubus-control.ubus)/control.ubus)*100,&nbsp;2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="298" href="#298">298</a></td><td class="col-11 codeLine"><span class="default">end</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="299" href="#299">299</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="300" href="#300">300</a></td><td class="col-11 codeLine"><span class="default">as&nbsp;`Measured&nbsp;CR&nbsp;Difference&nbsp;(%)`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="301" href="#301">301</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="302" href="#302">302</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;max(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="303" href="#303">303</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;case&nbsp;when&nbsp;test_data.variation&nbsp;regexp&nbsp;'control'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="304" href="#304">304</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then&nbsp;'n/a'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="305" href="#305">305</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="306" href="#306">306</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="307" href="#307">307</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="308" href="#308">308</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;abs((control.ubus-test_data.ubus)/sqrt(pow(control.standard_error_cr,2)+pow(test_data.standard_error_cr,2)))&nbsp;-&nbsp;abs(z_score)&nbsp;&gt;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="309" href="#309">309</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="310" href="#310">310</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt_p_value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="311" href="#311">311</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="312" href="#312">312</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="313" href="#313">313</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;end</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="314" href="#314">314</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="315" href="#315">315</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="316" href="#316">316</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="317" href="#317">317</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="318" href="#318">318</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;as&nbsp;`Confidence&nbsp;Unq&nbsp;Bookings/&nbsp;Unq&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="319" href="#319">319</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="320" href="#320">320</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="321" href="#321">321</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="322" href="#322">322</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="323" href="#323">323</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="324" href="#324">324</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;if(test_data.variation&nbsp;regexp&nbsp;'control',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="325" href="#325">325</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;'n/a',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="326" href="#326">326</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="327" href="#327">327</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;round((1-</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="328" href="#328">328</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="329" href="#329">329</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;max(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="330" href="#330">330</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;case&nbsp;when&nbsp;test_data.variation&nbsp;regexp&nbsp;'control'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="331" href="#331">331</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then&nbsp;'n/a'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="332" href="#332">332</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="333" href="#333">333</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="334" href="#334">334</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;when</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="335" href="#335">335</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;abs((control.ubus-test_data.ubus)/sqrt(pow(control.standard_error_cr,2)+pow(test_data.standard_error_cr,2)))&nbsp;-&nbsp;abs(z_score)&nbsp;&gt;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="336" href="#336">336</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;then</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="337" href="#337">337</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt_p_value</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="338" href="#338">338</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;else</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="339" href="#339">339</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="340" href="#340">340</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;end</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="341" href="#341">341</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="342" href="#342">342</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;),2)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="343" href="#343">343</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="344" href="#344">344</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="345" href="#345">345</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;as&nbsp;`P-Value&nbsp;Unq&nbsp;Bookings/Unq&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="346" href="#346">346</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="347" href="#347">347</a></td><td class="col-11 codeLine"><span class="default">test_data.visits&nbsp;as&nbsp;`Visits`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="348" href="#348">348</a></td><td class="col-11 codeLine"><span class="default">test_data.unique_visitors&nbsp;as&nbsp;`Unq&nbsp;Visitors`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="349" href="#349">349</a></td><td class="col-11 codeLine"><span class="default">test_data.searches&nbsp;as&nbsp;`Searches`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="350" href="#350">350</a></td><td class="col-11 codeLine"><span class="default">test_data.queried_searches&nbsp;as&nbsp;`Queried&nbsp;Searches`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="351" href="#351">351</a></td><td class="col-11 codeLine"><span class="default">test_data.static_searches&nbsp;as&nbsp;`Static&nbsp;Searches`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="352" href="#352">352</a></td><td class="col-11 codeLine"><span class="default">test_data.unique_searchers&nbsp;`Unq&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="353" href="#353">353</a></td><td class="col-11 codeLine"><span class="default">test_data.queried_searchers&nbsp;`Searchers&nbsp;Performing&nbsp;Queried&nbsp;Searches`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="354" href="#354">354</a></td><td class="col-11 codeLine"><span class="default">test_data.static_searchers&nbsp;`Searchers&nbsp;Performing&nbsp;Static&nbsp;Searches`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="355" href="#355">355</a></td><td class="col-11 codeLine"><span class="default">test_data.`total_clicks&nbsp;unit&nbsp;or&nbsp;fac&nbsp;detail`&nbsp;as&nbsp;`Total&nbsp;Clicks&nbsp;(Unit&nbsp;or&nbsp;Facility&nbsp;Detail)`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="356" href="#356">356</a></td><td class="col-11 codeLine"><span class="default">test_data.facility_page_views&nbsp;`Facility&nbsp;Page&nbsp;Views`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="357" href="#357">357</a></td><td class="col-11 codeLine"><span class="default">test_data.unit_page_views&nbsp;`Unit&nbsp;Page&nbsp;Views`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="358" href="#358">358</a></td><td class="col-11 codeLine"><span class="default">test_data.searchers_that_clicked&nbsp;`Searchers&nbsp;viewing&nbsp;a&nbsp;facility&nbsp;page&nbsp;or&nbsp;unit&nbsp;page`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="359" href="#359">359</a></td><td class="col-11 codeLine"><span class="default">test_data.unique_facility_page_views&nbsp;`Unq&nbsp;Facility&nbsp;Page&nbsp;Views`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="360" href="#360">360</a></td><td class="col-11 codeLine"><span class="default">test_data.unique_unit_page_views&nbsp;`Unq&nbsp;Unit&nbsp;Page&nbsp;Views`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="361" href="#361">361</a></td><td class="col-11 codeLine"><span class="default">test_data.avg_clicks_per_visitor_that_clicked&nbsp;`Avg&nbsp;Clicks&nbsp;per&nbsp;visitor&nbsp;that&nbsp;clicked`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="362" href="#362">362</a></td><td class="col-11 codeLine"><span class="default">test_data.`page_views/searches`&nbsp;`Page&nbsp;Views/Searches`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="363" href="#363">363</a></td><td class="col-11 codeLine"><span class="default">test_data.`page_views/unique_searchers`&nbsp;as&nbsp;`Page&nbsp;Views/Unq&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="364" href="#364">364</a></td><td class="col-11 codeLine"><span class="default">test_data.`unit_page_views/searches`&nbsp;`Unit&nbsp;Page&nbsp;Views/Searches`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="365" href="#365">365</a></td><td class="col-11 codeLine"><span class="default">test_data.`unique_unit_page_views/unique_searchers`&nbsp;as&nbsp;`Unq&nbsp;Unit&nbsp;Page&nbsp;Views/Unq&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="366" href="#366">366</a></td><td class="col-11 codeLine"><span class="default">test_data.`facility_page_views/searches`&nbsp;as&nbsp;`Facility&nbsp;Page&nbsp;Views&nbsp;/&nbsp;Searches`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="367" href="#367">367</a></td><td class="col-11 codeLine"><span class="default">test_data.`unique_facility_page_views/unique_searchers`&nbsp;as&nbsp;`Unq&nbsp;Facility&nbsp;Page&nbsp;Views/Unique&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="368" href="#368">368</a></td><td class="col-11 codeLine"><span class="default">test_data.`unq_clickers/unq_searchers`&nbsp;as&nbsp;`Unq&nbsp;Searchers&nbsp;Clicking/&nbsp;Unq&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="369" href="#369">369</a></td><td class="col-11 codeLine"><span class="default">test_data.`unique_visitors_that_clicked/unique_visitors`&nbsp;as&nbsp;`Unq&nbsp;Visitors&nbsp;Clicking/Unq&nbsp;Visitors`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="370" href="#370">370</a></td><td class="col-11 codeLine"><span class="default">test_data.bookings&nbsp;as&nbsp;`Bookings`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="371" href="#371">371</a></td><td class="col-11 codeLine"><span class="default">test_data.unique_bookings&nbsp;as&nbsp;`Unq&nbsp;Bookings`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="372" href="#372">372</a></td><td class="col-11 codeLine"><span class="default">test_data.npv&nbsp;as&nbsp;`NPV`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="373" href="#373">373</a></td><td class="col-11 codeLine"><span class="default">test_data.`bookings.bookings/visits`&nbsp;as&nbsp;`Bookings/Visits`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="374" href="#374">374</a></td><td class="col-11 codeLine"><span class="default">test_data.`bookings.bookings/unique_searchers`&nbsp;as&nbsp;`Bookings/Unq&nbsp;Searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="375" href="#375">375</a></td><td class="col-11 codeLine"><span class="default">test_data.`bookings.unique_bookings/unique_visitors`&nbsp;as&nbsp;`Unq&nbsp;Bookings/Unq&nbsp;Visitors`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="376" href="#376">376</a></td><td class="col-11 codeLine"><span class="default">test_data.npv_per_unique_visitor_includes_resid&nbsp;as&nbsp;`NPV&nbsp;per&nbsp;Unq&nbsp;Visitor`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="377" href="#377">377</a></td><td class="col-11 codeLine"><span class="default">round(test_data.lower_90_pct_cr_limit&nbsp;*100,2)&nbsp;as&nbsp;`Lower&nbsp;CR&nbsp;Limit&nbsp;90%&nbsp;Confidence`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="378" href="#378">378</a></td><td class="col-11 codeLine"><span class="default">round(test_data.upper_90_pct_cr_limit&nbsp;*100,2)&nbsp;as&nbsp;`Upper&nbsp;CR&nbsp;Limit&nbsp;90%&nbsp;Confidence`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="379" href="#379">379</a></td><td class="col-11 codeLine"><span class="default">round(test_data.lower_95_pct_cr_limit&nbsp;*100,2)&nbsp;as&nbsp;`Lower&nbsp;CR&nbsp;Limit&nbsp;95%&nbsp;Confidence`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="380" href="#380">380</a></td><td class="col-11 codeLine"><span class="default">round(test_data.upper_95_pct_cr_limit&nbsp;*100,2)&nbsp;as&nbsp;`Upper&nbsp;CR&nbsp;Limit&nbsp;95%&nbsp;Confidence`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="381" href="#381">381</a></td><td class="col-11 codeLine"><span class="default">round(test_data.standard_error_cr&nbsp;*100,2)&nbsp;as&nbsp;`Standard&nbsp;Error&nbsp;of&nbsp;CR`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="382" href="#382">382</a></td><td class="col-11 codeLine"><span class="default">test_data.mir&nbsp;as&nbsp;`Move-in&nbsp;Rate`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="383" href="#383">383</a></td><td class="col-11 codeLine"><span class="default">test_data.processed_bookings&nbsp;as&nbsp;`Reconciled&nbsp;Bookings`</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="384" href="#384">384</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="385" href="#385">385</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="386" href="#386">386</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="387" href="#387">387</a></td><td class="col-11 codeLine"><span class="default">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="388" href="#388">388</a></td><td class="col-11 codeLine"><span class="default">db_analytics.z_table&nbsp;z</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="389" href="#389">389</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="390" href="#390">390</a></td><td class="col-11 codeLine"><span class="default">--&nbsp;Test&nbsp;data</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="391" href="#391">391</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="392" href="#392">392</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="393" href="#393">393</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="394" href="#394">394</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;t.label&nbsp;as&nbsp;test_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="395" href="#395">395</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;t.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="396" href="#396">396</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tv.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="397" href="#397">397</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;t.start_time&nbsp;as&nbsp;started,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="398" href="#398">398</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;t.end_time&nbsp;as&nbsp;ended,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="399" href="#399">399</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tv.label&nbsp;as&nbsp;variation,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="400" href="#400">400</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tv.distribution,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="401" href="#401">401</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tv.phone_group_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="402" href="#402">402</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;visits,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="403" href="#403">403</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unique_visitors,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="404" href="#404">404</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;searches,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="405" href="#405">405</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;queried_searches,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="406" href="#406">406</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;static_searches,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="407" href="#407">407</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="408" href="#408">408</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unique_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="409" href="#409">409</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;queried_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="410" href="#410">410</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;static_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="411" href="#411">411</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;page_views&nbsp;as&nbsp;`total_clicks&nbsp;unit&nbsp;or&nbsp;fac&nbsp;detail`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="412" href="#412">412</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_page_views,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="413" href="#413">413</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unit_page_views,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="414" href="#414">414</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;visitors_page_viewing&nbsp;as&nbsp;searchers_that_clicked,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="415" href="#415">415</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unique_facility_page_views,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="416" href="#416">416</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unique_unit_page_views,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="417" href="#417">417</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;page_views/visitors_page_viewing&nbsp;as&nbsp;avg_clicks_per_visitor_that_clicked,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="418" href="#418">418</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;page_views/searches,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="419" href="#419">419</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="420" href="#420">420</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;page_views/unique_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="421" href="#421">421</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="422" href="#422">422</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="423" href="#423">423</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unit_page_views/searches,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="424" href="#424">424</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="425" href="#425">425</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;facility_page_views/searches,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="426" href="#426">426</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="427" href="#427">427</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unique_facility_page_views/unique_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="428" href="#428">428</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="429" href="#429">429</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unique_unit_page_views/unique_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="430" href="#430">430</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="431" href="#431">431</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="432" href="#432">432</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="433" href="#433">433</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;visitors_page_viewing/unique_searchers&nbsp;as&nbsp;`unq_clickers/unq_searchers`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="434" href="#434">434</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;visitors_page_viewing/unique_visitors&nbsp;as&nbsp;`unique_visitors_that_clicked/unique_visitors`,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="435" href="#435">435</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="436" href="#436">436</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.unique_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="437" href="#437">437</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revenue.npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="438" href="#438">438</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.bookings/visits,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="439" href="#439">439</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.bookings/unique_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="440" href="#440">440</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.unique_bookings/unique_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="441" href="#441">441</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.unique_bookings/unique_searchers&nbsp;as&nbsp;ubus,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="442" href="#442">442</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.unique_bookings/unique_visitors,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="443" href="#443">443</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revenue.revenue/unique_visitors&nbsp;as&nbsp;revenue_per_unique_visitor,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="444" href="#444">444</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revenue.revenue/unique_searchers&nbsp;as&nbsp;&nbsp;revenue_per_unique_searcher,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="445" href="#445">445</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revenue.npv/unique_visitors&nbsp;as&nbsp;npv_per_unique_visitor_includes_resid,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="446" href="#446">446</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revenue.npv/unique_searchers&nbsp;as&nbsp;npv_per_unique_searcher_includes_resid,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="447" href="#447">447</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.mir,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="448" href="#448">448</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.processed_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="449" href="#449">449</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.avg_npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="450" href="#450">450</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.sample_size_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="451" href="#451">451</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.std_dev_npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="452" href="#452">452</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revsig.std_dev_rev_unq_search,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="453" href="#453">453</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revsig.rev_p_unq_search,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="454" href="#454">454</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revsig.sample_size,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="455" href="#455">455</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="456" href="#456">456</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="457" href="#457">457</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(&nbsp;&nbsp;&nbsp;&nbsp;/*CP*/(bookings.unique_bookings/page_views.unique_searchers)&nbsp;/*CP*/&nbsp;-&nbsp;(1.28&nbsp;*&nbsp;&nbsp;/*SE*/&nbsp;(sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)))&nbsp;&lt;&nbsp;0&nbsp;/*SE*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="458" href="#458">458</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="459" href="#459">459</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="460" href="#460">460</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="461" href="#461">461</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/*CP*/(bookings.unique_bookings/page_views.unique_searchers)&nbsp;/*CP*/&nbsp;-&nbsp;(1.28&nbsp;*&nbsp;&nbsp;/*SE*/&nbsp;(sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)))&nbsp;/*SE*/)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="462" href="#462">462</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="463" href="#463">463</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;as&nbsp;lower_90_pct_cr_limit,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="464" href="#464">464</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="465" href="#465">465</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="466" href="#466">466</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="467" href="#467">467</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(&nbsp;&nbsp;&nbsp;&nbsp;/*CP*/(bookings.unique_bookings/page_views.unique_searchers)&nbsp;/*CP*/&nbsp;+&nbsp;(1.28&nbsp;*&nbsp;&nbsp;/*SE*/&nbsp;(sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)))&nbsp;&gt;1&nbsp;&nbsp;/*SE*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="468" href="#468">468</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="469" href="#469">469</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="470" href="#470">470</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="471" href="#471">471</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/*CP*/(bookings.unique_bookings/page_views.unique_searchers)&nbsp;/*CP*/&nbsp;+&nbsp;(1.28&nbsp;*&nbsp;&nbsp;/*SE*/&nbsp;(sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)))&nbsp;/*SE*/)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="472" href="#472">472</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="473" href="#473">473</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;as&nbsp;upper_90_pct_cr_limit,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="474" href="#474">474</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="475" href="#475">475</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="476" href="#476">476</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="477" href="#477">477</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="478" href="#478">478</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(&nbsp;&nbsp;&nbsp;&nbsp;/*CP*/(bookings.unique_bookings/page_views.unique_searchers)&nbsp;/*CP*/&nbsp;-&nbsp;(1.65&nbsp;*&nbsp;&nbsp;/*SE*/&nbsp;(sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)))&nbsp;&lt;&nbsp;0&nbsp;/*SE*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="479" href="#479">479</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="480" href="#480">480</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="481" href="#481">481</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="482" href="#482">482</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/*CP*/(bookings.unique_bookings/page_views.unique_searchers)&nbsp;/*CP*/&nbsp;-&nbsp;(1.65&nbsp;*&nbsp;&nbsp;/*SE*/&nbsp;(sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)))&nbsp;/*SE*/)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="483" href="#483">483</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="484" href="#484">484</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;as&nbsp;lower_95_pct_cr_limit,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="485" href="#485">485</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="486" href="#486">486</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="487" href="#487">487</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="488" href="#488">488</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;if(&nbsp;&nbsp;&nbsp;&nbsp;/*CP*/(bookings.unique_bookings/page_views.unique_searchers)&nbsp;/*CP*/&nbsp;+&nbsp;(1.65&nbsp;*&nbsp;&nbsp;/*SE*/&nbsp;(sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)))&nbsp;&gt;1&nbsp;&nbsp;/*SE*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="489" href="#489">489</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="490" href="#490">490</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="491" href="#491">491</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="492" href="#492">492</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/*CP*/(bookings.unique_bookings/page_views.unique_searchers)&nbsp;/*CP*/&nbsp;+&nbsp;(1.65&nbsp;*&nbsp;&nbsp;/*SE*/&nbsp;(sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)))&nbsp;/*SE*/)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="493" href="#493">493</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="494" href="#494">494</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;as&nbsp;upper_95_pct_cr_limit,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="495" href="#495">495</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="496" href="#496">496</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="497" href="#497">497</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)&nbsp;as&nbsp;standard_error_cr</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="498" href="#498">498</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="499" href="#499">499</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="500" href="#500">500</a></td><td class="col-11 codeLine"><span class="default">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="501" href="#501">501</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="502" href="#502">502</a></td><td class="col-11 codeLine"><span class="default">/*visits&nbsp;*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="503" href="#503">503</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="504" href="#504">504</a></td><td class="col-11 codeLine"><span class="default">(select&nbsp;tt.test_id,&nbsp;tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="505" href="#505">505</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(type='visit',v.visit_id,null))&nbsp;as&nbsp;visits,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="506" href="#506">506</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(type='visit',visit_id,null))&nbsp;as&nbsp;unique_visitors</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="507" href="#507">507</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="508" href="#508">508</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="509" href="#509">509</a></td><td class="col-11 codeLine"><span class="default">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="510" href="#510">510</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="511" href="#511">511</a></td><td class="col-11 codeLine"><span class="default">sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="512" href="#512">512</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.visits&nbsp;v</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="513" href="#513">513</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(type='visit'&nbsp;and&nbsp;v.visit_id&nbsp;=&nbsp;foreign_key)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="514" href="#514">514</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="515" href="#515">515</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="516" href="#516">516</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fip</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="517" href="#517">517</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="518" href="#518">518</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(fip.ip_address=v.ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="519" href="#519">519</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="520" href="#520">520</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;fip.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="521" href="#521">521</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;tt.test_id&nbsp;=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="522" href="#522">522</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="523" href="#523">523</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;tt.variation_id)visits</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="524" href="#524">524</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="525" href="#525">525</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="526" href="#526">526</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="527" href="#527">527</a></td><td class="col-11 codeLine"><span class="default">/*page_views*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="528" href="#528">528</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="529" href="#529">529</a></td><td class="col-11 codeLine"><span class="default">select&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="530" href="#530">530</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="531" href="#531">531</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(type='page_view'&nbsp;and&nbsp;((pv.page_type&nbsp;in('SEARCH'))&nbsp;or&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;sc.content_template_id&nbsp;not&nbsp;in&nbsp;(0,3,4))),v.visit_id,null))&nbsp;as&nbsp;searches,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="532" href="#532">532</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(type='page_view'&nbsp;and&nbsp;pv.page_type&nbsp;in('SEARCH'),v.visit_id,null))&nbsp;as&nbsp;queried_searches,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="533" href="#533">533</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(type='page_view'&nbsp;and&nbsp;pv.page_type&nbsp;in('site_content')&nbsp;and&nbsp;sc.content_template_id&nbsp;not&nbsp;in&nbsp;(0,3,4),v.visit_id,null))&nbsp;as&nbsp;static_searches,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="534" href="#534">534</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(type='page_view'&nbsp;and&nbsp;((pv.page_type&nbsp;in('SEARCH'))&nbsp;or&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;sc.content_template_id&nbsp;not&nbsp;in&nbsp;(0,3,4))),v.visit_id,null))&nbsp;as&nbsp;unique_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="535" href="#535">535</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(type='page_view'&nbsp;and&nbsp;((pv.page_type&nbsp;in('SEARCH'))&nbsp;or&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;sc.content_template_id&nbsp;not&nbsp;in&nbsp;(0,3,4))),v.visit_id,null))&nbsp;as&nbsp;unique_searchers_visitors,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="536" href="#536">536</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(type='page_view'&nbsp;and&nbsp;pv.page_type&nbsp;in('SEARCH'),v.visit_id,null))&nbsp;as&nbsp;queried_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="537" href="#537">537</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(type='page_view'&nbsp;and&nbsp;pv.page_type&nbsp;in('site_content')&nbsp;and&nbsp;sc.content_template_id&nbsp;not&nbsp;in&nbsp;(0,3,4),v.visit_id,null))&nbsp;as&nbsp;static_searchers,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="538" href="#538">538</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(pv.page_type&nbsp;in&nbsp;('FACILITY_DETAIL','MOVE_IN_DETAIL','FACILITY_PANE_VIEW'),v.visit_id,null))&nbsp;as&nbsp;page_views,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="539" href="#539">539</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(pv.page_type&nbsp;in&nbsp;('MOVE_IN_DETAIL'),v.visit_id,null))&nbsp;as&nbsp;unit_page_views,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="540" href="#540">540</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(pv.page_type&nbsp;in&nbsp;('FACILITY_DETAIL'),v.visit_id,null))&nbsp;as&nbsp;facility_page_views,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="541" href="#541">541</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(pv.page_type&nbsp;in&nbsp;('MOVE_IN_DETAIL'),v.visit_id,null))&nbsp;as&nbsp;unique_unit_page_views,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="542" href="#542">542</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(pv.page_type&nbsp;in&nbsp;('FACILITY_DETAIL'),v.visit_id,null))&nbsp;as&nbsp;unique_facility_page_views,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="543" href="#543">543</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(pv.page_type&nbsp;in&nbsp;('FACILITY_DETAIL','MOVE_IN_DETAIL','FACILITY_PANE_VIEW'),&nbsp;&nbsp;v.visit_id,&nbsp;null))&nbsp;as&nbsp;visitors_page_viewing</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="544" href="#544">544</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="545" href="#545">545</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="546" href="#546">546</a></td><td class="col-11 codeLine"><span class="default">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="547" href="#547">547</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="548" href="#548">548</a></td><td class="col-11 codeLine"><span class="default">sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="549" href="#549">549</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.page_views&nbsp;pv&nbsp;on(type='page_view'&nbsp;and&nbsp;foreign_key&nbsp;=&nbsp;pv.page_view_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="550" href="#550">550</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.visits&nbsp;v&nbsp;on&nbsp;(pv.visit_id=v.visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="551" href="#551">551</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="552" href="#552">552</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="553" href="#553">553</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.site_content&nbsp;sc&nbsp;on&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;pv.site_content_id&nbsp;=&nbsp;sc.content_id&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="554" href="#554">554</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fips&nbsp;on&nbsp;(fips.ip_address=v.ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="555" href="#555">555</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="556" href="#556">556</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="557" href="#557">557</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;fips.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="558" href="#558">558</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;tt.test_id&nbsp;=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="559" href="#559">559</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="560" href="#560">560</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="561" href="#561">561</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;tt.variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="562" href="#562">562</a></td><td class="col-11 codeLine"><span class="default">)page_views</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="563" href="#563">563</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="564" href="#564">564</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(page_views.variation_id=visits.variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="565" href="#565">565</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="566" href="#566">566</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="567" href="#567">567</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="568" href="#568">568</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="569" href="#569">569</a></td><td class="col-11 codeLine"><span class="default">/*&nbsp;bookings*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="570" href="#570">570</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="571" href="#571">571</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="572" href="#572">572</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="573" href="#573">573</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="574" href="#574">574</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="575" href="#575">575</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(type='booking'&nbsp;and&nbsp;booking_state&nbsp;IN&nbsp;('PENDING',&nbsp;'CONFIRMED',&nbsp;'DISPUTED',&nbsp;'CANCELLED'),lrs.confirmation_code,null))&nbsp;as&nbsp;bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="576" href="#576">576</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(type='booking'&nbsp;and&nbsp;booking_state&nbsp;IN&nbsp;('PENDING',&nbsp;'CONFIRMED',&nbsp;'DISPUTED',&nbsp;'CANCELLED'),v.visit_id,null))&nbsp;as&nbsp;unique_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="577" href="#577">577</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;avg(npv)&nbsp;as&nbsp;avg_npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="578" href="#578">578</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(*)&nbsp;as&nbsp;sample_size_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="579" href="#579">579</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;stddev_samp(lrs.npv)&nbsp;as&nbsp;std_dev_npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="580" href="#580">580</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="581" href="#581">581</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="582" href="#582">582</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(type='booking'&nbsp;and&nbsp;booking_state&nbsp;IN&nbsp;('CONFIRMED'),lrs.confirmation_code,null))&nbsp;as&nbsp;confirmed_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="583" href="#583">583</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(type='booking'&nbsp;and&nbsp;booking_state&nbsp;IN&nbsp;('CONFIRMED','disputed','cancelled'),lrs.confirmation_code,null))&nbsp;as&nbsp;processed_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="584" href="#584">584</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="585" href="#585">585</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(type='booking'&nbsp;and&nbsp;booking_state&nbsp;IN&nbsp;('CONFIRMED'),lrs.confirmation_code,null))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="586" href="#586">586</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="587" href="#587">587</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(if(type='booking'&nbsp;and&nbsp;booking_state&nbsp;IN&nbsp;('CONFIRMED','disputed','cancelled'),lrs.confirmation_code,null))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="588" href="#588">588</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;as&nbsp;mir</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="589" href="#589">589</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="590" href="#590">590</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="591" href="#591">591</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="592" href="#592">592</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="593" href="#593">593</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="594" href="#594">594</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="595" href="#595">595</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="596" href="#596">596</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs&nbsp;on(foreign_key&nbsp;=&nbsp;lrs.confirmation_code)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="597" href="#597">597</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.visits&nbsp;v&nbsp;using&nbsp;(visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="598" href="#598">598</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="599" href="#599">599</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="600" href="#600">600</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fi&nbsp;using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="601" href="#601">601</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;fi.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="602" href="#602">602</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="603" href="#603">603</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="604" href="#604">604</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="605" href="#605">605</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;tt.variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="606" href="#606">606</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;)bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="607" href="#607">607</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;(bookings.variation_id=visits.variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="608" href="#608">608</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="609" href="#609">609</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="610" href="#610">610</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="611" href="#611">611</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="612" href="#612">612</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="613" href="#613">613</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;sum(bid_amount)&nbsp;as&nbsp;revenue,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="614" href="#614">614</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;sum(npv)&nbsp;as&nbsp;npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="615" href="#615">615</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="616" href="#616">616</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="617" href="#617">617</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="618" href="#618">618</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="619" href="#619">619</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;(select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="620" href="#620">620</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="621" href="#621">621</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;lrs.visit_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="622" href="#622">622</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;avg(bid_amount)&nbsp;as&nbsp;bid_amount,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="623" href="#623">623</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;avg(npv)&nbsp;as&nbsp;npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="624" href="#624">624</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="625" href="#625">625</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="626" href="#626">626</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="627" href="#627">627</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="628" href="#628">628</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs&nbsp;on(foreign_key&nbsp;=&nbsp;lrs.confirmation_code)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="629" href="#629">629</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.visits&nbsp;v&nbsp;using&nbsp;(visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="630" href="#630">630</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="631" href="#631">631</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="632" href="#632">632</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fi&nbsp;using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="633" href="#633">633</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;fi.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="634" href="#634">634</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="635" href="#635">635</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="636" href="#636">636</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="637" href="#637">637</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;tt.variation_id,lrs.visit_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="638" href="#638">638</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)revbybooking</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="639" href="#639">639</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="640" href="#640">640</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="641" href="#641">641</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)revenue</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="642" href="#642">642</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="643" href="#643">643</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(revenue.variation_id=visits.variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="644" href="#644">644</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="645" href="#645">645</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="646" href="#646">646</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="647" href="#647">647</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="648" href="#648">648</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="649" href="#649">649</a></td><td class="col-11 codeLine"><span class="default">select&nbsp;rawrev.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="650" href="#650">650</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;rawrev.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="651" href="#651">651</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(rawrev.npv)&nbsp;npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="652" href="#652">652</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;searchers&nbsp;as&nbsp;sample_size,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="653" href="#653">653</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unq_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="654" href="#654">654</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;total_npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="655" href="#655">655</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;searchers-unq_bookings&nbsp;as&nbsp;num_0s,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="656" href="#656">656</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;total_npv/searchers&nbsp;as&nbsp;rev_p_unq_search,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="657" href="#657">657</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(pow(rawrev.npv&nbsp;-&nbsp;(total_npv/searchers),2))&nbsp;as&nbsp;bookings_delta,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="658" href="#658">658</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="659" href="#659">659</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sqrt(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="660" href="#660">660</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="661" href="#661">661</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="662" href="#662">662</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="663" href="#663">663</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(pow(rawrev.npv&nbsp;-&nbsp;(total_npv/searchers),2))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="664" href="#664">664</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="665" href="#665">665</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;+</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="666" href="#666">666</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="667" href="#667">667</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(searchers-unq_bookings)&nbsp;#num&nbsp;zeros</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="668" href="#668">668</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="669" href="#669">669</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="670" href="#670">670</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;pow(0-(total_npv/searchers),2)&nbsp;#&nbsp;x&nbsp;-&nbsp;sample&nbsp;mean&nbsp;for&nbsp;all&nbsp;the&nbsp;searchers&nbsp;with&nbsp;no&nbsp;bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="671" href="#671">671</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="672" href="#672">672</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="673" href="#673">673</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="674" href="#674">674</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="675" href="#675">675</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="676" href="#676">676</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="677" href="#677">677</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(&nbsp;&nbsp;1/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="678" href="#678">678</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(searchers-1)&nbsp;#degrees&nbsp;freedom&nbsp;1/n-1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="679" href="#679">679</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="680" href="#680">680</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="681" href="#681">681</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)&nbsp;as&nbsp;std_dev_rev_unq_search</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="682" href="#682">682</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="683" href="#683">683</a></td><td class="col-11 codeLine"><span class="default">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="684" href="#684">684</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="685" href="#685">685</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="686" href="#686">686</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="687" href="#687">687</a></td><td class="col-11 codeLine"><span class="default">tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="688" href="#688">688</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="689" href="#689">689</a></td><td class="col-11 codeLine"><span class="default">lrs.visit_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="690" href="#690">690</a></td><td class="col-11 codeLine"><span class="default">avg(npv)&nbsp;as&nbsp;npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="691" href="#691">691</a></td><td class="col-11 codeLine"><span class="default">count(distinct&nbsp;confirmation_code)&nbsp;as&nbsp;bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="692" href="#692">692</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="693" href="#693">693</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="694" href="#694">694</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="695" href="#695">695</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(lrs.confirmation_code&nbsp;=&nbsp;tt.foreign_key&nbsp;and&nbsp;type&nbsp;=&nbsp;'booking')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="696" href="#696">696</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.visits&nbsp;v</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="697" href="#697">697</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v.visit_id=lrs.visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="698" href="#698">698</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="699" href="#699">699</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="700" href="#700">700</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fips</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="701" href="#701">701</a></td><td class="col-11 codeLine"><span class="default">using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="702" href="#702">702</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="703" href="#703">703</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;fips.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="704" href="#704">704</a></td><td class="col-11 codeLine"><span class="default">and</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="705" href="#705">705</a></td><td class="col-11 codeLine"><span class="default">not&nbsp;lrs.free</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="706" href="#706">706</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;lrs.booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="707" href="#707">707</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="708" href="#708">708</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="709" href="#709">709</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,&nbsp;lrs.visit_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="710" href="#710">710</a></td><td class="col-11 codeLine"><span class="default">)rawrev</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="711" href="#711">711</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="712" href="#712">712</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="713" href="#713">713</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="714" href="#714">714</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="715" href="#715">715</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="716" href="#716">716</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="717" href="#717">717</a></td><td class="col-11 codeLine"><span class="default">tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="718" href="#718">718</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="719" href="#719">719</a></td><td class="col-11 codeLine"><span class="default">count(distinct&nbsp;pv.visit_id)&nbsp;as&nbsp;searchers</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="720" href="#720">720</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="721" href="#721">721</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="722" href="#722">722</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="723" href="#723">723</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.page_views&nbsp;pv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="724" href="#724">724</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(tt.foreign_key&nbsp;=pv.page_view_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="725" href="#725">725</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="726" href="#726">726</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.visits&nbsp;v&nbsp;on&nbsp;(pv.visit_id=v.visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="727" href="#727">727</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="728" href="#728">728</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="729" href="#729">729</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.site_content&nbsp;sc&nbsp;on&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;pv.site_content_id&nbsp;=&nbsp;sc.content_id&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="730" href="#730">730</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="731" href="#731">731</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fips</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="732" href="#732">732</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(fips.ip_address&nbsp;=&nbsp;v.ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="733" href="#733">733</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="734" href="#734">734</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="735" href="#735">735</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;fips.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="736" href="#736">736</a></td><td class="col-11 codeLine"><span class="default">and</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="737" href="#737">737</a></td><td class="col-11 codeLine"><span class="default">((pv.page_type&nbsp;in('SEARCH'))&nbsp;or&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;sc.content_template_id&nbsp;not&nbsp;in&nbsp;(0,3,4)))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="738" href="#738">738</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="739" href="#739">739</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="740" href="#740">740</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="741" href="#741">741</a></td><td class="col-11 codeLine"><span class="default">variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="742" href="#742">742</a></td><td class="col-11 codeLine"><span class="default">)searchsample</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="743" href="#743">743</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="744" href="#744">744</a></td><td class="col-11 codeLine"><span class="default">using&nbsp;(variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="745" href="#745">745</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="746" href="#746">746</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="747" href="#747">747</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="748" href="#748">748</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="749" href="#749">749</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(select&nbsp;tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="750" href="#750">750</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="751" href="#751">751</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;lrs.visit_id)&nbsp;as&nbsp;unq_bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="752" href="#752">752</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="753" href="#753">753</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="754" href="#754">754</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="755" href="#755">755</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="756" href="#756">756</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs&nbsp;on(foreign_key&nbsp;=&nbsp;lrs.confirmation_code)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="757" href="#757">757</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.visits&nbsp;v&nbsp;using&nbsp;(visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="758" href="#758">758</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="759" href="#759">759</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="760" href="#760">760</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fi&nbsp;using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="761" href="#761">761</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;fi.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="762" href="#762">762</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="763" href="#763">763</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;tt.test_id=&nbsp;&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="764" href="#764">764</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="765" href="#765">765</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="766" href="#766">766</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)unqbookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="767" href="#767">767</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="768" href="#768">768</a></td><td class="col-11 codeLine"><span class="default">using&nbsp;(variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="769" href="#769">769</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="770" href="#770">770</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="771" href="#771">771</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="772" href="#772">772</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="773" href="#773">773</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="774" href="#774">774</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="775" href="#775">775</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="776" href="#776">776</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="777" href="#777">777</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(npv)&nbsp;as&nbsp;total_npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="778" href="#778">778</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="779" href="#779">779</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="780" href="#780">780</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;(select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="781" href="#781">781</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="782" href="#782">782</a></td><td class="col-11 codeLine"><span class="default">tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="783" href="#783">783</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="784" href="#784">784</a></td><td class="col-11 codeLine"><span class="default">lrs.visit_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="785" href="#785">785</a></td><td class="col-11 codeLine"><span class="default">avg(npv)&nbsp;as&nbsp;npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="786" href="#786">786</a></td><td class="col-11 codeLine"><span class="default">count(distinct&nbsp;confirmation_code)&nbsp;as&nbsp;bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="787" href="#787">787</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="788" href="#788">788</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="789" href="#789">789</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="790" href="#790">790</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(lrs.confirmation_code&nbsp;=&nbsp;tt.foreign_key&nbsp;and&nbsp;type&nbsp;=&nbsp;'booking')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="791" href="#791">791</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.visits&nbsp;v</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="792" href="#792">792</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v.visit_id=lrs.visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="793" href="#793">793</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="794" href="#794">794</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="795" href="#795">795</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fips</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="796" href="#796">796</a></td><td class="col-11 codeLine"><span class="default">using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="797" href="#797">797</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="798" href="#798">798</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;fips.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="799" href="#799">799</a></td><td class="col-11 codeLine"><span class="default">and</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="800" href="#800">800</a></td><td class="col-11 codeLine"><span class="default">not&nbsp;lrs.free</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="801" href="#801">801</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;lrs.booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="802" href="#802">802</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="803" href="#803">803</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="804" href="#804">804</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,&nbsp;lrs.visit_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="805" href="#805">805</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;)bookingnpv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="806" href="#806">806</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="807" href="#807">807</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="808" href="#808">808</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)totalnpv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="809" href="#809">809</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="810" href="#810">810</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="811" href="#811">811</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="812" href="#812">812</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="813" href="#813">813</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="814" href="#814">814</a></td><td class="col-11 codeLine"><span class="default">)revsig</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="815" href="#815">815</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(revsig.variation_id=visits.variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="816" href="#816">816</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="817" href="#817">817</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="818" href="#818">818</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.tests&nbsp;t</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="819" href="#819">819</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(t.test_id=visits.test_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="820" href="#820">820</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.test_variations&nbsp;tv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="821" href="#821">821</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(tv.variation_id=visits.variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="822" href="#822">822</a></td><td class="col-11 codeLine"><span class="default">)test_data</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="823" href="#823">823</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="824" href="#824">824</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="825" href="#825">825</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="826" href="#826">826</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="827" href="#827">827</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="828" href="#828">828</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="829" href="#829">829</a></td><td class="col-11 codeLine"><span class="default">--&nbsp;control</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="830" href="#830">830</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="831" href="#831">831</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="832" href="#832">832</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="833" href="#833">833</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;t.label&nbsp;as&nbsp;test_name,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="834" href="#834">834</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;t.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="835" href="#835">835</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tv.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="836" href="#836">836</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;t.start_time&nbsp;as&nbsp;started,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="837" href="#837">837</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tv.label&nbsp;as&nbsp;variation,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="838" href="#838">838</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tv.distribution,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="839" href="#839">839</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tv.phone_group_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="840" href="#840">840</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.unique_bookings/unique_searchers&nbsp;as&nbsp;ubus,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="841" href="#841">841</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="842" href="#842">842</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sqrt((bookings.unique_bookings/page_views.unique_searchers)*(1-(bookings.unique_bookings/page_views.unique_searchers))/page_views.unique_searchers)&nbsp;as&nbsp;standard_error_cr,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="843" href="#843">843</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.avg_npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="844" href="#844">844</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.sample_size_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="845" href="#845">845</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;bookings.std_dev_npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="846" href="#846">846</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revenue.npv/unique_searchers&nbsp;as&nbsp;npv_per_unique_searcher_includes_resid,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="847" href="#847">847</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revsig.std_dev_rev_unq_search,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="848" href="#848">848</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revsig.revsig.rev_p_unq_search,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="849" href="#849">849</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;revsig.sample_size</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="850" href="#850">850</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="851" href="#851">851</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="852" href="#852">852</a></td><td class="col-11 codeLine"><span class="default">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="853" href="#853">853</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="854" href="#854">854</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="855" href="#855">855</a></td><td class="col-11 codeLine"><span class="default">/*page_views*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="856" href="#856">856</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="857" href="#857">857</a></td><td class="col-11 codeLine"><span class="default">select&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="858" href="#858">858</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="859" href="#859">859</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="860" href="#860">860</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(type='page_view'&nbsp;and&nbsp;((pv.page_type&nbsp;in('SEARCH'))&nbsp;or&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;sc.content_template_id&nbsp;not&nbsp;in&nbsp;(0,3,4))),v.visit_id,null))&nbsp;as&nbsp;unique_searchers</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="861" href="#861">861</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="862" href="#862">862</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="863" href="#863">863</a></td><td class="col-11 codeLine"><span class="default">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="864" href="#864">864</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="865" href="#865">865</a></td><td class="col-11 codeLine"><span class="default">sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="866" href="#866">866</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.page_views&nbsp;pv&nbsp;on(type='page_view'&nbsp;and&nbsp;foreign_key&nbsp;=&nbsp;pv.page_view_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="867" href="#867">867</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.visits&nbsp;v&nbsp;on&nbsp;(pv.visit_id=v.visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="868" href="#868">868</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="869" href="#869">869</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="870" href="#870">870</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.site_content&nbsp;sc&nbsp;on&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;pv.site_content_id&nbsp;=&nbsp;sc.content_id&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="871" href="#871">871</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fips&nbsp;on&nbsp;(fips.ip_address=v.ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="872" href="#872">872</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="873" href="#873">873</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="874" href="#874">874</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;fips.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="875" href="#875">875</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;tt.test_id&nbsp;=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="876" href="#876">876</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="877" href="#877">877</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="878" href="#878">878</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;tt.variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="879" href="#879">879</a></td><td class="col-11 codeLine"><span class="default">)page_views</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="880" href="#880">880</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="881" href="#881">881</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="882" href="#882">882</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="883" href="#883">883</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="884" href="#884">884</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="885" href="#885">885</a></td><td class="col-11 codeLine"><span class="default">/*&nbsp;bookings*/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="886" href="#886">886</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="887" href="#887">887</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="888" href="#888">888</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="889" href="#889">889</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="890" href="#890">890</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="891" href="#891">891</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="892" href="#892">892</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;if(type='booking'&nbsp;and&nbsp;booking_state&nbsp;IN&nbsp;('PENDING',&nbsp;'CONFIRMED',&nbsp;'DISPUTED',&nbsp;'CANCELLED'),v.visit_id,null))&nbsp;as&nbsp;unique_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="893" href="#893">893</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;avg(npv)&nbsp;as&nbsp;avg_npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="894" href="#894">894</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(*)&nbsp;as&nbsp;sample_size_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="895" href="#895">895</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;stddev_samp(lrs.npv)&nbsp;as&nbsp;std_dev_npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="896" href="#896">896</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="897" href="#897">897</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="898" href="#898">898</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="899" href="#899">899</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="900" href="#900">900</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs&nbsp;on(foreign_key&nbsp;=&nbsp;lrs.confirmation_code)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="901" href="#901">901</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.visits&nbsp;v&nbsp;using&nbsp;(visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="902" href="#902">902</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="903" href="#903">903</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="904" href="#904">904</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fi&nbsp;using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="905" href="#905">905</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;fi.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="906" href="#906">906</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="907" href="#907">907</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="908" href="#908">908</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="909" href="#909">909</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;tt.variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="910" href="#910">910</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;)bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="911" href="#911">911</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;on&nbsp;(bookings.variation_id=page_views.variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="912" href="#912">912</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="913" href="#913">913</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="914" href="#914">914</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="915" href="#915">915</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="916" href="#916">916</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="917" href="#917">917</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="918" href="#918">918</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;sum(bid_amount)&nbsp;as&nbsp;revenue,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="919" href="#919">919</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;sum(npv)&nbsp;as&nbsp;npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="920" href="#920">920</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="921" href="#921">921</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="922" href="#922">922</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="923" href="#923">923</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="924" href="#924">924</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;(select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="925" href="#925">925</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="926" href="#926">926</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;lrs.visit_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="927" href="#927">927</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;avg(bid_amount)&nbsp;as&nbsp;bid_amount,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="928" href="#928">928</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;avg(npv)&nbsp;as&nbsp;npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="929" href="#929">929</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="930" href="#930">930</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="931" href="#931">931</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="932" href="#932">932</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="933" href="#933">933</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs&nbsp;on(foreign_key&nbsp;=&nbsp;lrs.confirmation_code)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="934" href="#934">934</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.visits&nbsp;v&nbsp;using&nbsp;(visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="935" href="#935">935</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="936" href="#936">936</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="937" href="#937">937</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fi&nbsp;using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="938" href="#938">938</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;fi.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="939" href="#939">939</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="940" href="#940">940</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="941" href="#941">941</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="942" href="#942">942</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;tt.variation_id,lrs.visit_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="943" href="#943">943</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)revbybooking</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="944" href="#944">944</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="945" href="#945">945</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="946" href="#946">946</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)revenue</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="947" href="#947">947</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="948" href="#948">948</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(revenue.variation_id=page_views.variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="949" href="#949">949</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="950" href="#950">950</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="951" href="#951">951</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="952" href="#952">952</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="953" href="#953">953</a></td><td class="col-11 codeLine"><span class="default">select&nbsp;rawrev.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="954" href="#954">954</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;rawrev.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="955" href="#955">955</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(rawrev.npv)&nbsp;npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="956" href="#956">956</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;searchers&nbsp;as&nbsp;sample_size,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="957" href="#957">957</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;unq_bookings,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="958" href="#958">958</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;total_npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="959" href="#959">959</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;searchers-unq_bookings&nbsp;as&nbsp;num_0s,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="960" href="#960">960</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;total_npv/searchers&nbsp;as&nbsp;rev_p_unq_search,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="961" href="#961">961</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(pow(rawrev.npv&nbsp;-&nbsp;(total_npv/searchers),2))&nbsp;as&nbsp;bookings_delta,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="962" href="#962">962</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="963" href="#963">963</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="964" href="#964">964</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="965" href="#965">965</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sqrt(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="966" href="#966">966</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="967" href="#967">967</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="968" href="#968">968</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="969" href="#969">969</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(pow(rawrev.npv&nbsp;-&nbsp;(total_npv/searchers),2))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="970" href="#970">970</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="971" href="#971">971</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;+</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="972" href="#972">972</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="973" href="#973">973</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(searchers-unq_bookings)&nbsp;#num&nbsp;zeros</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="974" href="#974">974</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="975" href="#975">975</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="976" href="#976">976</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;pow(0-(total_npv/searchers),2)&nbsp;#&nbsp;x&nbsp;-&nbsp;sample&nbsp;mean&nbsp;for&nbsp;all&nbsp;the&nbsp;searchers&nbsp;with&nbsp;no&nbsp;bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="977" href="#977">977</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="978" href="#978">978</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="979" href="#979">979</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="980" href="#980">980</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="981" href="#981">981</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="982" href="#982">982</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="983" href="#983">983</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(&nbsp;&nbsp;1/</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="984" href="#984">984</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(searchers-1)&nbsp;#degrees&nbsp;freedom&nbsp;1/n-1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="985" href="#985">985</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="986" href="#986">986</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="987" href="#987">987</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)&nbsp;as&nbsp;std_dev_rev_unq_search</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="988" href="#988">988</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="989" href="#989">989</a></td><td class="col-11 codeLine"><span class="default">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="990" href="#990">990</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="991" href="#991">991</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="992" href="#992">992</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="993" href="#993">993</a></td><td class="col-11 codeLine"><span class="default">tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="994" href="#994">994</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="995" href="#995">995</a></td><td class="col-11 codeLine"><span class="default">lrs.visit_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="996" href="#996">996</a></td><td class="col-11 codeLine"><span class="default">avg(npv)&nbsp;as&nbsp;npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="997" href="#997">997</a></td><td class="col-11 codeLine"><span class="default">count(distinct&nbsp;confirmation_code)&nbsp;as&nbsp;bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="998" href="#998">998</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="999" href="#999">999</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1000" href="#1000">1000</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1001" href="#1001">1001</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(lrs.confirmation_code&nbsp;=&nbsp;tt.foreign_key&nbsp;and&nbsp;type&nbsp;=&nbsp;'booking')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1002" href="#1002">1002</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.visits&nbsp;v</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1003" href="#1003">1003</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v.visit_id=lrs.visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1004" href="#1004">1004</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1005" href="#1005">1005</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1006" href="#1006">1006</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fips</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1007" href="#1007">1007</a></td><td class="col-11 codeLine"><span class="default">using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1008" href="#1008">1008</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1009" href="#1009">1009</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;fips.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1010" href="#1010">1010</a></td><td class="col-11 codeLine"><span class="default">and</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1011" href="#1011">1011</a></td><td class="col-11 codeLine"><span class="default">not&nbsp;lrs.free</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1012" href="#1012">1012</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;lrs.booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1013" href="#1013">1013</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1014" href="#1014">1014</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1015" href="#1015">1015</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,&nbsp;lrs.visit_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1016" href="#1016">1016</a></td><td class="col-11 codeLine"><span class="default">)rawrev</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1017" href="#1017">1017</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1018" href="#1018">1018</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1019" href="#1019">1019</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1020" href="#1020">1020</a></td><td class="col-11 codeLine"><span class="default">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1021" href="#1021">1021</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1022" href="#1022">1022</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1023" href="#1023">1023</a></td><td class="col-11 codeLine"><span class="default">tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1024" href="#1024">1024</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1025" href="#1025">1025</a></td><td class="col-11 codeLine"><span class="default">count(distinct&nbsp;pv.visit_id)&nbsp;as&nbsp;searchers</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1026" href="#1026">1026</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1027" href="#1027">1027</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1028" href="#1028">1028</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1029" href="#1029">1029</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.page_views&nbsp;pv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1030" href="#1030">1030</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(tt.foreign_key&nbsp;=pv.page_view_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1031" href="#1031">1031</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1032" href="#1032">1032</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.visits&nbsp;v&nbsp;on&nbsp;(pv.visit_id=v.visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1033" href="#1033">1033</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1034" href="#1034">1034</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1035" href="#1035">1035</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.site_content&nbsp;sc&nbsp;on&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;pv.site_content_id&nbsp;=&nbsp;sc.content_id&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1036" href="#1036">1036</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1037" href="#1037">1037</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fips</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1038" href="#1038">1038</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(fips.ip_address&nbsp;=&nbsp;v.ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1039" href="#1039">1039</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1040" href="#1040">1040</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1041" href="#1041">1041</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;fips.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1042" href="#1042">1042</a></td><td class="col-11 codeLine"><span class="default">and</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1043" href="#1043">1043</a></td><td class="col-11 codeLine"><span class="default">((pv.page_type&nbsp;in('SEARCH'))&nbsp;or&nbsp;(pv.page_type='site_content'&nbsp;and&nbsp;sc.content_template_id&nbsp;not&nbsp;in&nbsp;(0,3,4)))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1044" href="#1044">1044</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1045" href="#1045">1045</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1046" href="#1046">1046</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1047" href="#1047">1047</a></td><td class="col-11 codeLine"><span class="default">variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1048" href="#1048">1048</a></td><td class="col-11 codeLine"><span class="default">)searchsample</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1049" href="#1049">1049</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1050" href="#1050">1050</a></td><td class="col-11 codeLine"><span class="default">using&nbsp;(variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1051" href="#1051">1051</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1052" href="#1052">1052</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1053" href="#1053">1053</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1054" href="#1054">1054</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1055" href="#1055">1055</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;(select&nbsp;tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1056" href="#1056">1056</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1057" href="#1057">1057</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;count(distinct&nbsp;lrs.visit_id)&nbsp;as&nbsp;unq_bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1058" href="#1058">1058</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1059" href="#1059">1059</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1060" href="#1060">1060</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1061" href="#1061">1061</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1062" href="#1062">1062</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs&nbsp;on(foreign_key&nbsp;=&nbsp;lrs.confirmation_code)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1063" href="#1063">1063</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;join&nbsp;sparefoot.visits&nbsp;v&nbsp;using&nbsp;(visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1064" href="#1064">1064</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1065" href="#1065">1065</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1066" href="#1066">1066</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fi&nbsp;using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1067" href="#1067">1067</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;fi.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1068" href="#1068">1068</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1069" href="#1069">1069</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;tt.test_id=&nbsp;&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1070" href="#1070">1070</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1071" href="#1071">1071</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;group&nbsp;by&nbsp;variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1072" href="#1072">1072</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)unqbookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1073" href="#1073">1073</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1074" href="#1074">1074</a></td><td class="col-11 codeLine"><span class="default">using&nbsp;(variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1075" href="#1075">1075</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1076" href="#1076">1076</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1077" href="#1077">1077</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1078" href="#1078">1078</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1079" href="#1079">1079</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1080" href="#1080">1080</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1081" href="#1081">1081</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;select&nbsp;test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1082" href="#1082">1082</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1083" href="#1083">1083</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(npv)&nbsp;as&nbsp;total_npv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1084" href="#1084">1084</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1085" href="#1085">1085</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1086" href="#1086">1086</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;(select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1087" href="#1087">1087</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1088" href="#1088">1088</a></td><td class="col-11 codeLine"><span class="default">tt.test_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1089" href="#1089">1089</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1090" href="#1090">1090</a></td><td class="col-11 codeLine"><span class="default">lrs.visit_id,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1091" href="#1091">1091</a></td><td class="col-11 codeLine"><span class="default">avg(npv)&nbsp;as&nbsp;npv,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1092" href="#1092">1092</a></td><td class="col-11 codeLine"><span class="default">count(distinct&nbsp;confirmation_code)&nbsp;as&nbsp;bookings</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1093" href="#1093">1093</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1094" href="#1094">1094</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;sparefoot.test_tracking&nbsp;tt</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1095" href="#1095">1095</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.listing_rent_submission&nbsp;lrs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1096" href="#1096">1096</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(lrs.confirmation_code&nbsp;=&nbsp;tt.foreign_key&nbsp;and&nbsp;type&nbsp;=&nbsp;'booking')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1097" href="#1097">1097</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.visits&nbsp;v</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1098" href="#1098">1098</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v.visit_id=lrs.visit_id)</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1099" href="#1099">1099</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'PAGE'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">'join&nbsp;db_analytics._ui_test_results_quickrep_filter&nbsp;v2</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1100" href="#1100">1100</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(v2.visit_id=v.visit_id)'</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1101" href="#1101">1101</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join&nbsp;sparefoot.filtered_ips&nbsp;fips</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1102" href="#1102">1102</a></td><td class="col-11 codeLine"><span class="default">using&nbsp;(ip_address)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1103" href="#1103">1103</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1104" href="#1104">1104</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;fips.ip_address&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1105" href="#1105">1105</a></td><td class="col-11 codeLine"><span class="default">and</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1106" href="#1106">1106</a></td><td class="col-11 codeLine"><span class="default">not&nbsp;lrs.free</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1107" href="#1107">1107</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;lrs.booking_state&nbsp;not&nbsp;in&nbsp;('invalid','errored')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1108" href="#1108">1108</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;tt.test_id=&nbsp;:TEST_ID</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1109" href="#1109">1109</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1110" href="#1110">1110</a></td><td class="col-11 codeLine"><span class="default">tt.variation_id,&nbsp;lrs.visit_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1111" href="#1111">1111</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;)bookingnpv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1112" href="#1112">1112</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1113" href="#1113">1113</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1114" href="#1114">1114</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;)totalnpv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1115" href="#1115">1115</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1116" href="#1116">1116</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;using&nbsp;(variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1117" href="#1117">1117</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1118" href="#1118">1118</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1119" href="#1119">1119</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1120" href="#1120">1120</a></td><td class="col-11 codeLine"><span class="default">)revsig</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1121" href="#1121">1121</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(revsig.variation_id=page_views.variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1122" href="#1122">1122</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1123" href="#1123">1123</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1124" href="#1124">1124</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1125" href="#1125">1125</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1126" href="#1126">1126</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1127" href="#1127">1127</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.tests&nbsp;t</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1128" href="#1128">1128</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(t.test_id=page_views.test_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1129" href="#1129">1129</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;sparefoot.test_variations&nbsp;tv</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1130" href="#1130">1130</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(tv.variation_id=page_views.variation_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1131" href="#1131">1131</a></td><td class="col-11 codeLine"><span class="default">)control</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1132" href="#1132">1132</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1133" href="#1133">1133</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(control.variation&nbsp;regexp&nbsp;'control')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1134" href="#1134">1134</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1135" href="#1135">1135</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1136" href="#1136">1136</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1137" href="#1137">1137</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1138" href="#1138">1138</a></td><td class="col-11 codeLine"><span class="default">sparefoot.test_site_map&nbsp;tsm</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1139" href="#1139">1139</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(tsm.test_id=test_data.test_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1140" href="#1140">1140</a></td><td class="col-11 codeLine"><span class="default">join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1141" href="#1141">1141</a></td><td class="col-11 codeLine"><span class="default">sparefoot.sites&nbsp;s</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1142" href="#1142">1142</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(tsm.site_id=s.site_id)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1143" href="#1143">1143</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1144" href="#1144">1144</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;test_data.variation_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1145" href="#1145">1145</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1146" href="#1146">1146</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1147" href="#1147">1147</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1148" href="#1148">1148</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="1149" href="#1149">1149</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$sql</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1150" href="#1150">1150</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1151" href="#1151">1151</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

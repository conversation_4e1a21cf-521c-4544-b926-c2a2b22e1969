<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickRep/Report/Emails</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Report</a></li>
         <li class="breadcrumb-item"><a href="index.html">Emails</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ClientRevenueAndProfit.php.html#11">Sparefoot\PitaService\QuickRep\Report\Emails\ClientRevenueAndProfit</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConsumerContacts.php.html#8">Sparefoot\PitaService\QuickRep\Report\Emails\ConsumerContacts</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FirstReconciliationEmailData.php.html#8">Sparefoot\PitaService\QuickRep\Report\Emails\FirstReconciliationEmailData</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetClientEmails.php.html#9">Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="GetClientEmails.php.html#9">Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails</a></td><td class="text-right">1722</td></tr>
       <tr><td><a href="ClientRevenueAndProfit.php.html#11">Sparefoot\PitaService\QuickRep\Report\Emails\ClientRevenueAndProfit</a></td><td class="text-right">132</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ClientRevenueAndProfit.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ClientRevenueAndProfit::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClientRevenueAndProfit.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ClientRevenueAndProfit::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClientRevenueAndProfit.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ClientRevenueAndProfit::overrideBoundParameters">overrideBoundParameters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClientRevenueAndProfit.php.html#30"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ClientRevenueAndProfit::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClientRevenueAndProfit.php.html#51"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ClientRevenueAndProfit::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClientRevenueAndProfit.php.html#69"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ClientRevenueAndProfit::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConsumerContacts.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ConsumerContacts::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConsumerContacts.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ConsumerContacts::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConsumerContacts.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ConsumerContacts::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConsumerContacts.php.html#45"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ConsumerContacts::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConsumerContacts.php.html#53"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ConsumerContacts::prepareParameters">prepareParameters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ConsumerContacts.php.html#58"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ConsumerContacts::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FirstReconciliationEmailData.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\FirstReconciliationEmailData::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FirstReconciliationEmailData.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\FirstReconciliationEmailData::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FirstReconciliationEmailData.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\FirstReconciliationEmailData::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FirstReconciliationEmailData.php.html#28"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\FirstReconciliationEmailData::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetClientEmails.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetClientEmails.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetClientEmails.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetClientEmails.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetClientEmails.php.html#167"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails::getWhereClauses">getWhereClauses</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="GetClientEmails.php.html#263"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="GetClientEmails.php.html#167"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails::getWhereClauses">getWhereClauses</abbr></a></td><td class="text-right">1122</td></tr>
       <tr><td><a href="ClientRevenueAndProfit.php.html#69"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\ClientRevenueAndProfit::getSql">getSql</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="GetClientEmails.php.html#263"><abbr title="Sparefoot\PitaService\QuickRep\Report\Emails\GetClientEmails::getSql">getSql</abbr></a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([4,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([22,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,11,"<a href=\"ClientRevenueAndProfit.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ClientRevenueAndProfit<\/a>"],[0,6,"<a href=\"ConsumerContacts.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ConsumerContacts<\/a>"],[0,4,"<a href=\"FirstReconciliationEmailData.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\FirstReconciliationEmailData<\/a>"],[0,41,"<a href=\"GetClientEmails.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\GetClientEmails<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ClientRevenueAndProfit.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ClientRevenueAndProfit::getCategory<\/a>"],[0,1,"<a href=\"ClientRevenueAndProfit.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ClientRevenueAndProfit::getName<\/a>"],[0,1,"<a href=\"ClientRevenueAndProfit.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ClientRevenueAndProfit::overrideBoundParameters<\/a>"],[0,1,"<a href=\"ClientRevenueAndProfit.php.html#30\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ClientRevenueAndProfit::getDescription<\/a>"],[0,1,"<a href=\"ClientRevenueAndProfit.php.html#51\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ClientRevenueAndProfit::getInputs<\/a>"],[0,6,"<a href=\"ClientRevenueAndProfit.php.html#69\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ClientRevenueAndProfit::getSql<\/a>"],[0,1,"<a href=\"ConsumerContacts.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ConsumerContacts::getCategory<\/a>"],[0,1,"<a href=\"ConsumerContacts.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ConsumerContacts::getName<\/a>"],[0,1,"<a href=\"ConsumerContacts.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ConsumerContacts::getDescription<\/a>"],[0,1,"<a href=\"ConsumerContacts.php.html#45\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ConsumerContacts::getInputs<\/a>"],[0,1,"<a href=\"ConsumerContacts.php.html#53\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ConsumerContacts::prepareParameters<\/a>"],[0,1,"<a href=\"ConsumerContacts.php.html#58\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\ConsumerContacts::getSql<\/a>"],[0,1,"<a href=\"FirstReconciliationEmailData.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\FirstReconciliationEmailData::getCategory<\/a>"],[0,1,"<a href=\"FirstReconciliationEmailData.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\FirstReconciliationEmailData::getName<\/a>"],[0,1,"<a href=\"FirstReconciliationEmailData.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\FirstReconciliationEmailData::getInputs<\/a>"],[0,1,"<a href=\"FirstReconciliationEmailData.php.html#28\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\FirstReconciliationEmailData::getSql<\/a>"],[0,1,"<a href=\"GetClientEmails.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\GetClientEmails::getCategory<\/a>"],[0,1,"<a href=\"GetClientEmails.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\GetClientEmails::getName<\/a>"],[0,1,"<a href=\"GetClientEmails.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\GetClientEmails::getDescription<\/a>"],[0,1,"<a href=\"GetClientEmails.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\GetClientEmails::getInputs<\/a>"],[0,33,"<a href=\"GetClientEmails.php.html#167\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\GetClientEmails::getWhereClauses<\/a>"],[0,4,"<a href=\"GetClientEmails.php.html#263\">Sparefoot\\PitaService\\QuickRep\\Report\\Emails\\GetClientEmails::getSql<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

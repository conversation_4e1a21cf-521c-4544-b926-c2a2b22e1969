<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/service/src/QuickRep/Report/AmForecast.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="index.html">Report</a></li>
         <li class="breadcrumb-item active">AmForecast.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;8</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;5</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="Sparefoot\PitaService\QuickRep\Report\AmForecast">AmForecast</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;8</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;5</div></td>
       <td class="danger small">30</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#9"><abbr title="getCategory()">getCategory</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#14"><abbr title="getName()">getName</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#19"><abbr title="showInProduction()">showInProduction</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#24"><abbr title="getDescription()">getDescription</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#33"><abbr title="getSql(array $parameters)">getSql</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;3</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">AmForecast</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">Report</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getCategory</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'CPA&nbsp;Core&nbsp;Metrics'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getName</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'AM&nbsp;Forecast'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">showInProduction</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getDescription</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;b&gt;Confirmed&nbsp;booking&nbsp;by&nbsp;hold&nbsp;date&nbsp;metrics&nbsp;used&nbsp;in&nbsp;AM&nbsp;Forecasting.&lt;/b&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getSql</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$parameters</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$sql</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">dam.account_manager_type&nbsp;as&nbsp;account_manager,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">'#CPA&nbsp;facilities'&nbsp;as&nbsp;metric,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-01-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-02-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-03-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-04-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-05-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-06-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-07-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-08-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-09-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-10-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-11-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-12-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-01-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-02-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-03-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-04-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-05-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-06-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-07-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-08-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-09-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-10-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-11-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-12-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-01-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-02-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-03-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-04-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-05-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-06-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-07-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-08-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-09-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-10-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-11-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-12-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-01-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-02-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-03-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-04-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-05-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-06-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-07-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-08-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-09-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-10-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-11-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-12-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-01-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-02-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-03-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-04-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-05-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-06-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-07-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-08-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-09-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-10-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-11-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-12-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-01-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-02-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-03-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-04-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-05-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-06-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-07-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-08-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-09-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-10-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-11-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-12-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-01-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-02-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-03-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-04-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-05-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-06-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-07-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-08-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-09-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-10-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-11-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-12-01'),&nbsp;ifnull(facilities_live,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-12-01'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;db_analytics.fact_account_manager_month&nbsp;lf</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;db_analytics.dim_account_manager&nbsp;dam&nbsp;on&nbsp;(lf.account_manager_key=dam.account_manager_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;lf.account_manager_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="default">UNION</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="default">#transposed&nbsp;cpa&nbsp;volume</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">dam.account_manager_type&nbsp;as&nbsp;account_manager,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="default">'%&nbsp;CPA&nbsp;Volume'&nbsp;as&nbsp;metric,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-01-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-02-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-03-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-04-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-05-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-06-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-07-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-08-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-09-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-10-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-11-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-12-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2010-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-01-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-02-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-03-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-04-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-05-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-06-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-07-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-08-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-09-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-10-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-11-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-12-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2011-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-01-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-02-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-03-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-04-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-05-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-06-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-07-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-08-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-09-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-10-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-11-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-12-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2012-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-01-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-02-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-03-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-04-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-05-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-06-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-07-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-08-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-09-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-10-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-11-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-12-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2013-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-01-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-02-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-03-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-04-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-05-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-06-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-07-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-08-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-09-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-10-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-11-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-12-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2014-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-01-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-02-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-03-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-04-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-05-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-06-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-07-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-08-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-09-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-10-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-11-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-12-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2015-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-01-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-02-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-03-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-04-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-05-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-06-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-07-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-08-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-09-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-10-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-11-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-12-01'),&nbsp;ifnull(hold_date_move_ins,0)/hold_date_total_month_move_ins,&nbsp;null)),3)&nbsp;as&nbsp;'2016-12-01'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;db_analytics.fact_account_manager_month&nbsp;lf</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;db_analytics.dim_account_manager&nbsp;dam&nbsp;on&nbsp;(lf.account_manager_key=dam.account_manager_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"><span class="default">where</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="default">1=1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;lf.account_manager_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"><span class="default">union</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"><span class="default">select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"><span class="default">dam.account_manager_type&nbsp;as&nbsp;account_manager,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"><span class="default">'actual_cpa_bid'&nbsp;as&nbsp;metric,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-01-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-02-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="240" href="#240">240</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-03-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="241" href="#241">241</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-04-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="242" href="#242">242</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-05-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="243" href="#243">243</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-06-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="244" href="#244">244</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-07-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="245" href="#245">245</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-08-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="246" href="#246">246</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-09-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="247" href="#247">247</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-10-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="248" href="#248">248</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-11-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="249" href="#249">249</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-12-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="250" href="#250">250</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-01-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="251" href="#251">251</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-02-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="252" href="#252">252</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-03-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="253" href="#253">253</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-04-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="254" href="#254">254</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-05-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="255" href="#255">255</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-06-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="256" href="#256">256</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-07-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="257" href="#257">257</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-08-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="258" href="#258">258</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-09-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="259" href="#259">259</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-10-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="260" href="#260">260</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-11-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="261" href="#261">261</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-12-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="262" href="#262">262</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-01-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="263" href="#263">263</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-02-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="264" href="#264">264</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-03-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="265" href="#265">265</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-04-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="266" href="#266">266</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-05-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="267" href="#267">267</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-06-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="268" href="#268">268</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-07-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="269" href="#269">269</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-08-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="270" href="#270">270</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-09-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="271" href="#271">271</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-10-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="272" href="#272">272</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-11-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="273" href="#273">273</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-12-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="274" href="#274">274</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-01-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="275" href="#275">275</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-02-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="276" href="#276">276</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-03-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="277" href="#277">277</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-04-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="278" href="#278">278</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-05-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="279" href="#279">279</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-06-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="280" href="#280">280</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-07-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="281" href="#281">281</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-08-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="282" href="#282">282</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-09-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="283" href="#283">283</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-10-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="284" href="#284">284</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-11-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="285" href="#285">285</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-12-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="286" href="#286">286</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-01-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="287" href="#287">287</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-02-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="288" href="#288">288</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-03-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="289" href="#289">289</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-04-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="290" href="#290">290</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-05-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="291" href="#291">291</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-06-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="292" href="#292">292</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-07-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="293" href="#293">293</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-08-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="294" href="#294">294</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-09-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="295" href="#295">295</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-10-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="296" href="#296">296</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-11-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="297" href="#297">297</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-12-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="298" href="#298">298</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-01-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="299" href="#299">299</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-02-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="300" href="#300">300</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-03-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="301" href="#301">301</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-04-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="302" href="#302">302</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-05-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="303" href="#303">303</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-06-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="304" href="#304">304</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-07-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="305" href="#305">305</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-08-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="306" href="#306">306</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-09-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="307" href="#307">307</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-10-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="308" href="#308">308</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-11-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="309" href="#309">309</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-12-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="310" href="#310">310</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-01-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="311" href="#311">311</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-02-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="312" href="#312">312</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-03-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="313" href="#313">313</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-04-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="314" href="#314">314</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-05-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="315" href="#315">315</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-06-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="316" href="#316">316</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-07-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="317" href="#317">317</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-08-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="318" href="#318">318</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-09-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="319" href="#319">319</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-10-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="320" href="#320">320</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-11-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="321" href="#321">321</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-12-01'),&nbsp;ifnull(hold_date_avg_bid_move_ins,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-12-01'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="322" href="#322">322</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="323" href="#323">323</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="324" href="#324">324</a></td><td class="col-11 codeLine"><span class="default">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="325" href="#325">325</a></td><td class="col-11 codeLine"><span class="default">db_analytics.fact_account_manager_month&nbsp;lf</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="326" href="#326">326</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;db_analytics.dim_account_manager&nbsp;dam&nbsp;on&nbsp;(lf.account_manager_key=dam.account_manager_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="327" href="#327">327</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="328" href="#328">328</a></td><td class="col-11 codeLine"><span class="default">where</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="329" href="#329">329</a></td><td class="col-11 codeLine"><span class="default">1=1</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="330" href="#330">330</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="331" href="#331">331</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;lf.account_manager_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="332" href="#332">332</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="333" href="#333">333</a></td><td class="col-11 codeLine"><span class="default">UNION</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="334" href="#334">334</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="335" href="#335">335</a></td><td class="col-11 codeLine"><span class="default">#avg&nbsp;facility&nbsp;bidy</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="336" href="#336">336</a></td><td class="col-11 codeLine"><span class="default">select&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="337" href="#337">337</a></td><td class="col-11 codeLine"><span class="default">dam.account_manager_type&nbsp;as&nbsp;account_manager,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="338" href="#338">338</a></td><td class="col-11 codeLine"><span class="default">'Average&nbsp;Facility&nbsp;Bid'&nbsp;as&nbsp;metric,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="339" href="#339">339</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-01-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="340" href="#340">340</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-02-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="341" href="#341">341</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-03-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="342" href="#342">342</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-04-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="343" href="#343">343</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-05-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="344" href="#344">344</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-06-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="345" href="#345">345</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-07-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="346" href="#346">346</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-08-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="347" href="#347">347</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-09-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="348" href="#348">348</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-10-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="349" href="#349">349</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-11-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="350" href="#350">350</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2010-12-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2010-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="351" href="#351">351</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-01-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="352" href="#352">352</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-02-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="353" href="#353">353</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-03-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="354" href="#354">354</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-04-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="355" href="#355">355</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-05-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="356" href="#356">356</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-06-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="357" href="#357">357</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-07-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="358" href="#358">358</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-08-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="359" href="#359">359</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-09-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="360" href="#360">360</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-10-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="361" href="#361">361</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-11-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="362" href="#362">362</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2011-12-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2011-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="363" href="#363">363</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-01-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="364" href="#364">364</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-02-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="365" href="#365">365</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-03-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="366" href="#366">366</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-04-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="367" href="#367">367</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-05-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="368" href="#368">368</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-06-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="369" href="#369">369</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-07-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="370" href="#370">370</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-08-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="371" href="#371">371</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-09-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="372" href="#372">372</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-10-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="373" href="#373">373</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-11-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="374" href="#374">374</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2012-12-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2012-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="375" href="#375">375</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-01-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="376" href="#376">376</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-02-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="377" href="#377">377</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-03-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="378" href="#378">378</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-04-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="379" href="#379">379</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-05-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="380" href="#380">380</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-06-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="381" href="#381">381</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-07-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="382" href="#382">382</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-08-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="383" href="#383">383</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-09-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="384" href="#384">384</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-10-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="385" href="#385">385</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-11-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="386" href="#386">386</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2013-12-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2013-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="387" href="#387">387</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-01-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="388" href="#388">388</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-02-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="389" href="#389">389</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-03-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="390" href="#390">390</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-04-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="391" href="#391">391</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-05-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="392" href="#392">392</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-06-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="393" href="#393">393</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-07-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="394" href="#394">394</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-08-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="395" href="#395">395</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-09-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="396" href="#396">396</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-10-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="397" href="#397">397</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-11-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="398" href="#398">398</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2014-12-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2014-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="399" href="#399">399</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-01-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="400" href="#400">400</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-02-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="401" href="#401">401</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-03-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="402" href="#402">402</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-04-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="403" href="#403">403</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-05-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="404" href="#404">404</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-06-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="405" href="#405">405</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-07-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="406" href="#406">406</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-08-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="407" href="#407">407</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-09-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="408" href="#408">408</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-10-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="409" href="#409">409</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-11-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="410" href="#410">410</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2015-12-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2015-12-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="411" href="#411">411</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-01-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-01-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="412" href="#412">412</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-02-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-02-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="413" href="#413">413</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-03-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-03-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="414" href="#414">414</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-04-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-04-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="415" href="#415">415</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-05-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-05-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="416" href="#416">416</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-06-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-06-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="417" href="#417">417</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-07-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-07-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="418" href="#418">418</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-08-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-08-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="419" href="#419">419</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-09-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-09-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="420" href="#420">420</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-10-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-10-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="421" href="#421">421</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-11-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-11-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="422" href="#422">422</a></td><td class="col-11 codeLine"><span class="default">round(max(if(month_key&nbsp;in&nbsp;('2016-12-01'),&nbsp;ifnull(avg_facility_bid,0),&nbsp;null)),3)&nbsp;as&nbsp;'2016-12-01'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="423" href="#423">423</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="424" href="#424">424</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;db_analytics.fact_account_manager_month&nbsp;lf</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="425" href="#425">425</a></td><td class="col-11 codeLine"><span class="default">join&nbsp;db_analytics.dim_account_manager&nbsp;dam&nbsp;on&nbsp;(lf.account_manager_key=dam.account_manager_key)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="426" href="#426">426</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="427" href="#427">427</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;lf.account_manager_key</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="428" href="#428">428</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="429" href="#429">429</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="430" href="#430">430</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="431" href="#431">431</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="432" href="#432">432</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="433" href="#433">433</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="434" href="#434">434</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="435" href="#435">435</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="436" href="#436">436</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="437" href="#437">437</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$sql</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="438" href="#438">438</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="439" href="#439">439</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

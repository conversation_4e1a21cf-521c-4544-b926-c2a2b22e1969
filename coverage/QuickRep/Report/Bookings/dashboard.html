<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickRep/Report/Bookings</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Report</a></li>
         <li class="breadcrumb-item"><a href="index.html">Bookings</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BookingErrors.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrors</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsRollup.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsRollup</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsDashboard.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BookingsDashboard</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BotBookings.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BotBookings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DailyBookingsPrediction.php.html#8">Sparefoot\PitaService\QuickRep\Report\Bookings\DailyBookingsPrediction</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DelayedBookingFollowUps.php.html#8">Sparefoot\PitaService\QuickRep\Report\Bookings\DelayedBookingFollowUps</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExperimentAbsoluteConversionRates.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\ExperimentAbsoluteConversionRates</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RescuedBookings.php.html#8">Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValetBookingAlert.php.html#8">Sparefoot\PitaService\QuickRep\Report\Bookings\ValetBookingAlert</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BookingErrors.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrors</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="BookingErrorsRollup.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsRollup</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="RescuedBookings.php.html#8">Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="BookingsDashboard.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BookingsDashboard</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="BotBookings.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\BotBookings</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ValetBookingAlert.php.html#8">Sparefoot\PitaService\QuickRep\Report\Bookings\ValetBookingAlert</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="ExperimentAbsoluteConversionRates.php.html#9">Sparefoot\PitaService\QuickRep\Report\Bookings\ExperimentAbsoluteConversionRates</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BookingErrors.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrors::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrors.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrors::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrors.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrors::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrors.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrors::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrors.php.html#55"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrors::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrors.php.html#98"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrors::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#36"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#41"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#46"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#51"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#73"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#155"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsRollup.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsRollup::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsRollup.php.html#44"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsRollup::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsRollup.php.html#49"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsRollup::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsRollup.php.html#54"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsRollup::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsRollup.php.html#71"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsRollup::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingErrorsRollup.php.html#139"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsRollup::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsDashboard.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingsDashboard::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsDashboard.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingsDashboard::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsDashboard.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingsDashboard::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsDashboard.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingsDashboard::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsDashboard.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingsDashboard::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BookingsDashboard.php.html#95"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingsDashboard::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BotBookings.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BotBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BotBookings.php.html#16"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BotBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BotBookings.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BotBookings::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BotBookings.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BotBookings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BotBookings.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BotBookings::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="BotBookings.php.html#66"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BotBookings::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DailyBookingsPrediction.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DailyBookingsPrediction::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DailyBookingsPrediction.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DailyBookingsPrediction::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DailyBookingsPrediction.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DailyBookingsPrediction::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DailyBookingsPrediction.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DailyBookingsPrediction::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DailyBookingsPrediction.php.html#32"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DailyBookingsPrediction::getCharts">getCharts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DailyBookingsPrediction.php.html#56"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DailyBookingsPrediction::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DelayedBookingFollowUps.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DelayedBookingFollowUps::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DelayedBookingFollowUps.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DelayedBookingFollowUps::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DelayedBookingFollowUps.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DelayedBookingFollowUps::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DelayedBookingFollowUps.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DelayedBookingFollowUps::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DelayedBookingFollowUps.php.html#33"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\DelayedBookingFollowUps::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExperimentAbsoluteConversionRates.php.html#13"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ExperimentAbsoluteConversionRates::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExperimentAbsoluteConversionRates.php.html#18"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ExperimentAbsoluteConversionRates::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExperimentAbsoluteConversionRates.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ExperimentAbsoluteConversionRates::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExperimentAbsoluteConversionRates.php.html#28"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ExperimentAbsoluteConversionRates::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExperimentAbsoluteConversionRates.php.html#45"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ExperimentAbsoluteConversionRates::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RescuedBookings.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RescuedBookings.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RescuedBookings.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RescuedBookings.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RescuedBookings.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings::doCleanup">doCleanup</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RescuedBookings.php.html#65"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RescuedBookings.php.html#157"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings::parse_query">parse_query</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValetBookingAlert.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ValetBookingAlert::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValetBookingAlert.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ValetBookingAlert::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValetBookingAlert.php.html#21"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ValetBookingAlert::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValetBookingAlert.php.html#27"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ValetBookingAlert::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValetBookingAlert.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ValetBookingAlert::getAlerts">getAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ValetBookingAlert.php.html#73"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ValetBookingAlert::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="BookingErrors.php.html#98"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrors::getSql">getSql</abbr></a></td><td class="text-right">72</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#73"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType::getSql">getSql</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="BookingErrorsRollup.php.html#71"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsRollup::getSql">getSql</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="BookingsDashboard.php.html#95"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingsDashboard::getSql">getSql</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="BotBookings.php.html#66"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BotBookings::getSql">getSql</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="RescuedBookings.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings::doCleanup">doCleanup</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ValetBookingAlert.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ValetBookingAlert::getAlerts">getAlerts</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="BookingErrorsByType.php.html#155"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\BookingErrorsByType::getCharts">getCharts</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ExperimentAbsoluteConversionRates.php.html#28"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\ExperimentAbsoluteConversionRates::getInputs">getInputs</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="RescuedBookings.php.html#157"><abbr title="Sparefoot\PitaService\QuickRep\Report\Bookings\RescuedBookings::parse_query">parse_query</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([10,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([59,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,13,"<a href=\"BookingErrors.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrors<\/a>"],[0,13,"<a href=\"BookingErrorsByType.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsByType<\/a>"],[0,11,"<a href=\"BookingErrorsRollup.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsRollup<\/a>"],[0,9,"<a href=\"BookingsDashboard.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingsDashboard<\/a>"],[0,9,"<a href=\"BotBookings.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BotBookings<\/a>"],[0,6,"<a href=\"DailyBookingsPrediction.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DailyBookingsPrediction<\/a>"],[0,5,"<a href=\"DelayedBookingFollowUps.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DelayedBookingFollowUps<\/a>"],[0,6,"<a href=\"ExperimentAbsoluteConversionRates.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ExperimentAbsoluteConversionRates<\/a>"],[0,11,"<a href=\"RescuedBookings.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\RescuedBookings<\/a>"],[0,8,"<a href=\"ValetBookingAlert.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ValetBookingAlert<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"BookingErrors.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrors::getCategory<\/a>"],[0,1,"<a href=\"BookingErrors.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrors::getName<\/a>"],[0,1,"<a href=\"BookingErrors.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrors::getDescription<\/a>"],[0,1,"<a href=\"BookingErrors.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrors::getInputs<\/a>"],[0,1,"<a href=\"BookingErrors.php.html#55\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrors::getCharts<\/a>"],[0,8,"<a href=\"BookingErrors.php.html#98\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrors::getSql<\/a>"],[0,1,"<a href=\"BookingErrorsByType.php.html#36\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsByType::getCategory<\/a>"],[0,1,"<a href=\"BookingErrorsByType.php.html#41\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsByType::getName<\/a>"],[0,1,"<a href=\"BookingErrorsByType.php.html#46\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsByType::getDescription<\/a>"],[0,1,"<a href=\"BookingErrorsByType.php.html#51\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsByType::getInputs<\/a>"],[0,7,"<a href=\"BookingErrorsByType.php.html#73\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsByType::getSql<\/a>"],[0,2,"<a href=\"BookingErrorsByType.php.html#155\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsByType::getCharts<\/a>"],[0,1,"<a href=\"BookingErrorsRollup.php.html#39\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsRollup::getCategory<\/a>"],[0,1,"<a href=\"BookingErrorsRollup.php.html#44\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsRollup::getName<\/a>"],[0,1,"<a href=\"BookingErrorsRollup.php.html#49\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsRollup::getDescription<\/a>"],[0,1,"<a href=\"BookingErrorsRollup.php.html#54\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsRollup::getInputs<\/a>"],[0,6,"<a href=\"BookingErrorsRollup.php.html#71\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsRollup::getSql<\/a>"],[0,1,"<a href=\"BookingErrorsRollup.php.html#139\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingErrorsRollup::getCharts<\/a>"],[0,1,"<a href=\"BookingsDashboard.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingsDashboard::getCategory<\/a>"],[0,1,"<a href=\"BookingsDashboard.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingsDashboard::getName<\/a>"],[0,1,"<a href=\"BookingsDashboard.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingsDashboard::getDescription<\/a>"],[0,1,"<a href=\"BookingsDashboard.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingsDashboard::getInputs<\/a>"],[0,1,"<a href=\"BookingsDashboard.php.html#39\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingsDashboard::getCharts<\/a>"],[0,4,"<a href=\"BookingsDashboard.php.html#95\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BookingsDashboard::getSql<\/a>"],[0,1,"<a href=\"BotBookings.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BotBookings::getCategory<\/a>"],[0,1,"<a href=\"BotBookings.php.html#16\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BotBookings::getName<\/a>"],[0,1,"<a href=\"BotBookings.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BotBookings::getDescription<\/a>"],[0,1,"<a href=\"BotBookings.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BotBookings::getInputs<\/a>"],[0,1,"<a href=\"BotBookings.php.html#39\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BotBookings::getCharts<\/a>"],[0,4,"<a href=\"BotBookings.php.html#66\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\BotBookings::getSql<\/a>"],[0,1,"<a href=\"DailyBookingsPrediction.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DailyBookingsPrediction::getCategory<\/a>"],[0,1,"<a href=\"DailyBookingsPrediction.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DailyBookingsPrediction::getName<\/a>"],[0,1,"<a href=\"DailyBookingsPrediction.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DailyBookingsPrediction::getDescription<\/a>"],[0,1,"<a href=\"DailyBookingsPrediction.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DailyBookingsPrediction::getInputs<\/a>"],[0,1,"<a href=\"DailyBookingsPrediction.php.html#32\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DailyBookingsPrediction::getCharts<\/a>"],[0,1,"<a href=\"DailyBookingsPrediction.php.html#56\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DailyBookingsPrediction::getSql<\/a>"],[0,1,"<a href=\"DelayedBookingFollowUps.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DelayedBookingFollowUps::getCategory<\/a>"],[0,1,"<a href=\"DelayedBookingFollowUps.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DelayedBookingFollowUps::getName<\/a>"],[0,1,"<a href=\"DelayedBookingFollowUps.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DelayedBookingFollowUps::getDescription<\/a>"],[0,1,"<a href=\"DelayedBookingFollowUps.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DelayedBookingFollowUps::getInputs<\/a>"],[0,1,"<a href=\"DelayedBookingFollowUps.php.html#33\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\DelayedBookingFollowUps::getSql<\/a>"],[0,1,"<a href=\"ExperimentAbsoluteConversionRates.php.html#13\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ExperimentAbsoluteConversionRates::getCategory<\/a>"],[0,1,"<a href=\"ExperimentAbsoluteConversionRates.php.html#18\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ExperimentAbsoluteConversionRates::getName<\/a>"],[0,1,"<a href=\"ExperimentAbsoluteConversionRates.php.html#23\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ExperimentAbsoluteConversionRates::getDescription<\/a>"],[0,2,"<a href=\"ExperimentAbsoluteConversionRates.php.html#28\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ExperimentAbsoluteConversionRates::getInputs<\/a>"],[0,1,"<a href=\"ExperimentAbsoluteConversionRates.php.html#45\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ExperimentAbsoluteConversionRates::getSql<\/a>"],[0,1,"<a href=\"RescuedBookings.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\RescuedBookings::getCategory<\/a>"],[0,1,"<a href=\"RescuedBookings.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\RescuedBookings::getName<\/a>"],[0,1,"<a href=\"RescuedBookings.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\RescuedBookings::getDescription<\/a>"],[0,1,"<a href=\"RescuedBookings.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\RescuedBookings::getInputs<\/a>"],[0,4,"<a href=\"RescuedBookings.php.html#39\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\RescuedBookings::doCleanup<\/a>"],[0,1,"<a href=\"RescuedBookings.php.html#65\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\RescuedBookings::getSql<\/a>"],[0,2,"<a href=\"RescuedBookings.php.html#157\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\RescuedBookings::parse_query<\/a>"],[0,1,"<a href=\"ValetBookingAlert.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ValetBookingAlert::getCategory<\/a>"],[0,1,"<a href=\"ValetBookingAlert.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ValetBookingAlert::getName<\/a>"],[0,1,"<a href=\"ValetBookingAlert.php.html#21\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ValetBookingAlert::getDescription<\/a>"],[0,1,"<a href=\"ValetBookingAlert.php.html#27\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ValetBookingAlert::getInputs<\/a>"],[0,3,"<a href=\"ValetBookingAlert.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ValetBookingAlert::getAlerts<\/a>"],[0,1,"<a href=\"ValetBookingAlert.php.html#73\">Sparefoot\\PitaService\\QuickRep\\Report\\Bookings\\ValetBookingAlert::getSql<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickRep/Report/Facilities</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Report</a></li>
         <li class="breadcrumb-item"><a href="index.html">Facilities</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DuplicateNetworkFacilities.php.html#8">Sparefoot\PitaService\QuickRep\Report\Facilities\DuplicateNetworkFacilities</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#8">Sparefoot\PitaService\QuickRep\Report\Facilities\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenUnitsByFacility.php.html#8">Sparefoot\PitaService\QuickRep\Report\Facilities\HiddenUnitsByFacility</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NonLiveFacilities.php.html#7">Sparefoot\PitaService\QuickRep\Report\Facilities\NonLiveFacilities</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#10">Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#10">Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities</a></td><td class="text-right">156</td></tr>
       <tr><td><a href="DuplicateNetworkFacilities.php.html#8">Sparefoot\PitaService\QuickRep\Report\Facilities\DuplicateNetworkFacilities</a></td><td class="text-right">56</td></tr>
       <tr><td><a href="FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#8">Sparefoot\PitaService\QuickRep\Report\Facilities\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="HiddenUnitsByFacility.php.html#8">Sparefoot\PitaService\QuickRep\Report\Facilities\HiddenUnitsByFacility</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="DuplicateNetworkFacilities.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\DuplicateNetworkFacilities::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DuplicateNetworkFacilities.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\DuplicateNetworkFacilities::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DuplicateNetworkFacilities.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\DuplicateNetworkFacilities::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DuplicateNetworkFacilities.php.html#31"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\DuplicateNetworkFacilities::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DuplicateNetworkFacilities.php.html#40"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\DuplicateNetworkFacilities::getAlerts">getAlerts</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DuplicateNetworkFacilities.php.html#71"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\DuplicateNetworkFacilities::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenUnitsByFacility.php.html#10"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\HiddenUnitsByFacility::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenUnitsByFacility.php.html#15"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\HiddenUnitsByFacility::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenUnitsByFacility.php.html#20"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\HiddenUnitsByFacility::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenUnitsByFacility.php.html#25"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\HiddenUnitsByFacility::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="HiddenUnitsByFacility.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\HiddenUnitsByFacility::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NonLiveFacilities.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\NonLiveFacilities::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NonLiveFacilities.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\NonLiveFacilities::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NonLiveFacilities.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\NonLiveFacilities::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NonLiveFacilities.php.html#26"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\NonLiveFacilities::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="NonLiveFacilities.php.html#31"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\NonLiveFacilities::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities::showInProduction">showInProduction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#24"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#29"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#43"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#78"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities::prepareParameters">prepareParameters</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#85"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities::getSql">getSql</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#85"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities::getSql">getSql</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="DuplicateNetworkFacilities.php.html#40"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\DuplicateNetworkFacilities::getAlerts">getAlerts</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="HiddenUnitsByFacility.php.html#39"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\HiddenUnitsByFacility::getSql">getSql</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="SupplementalFacilityAmenities.php.html#78"><abbr title="Sparefoot\PitaService\QuickRep\Report\Facilities\SupplementalFacilityAmenities::prepareParameters">prepareParameters</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([28,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,7,"<a href=\"DuplicateNetworkFacilities.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\DuplicateNetworkFacilities<\/a>"],[0,6,"<a href=\"FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials<\/a>"],[0,6,"<a href=\"HiddenUnitsByFacility.php.html#8\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\HiddenUnitsByFacility<\/a>"],[0,5,"<a href=\"NonLiveFacilities.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\NonLiveFacilities<\/a>"],[0,12,"<a href=\"SupplementalFacilityAmenities.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\SupplementalFacilityAmenities<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"DuplicateNetworkFacilities.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\DuplicateNetworkFacilities::getCategory<\/a>"],[0,1,"<a href=\"DuplicateNetworkFacilities.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\DuplicateNetworkFacilities::getName<\/a>"],[0,1,"<a href=\"DuplicateNetworkFacilities.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\DuplicateNetworkFacilities::getDescription<\/a>"],[0,1,"<a href=\"DuplicateNetworkFacilities.php.html#31\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\DuplicateNetworkFacilities::getInputs<\/a>"],[0,2,"<a href=\"DuplicateNetworkFacilities.php.html#40\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\DuplicateNetworkFacilities::getAlerts<\/a>"],[0,1,"<a href=\"DuplicateNetworkFacilities.php.html#71\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\DuplicateNetworkFacilities::getSql<\/a>"],[0,1,"<a href=\"FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getCategory<\/a>"],[0,1,"<a href=\"FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getName<\/a>"],[0,1,"<a href=\"FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getDescription<\/a>"],[0,1,"<a href=\"FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getInputs<\/a>"],[0,2,"<a href=\"FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials.php.html#39\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\FacilitiesWithPhonesOrUrlsInDescrptionsOrSpecials::getSql<\/a>"],[0,1,"<a href=\"HiddenUnitsByFacility.php.html#10\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\HiddenUnitsByFacility::getCategory<\/a>"],[0,1,"<a href=\"HiddenUnitsByFacility.php.html#15\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\HiddenUnitsByFacility::getName<\/a>"],[0,1,"<a href=\"HiddenUnitsByFacility.php.html#20\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\HiddenUnitsByFacility::getDescription<\/a>"],[0,1,"<a href=\"HiddenUnitsByFacility.php.html#25\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\HiddenUnitsByFacility::getInputs<\/a>"],[0,2,"<a href=\"HiddenUnitsByFacility.php.html#39\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\HiddenUnitsByFacility::getSql<\/a>"],[0,1,"<a href=\"NonLiveFacilities.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\NonLiveFacilities::getCategory<\/a>"],[0,1,"<a href=\"NonLiveFacilities.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\NonLiveFacilities::getName<\/a>"],[0,1,"<a href=\"NonLiveFacilities.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\NonLiveFacilities::getDescription<\/a>"],[0,1,"<a href=\"NonLiveFacilities.php.html#26\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\NonLiveFacilities::getInputs<\/a>"],[0,1,"<a href=\"NonLiveFacilities.php.html#31\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\NonLiveFacilities::getSql<\/a>"],[0,1,"<a href=\"SupplementalFacilityAmenities.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\SupplementalFacilityAmenities::getCategory<\/a>"],[0,1,"<a href=\"SupplementalFacilityAmenities.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\SupplementalFacilityAmenities::showInProduction<\/a>"],[0,1,"<a href=\"SupplementalFacilityAmenities.php.html#24\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\SupplementalFacilityAmenities::getName<\/a>"],[0,1,"<a href=\"SupplementalFacilityAmenities.php.html#29\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\SupplementalFacilityAmenities::getDescription<\/a>"],[0,1,"<a href=\"SupplementalFacilityAmenities.php.html#43\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\SupplementalFacilityAmenities::getInputs<\/a>"],[0,2,"<a href=\"SupplementalFacilityAmenities.php.html#78\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\SupplementalFacilityAmenities::prepareParameters<\/a>"],[0,5,"<a href=\"SupplementalFacilityAmenities.php.html#85\">Sparefoot\\PitaService\\QuickRep\\Report\\Facilities\\SupplementalFacilityAmenities::getSql<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

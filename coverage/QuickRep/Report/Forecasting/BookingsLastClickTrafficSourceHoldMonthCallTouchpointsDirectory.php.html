<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/service/src/QuickRep/Report/Forecasting/BookingsLastClickTrafficSourceHoldMonthCallTouchpointsDirectory.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Report</a></li>
         <li class="breadcrumb-item"><a href="index.html">Forecasting</a></li>
         <li class="breadcrumb-item active">BookingsLastClickTrafficSourceHoldMonthCallTouchpointsDirectory.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;26</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;7</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="Sparefoot\PitaService\QuickRep\Report\Forecasting\BookingsLastClickTrafficSourceHoldMonthCallTouchpointsDirectory">BookingsLastClickTrafficSourceHoldMonthCallTouchpointsDirectory</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;26</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;7</div></td>
       <td class="danger small">90</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#12"><abbr title="getCategory()">getCategory</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#17"><abbr title="getName()">getName</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#22"><abbr title="showInProduction()">showInProduction</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#27"><abbr title="getDescription()">getDescription</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#41"><abbr title="getInputs()">getInputs</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;10</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#58"><abbr title="prepareParameters(array $parameters)">prepareParameters</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;3</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#66"><abbr title="getSql(array $parameters)">getSql</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;8</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">6</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report\Forecasting</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Input\QuerySelectInput</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">BookingsLastClickTrafficSourceHoldMonthCallTouchpointsDirectory</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">Report</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="default">$siteId</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getCategory</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'Forecasting'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getName</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'31.&nbsp;Bookings&nbsp;By&nbsp;Last&nbsp;Click&nbsp;Traffic&nbsp;Source&nbsp;and&nbsp;Hold&nbsp;Month&nbsp;for&nbsp;Call&nbsp;Touchpoints&nbsp;and&nbsp;Directory&nbsp;Facility&nbsp;Traffic'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">showInProduction</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getDescription</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'&lt;b&gt;&nbsp;Call&nbsp;Center&nbsp;booking&nbsp;counts&nbsp;by&nbsp;last&nbsp;click&nbsp;traffic_source&nbsp;by&nbsp;month&lt;/b&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;Metrics&nbsp;in&nbsp;this&nbsp;report&nbsp;are&nbsp;based&nbsp;on&nbsp;visits&nbsp;that&nbsp;landed&nbsp;on&nbsp;a&nbsp;directory&nbsp;facility&nbsp;page.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SelfStorage&nbsp;-&nbsp;Encompasses&nbsp;only&nbsp;the&nbsp;www.selfstorage.com&nbsp;site&nbsp;and&nbsp;the&nbsp;m.selfstorage.com&nbsp;site&nbsp;(no&nbsp;ppc&nbsp;or&nbsp;cobrands)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;SpareFoot&nbsp;-&nbsp;Encompasses&nbsp;only&nbsp;the&nbsp;www.sparefoot.com&nbsp;site&nbsp;and&nbsp;the&nbsp;m.sparefoot.com&nbsp;site&nbsp;(no&nbsp;ppc&nbsp;or&nbsp;cobrands)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getInputs</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'site_id'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">QuerySelectInput</span><span class="keyword">(</span><span class="default">'Site'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'SELECT&nbsp;site_id,&nbsp;title&nbsp;as&nbsp;site,&nbsp;affiliate_id</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;FROM&nbsp;sparefoot.sites&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;where&nbsp;site_id&nbsp;in&nbsp;(27,144,-1,59,&nbsp;177,173,180,204,205)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;if(title&nbsp;in&nbsp;(&quot;Self&nbsp;Storage&nbsp;Testing&nbsp;Number&nbsp;Version&nbsp;1&quot;,&quot;Self&nbsp;Storage&nbsp;Testing&nbsp;Number&nbsp;Version&nbsp;2&quot;),1,0)&nbsp;desc&nbsp;,affiliate_id&nbsp;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;;'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'site_id'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'site'</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">getDao</span><span class="keyword">(</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">false</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">prepareParameters</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">&amp;</span><span class="default">$parameters</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="keyword">]</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">if</span><span class="default">&nbsp;</span><span class="keyword">(</span><span class="keyword">isset</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'site_id'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">)</span><span class="default">&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">siteId</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="default">'&quot;'</span><span class="keyword">.</span><span class="default">implode</span><span class="keyword">(</span><span class="default">'&quot;,&quot;'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'site_id'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'&quot;'</span><span class="keyword">;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">unset</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'site_id'</span><span class="keyword">]</span><span class="keyword">)</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getSql</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$parameters</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">$sql</span><span class="default">&nbsp;</span><span class="keyword">=</span><span class="default">&nbsp;</span><span class="string">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;concat_ws(',',</span><span class="string">$this</span><span class="string">-&gt;</span><span class="string">siteId</span><span class="string">)&nbsp;as&nbsp;site_ids,&nbsp;ts.traffic_source_op_id&nbsp;as&nbsp;traffic_source_id,&nbsp;raw.*</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="string">from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="string">db_analytics.dim_traffic_source&nbsp;ts</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="string">left&nbsp;join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="string">(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="string">select&nbsp;ifnull(last_click_traffic_source_id,&nbsp;'Unknown')&nbsp;as&nbsp;traffic_sources_represented_in_data,&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;--&nbsp;2010</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-01-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-02-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-02-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-03-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-02',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-03-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-04-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-03',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-04-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-05-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-04',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-05-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-06-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-05',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-06-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-07-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-06',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-07-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-08-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-07',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-08-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-09-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-08',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-09-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-10-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-09',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-10-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-11-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-10',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-11-01'&nbsp;and&nbsp;month&nbsp;&lt;'2010-12-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-11',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2010-12-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-01-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2010-12',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="string">--&nbsp;2011&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-01-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-02-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-02-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-03-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-02',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-03-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-04-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-03',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-04-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-05-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-04',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-05-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-06-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-05',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-06-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-07-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-06',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-07-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-08-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-07',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-08-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-09-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-08',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-09-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-10-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-09',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-10-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-11-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-10',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-11-01'&nbsp;and&nbsp;month&nbsp;&lt;'2011-12-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-11',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2011-12-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-01-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2011-12',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;--&nbsp;2012&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-01-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-02-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-02-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-03-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-02',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-03-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-04-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-03',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-04-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-05-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-04',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-05-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-06-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-05',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-06-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-07-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-06',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-07-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-08-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-07',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-08-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-09-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-08',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-09-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-10-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-09',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-10-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-11-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-10',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-11-01'&nbsp;and&nbsp;month&nbsp;&lt;'2012-12-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-11',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2012-12-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-01-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2012-12',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;--&nbsp;2013</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-01-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-02-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-02-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-03-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-02',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-03-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-04-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-03',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-04-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-05-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-04',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-05-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-06-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-05',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-06-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-07-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-06',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-07-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-08-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-07',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-08-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-09-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-08',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-09-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-10-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-09',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-10-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-11-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-10',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-11-01'&nbsp;and&nbsp;month&nbsp;&lt;'2013-12-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-11',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2013-12-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-01-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2013-12',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-01-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-02-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-02-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-03-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-02',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-03-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-04-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-03',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-04-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-05-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-04',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-05-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-06-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-05',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-06-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-07-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-06',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-07-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-08-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-07',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-08-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-09-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-08',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-09-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-10-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-09',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-10-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-11-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-10',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-11-01'&nbsp;and&nbsp;month&nbsp;&lt;'2014-12-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-11',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2014-12-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-01-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2014-12',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-01-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-02-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-01',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-02-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-03-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-02',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-03-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-04-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-03',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-04-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-05-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-04',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-05-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-06-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-05',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-06-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-07-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-06',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-07-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-08-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-07',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-08-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-09-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-08',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-09-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-10-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-09',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-10-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-11-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-10',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-11-01'&nbsp;and&nbsp;month&nbsp;&lt;'2015-12-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-11',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;sum(&nbsp;if(month&nbsp;&gt;=&nbsp;'2015-12-01'&nbsp;and&nbsp;month&nbsp;&lt;'2016-01-01',&nbsp;cc_bookings_hold_date_directory,&nbsp;null))&nbsp;as&nbsp;'2015-12'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="string">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;from</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="string">db_analytics.forecasting_fact_table&nbsp;lrs</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"><span class="string">where&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"><span class="string">1=1</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="string">&quot;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$this</span><span class="default">-&gt;</span><span class="default">siteId</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="string">&quot;</span><span class="string">&nbsp;AND&nbsp;lrs.site_id&nbsp;in&nbsp;(</span><span class="string">{</span><span class="string">$this</span><span class="string">-&gt;</span><span class="string">siteId</span><span class="keyword">}</span><span class="string">)</span><span class="string">&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">''</span><span class="keyword">)</span><span class="keyword">.</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;traffic_sources_represented_in_data</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;)raw</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"><span class="default">&nbsp;on&nbsp;(ifnull(raw.traffic_sources_represented_in_data,'unknown')&nbsp;=&nbsp;ts.traffic_source_op_id)&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;order&nbsp;by&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;if(</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ifnull(traffic_source_id,&nbsp;'null&nbsp;traffic&nbsp;source')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;in&nbsp;&nbsp;&nbsp;&nbsp;('google-organic',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'yahoo-organic',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'bing-organic',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'__Direct',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'__Referred',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'null&nbsp;traffic&nbsp;source',</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;'unknown')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;or&nbsp;traffic_source_id&nbsp;is&nbsp;null</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;1,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;0)&nbsp;desc,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;ts.traffic_source_key&nbsp;asc&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">$sql</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

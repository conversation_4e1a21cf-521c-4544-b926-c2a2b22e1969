<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Code Coverage for /var/www/service/src/QuickRep/Report/FacilityStateChanges.php</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/octicons.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="index.html">Report</a></li>
         <li class="breadcrumb-item active">FacilityStateChanges.php</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="table-responsive">
    <table class="table table-bordered">
     <thead>
      <tr>
       <td>&nbsp;</td>
       <td colspan="10"><div align="center"><strong>Code Coverage</strong></div></td>
      </tr>
      <tr>
       <td>&nbsp;</td>
       <td colspan="3"><div align="center"><strong>Lines</strong></div></td>
       <td colspan="4"><div align="center"><strong>Functions and Methods</strong></div></td>
       <td colspan="3"><div align="center"><strong>Classes and Traits</strong></div></td>
      </tr>
     </thead>
     <tbody>
      <tr>
       <td class="danger">Total</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;87</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;6</div></td>
       <td class="danger small"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger"><abbr title="Sparefoot\PitaService\QuickRep\Report\FacilityStateChanges">FacilityStateChanges</abbr></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;87</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;6</div></td>
       <td class="danger small">5402</td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#11"><abbr title="getCategory()">getCategory</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#16"><abbr title="getName()">getName</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#21"><abbr title="getDescription()">getDescription</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;2</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#59"><abbr title="showInProduction()">showInProduction</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#64"><abbr title="getInputs()">getInputs</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;8</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">2</td>
       <td class="danger" colspan="3"></td>
      </tr>

      <tr>
       <td class="danger">&nbsp;<a href="#76"><abbr title="getSql(array $parameters)">getSql</abbr></a></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;74</div></td>
       <td class="danger big">       <div class="progress">
         <div class="progress-bar bg-danger" role="progressbar" aria-valuenow="0.00" aria-valuemin="0" aria-valuemax="100" style="width: 0.00%">
           <span class="sr-only">0.00% covered (danger)</span>
         </div>
       </div>
</td>
       <td class="danger small"><div align="right">0.00%</div></td>
       <td class="danger small"><div align="right">0&nbsp;/&nbsp;1</div></td>
       <td class="danger small">4692</td>
       <td class="danger" colspan="3"></td>
      </tr>


     </tbody>
    </table>
   </div>
<table id="code" class="table table-borderless table-condensed">
<tbody>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="1" href="#1">1</a></td><td class="col-11 codeLine"><span class="default">&lt;?php</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="2" href="#2">2</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="3" href="#3">3</a></td><td class="col-11 codeLine"><span class="keyword">namespace</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="4" href="#4">4</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="5" href="#5">5</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Input\ArraySelectInput</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="6" href="#6">6</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Input\DateInput</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="7" href="#7">7</a></td><td class="col-11 codeLine"><span class="keyword">use</span><span class="default">&nbsp;</span><span class="default">Sparefoot\PitaService\QuickRep\Report</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="8" href="#8">8</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="9" href="#9">9</a></td><td class="col-11 codeLine"><span class="keyword">class</span><span class="default">&nbsp;</span><span class="default">FacilityStateChanges</span><span class="default">&nbsp;</span><span class="keyword">extends</span><span class="default">&nbsp;</span><span class="default">Report</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="10" href="#10">10</a></td><td class="col-11 codeLine"><span class="keyword">{</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="11" href="#11">11</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getCategory</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="12" href="#12">12</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="13" href="#13">13</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'Facility&nbsp;Live-ness'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="14" href="#14">14</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="15" href="#15">15</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="16" href="#16">16</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getName</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="17" href="#17">17</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="18" href="#18">18</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'Facility&nbsp;State&nbsp;Changes'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="19" href="#19">19</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="20" href="#20">20</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="21" href="#21">21</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getDescription</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="22" href="#22">22</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="23" href="#23">23</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">&quot;&lt;b&gt;Displays&nbsp;the&nbsp;number&nbsp;of&nbsp;facilities&nbsp;moving&nbsp;to&nbsp;each&nbsp;facility&nbsp;state&lt;/b&gt;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="24" href="#24">24</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;Note&nbsp;that&nbsp;the&nbsp;data&nbsp;is&nbsp;in&nbsp;monthly&nbsp;increments&nbsp;until&nbsp;2013-10-01.&nbsp;&nbsp;Data&nbsp;is&nbsp;gathered&nbsp;from&nbsp;a&nbsp;snapshot&nbsp;taken&nbsp;the&nbsp;morning&nbsp;of&nbsp;the&nbsp;date&nbsp;listed.&nbsp;&nbsp;Daily&nbsp;snapshots&nbsp;were&nbsp;not&nbsp;instituted&nbsp;until&nbsp;2013-10-01.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="25" href="#25">25</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="26" href="#26">26</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;&lt;b&gt;Monthly&nbsp;View:&lt;/b&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="27" href="#27">27</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;Click&nbsp;'Yes'&nbsp;in&nbsp;the&nbsp;'Monthly?'&nbsp;parameter&nbsp;to&nbsp;display&nbsp;facilities&nbsp;changing&nbsp;to&nbsp;the&nbsp;specified&nbsp;state&nbsp;over&nbsp;the&nbsp;time&nbsp;period&nbsp;from&nbsp;the&nbsp;1st&nbsp;of&nbsp;the&nbsp;previous&nbsp;month&nbsp;to&nbsp;the&nbsp;date&nbsp;listed.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="28" href="#28">28</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;&nbsp;For&nbsp;example,&nbsp;the&nbsp;'Live'&nbsp;values&nbsp;for&nbsp;the&nbsp;Date&nbsp;2013-10-01&nbsp;represent&nbsp;faciliites&nbsp;that&nbsp;were&nbsp;Live&nbsp;on&nbsp;2013-10-01&nbsp;but&nbsp;were&nbsp;not&nbsp;live&nbsp;2013-09-01&nbsp;the&nbsp;month&nbsp;before.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="29" href="#29">29</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="30" href="#30">30</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;De-select&nbsp;'Yes'&nbsp;in&nbsp;the&nbsp;'Monthly?'&nbsp;parameter&nbsp;to&nbsp;display&nbsp;state&nbsp;changes&nbsp;by&nbsp;day.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="31" href="#31">31</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;&nbsp;In&nbsp;the&nbsp;daily&nbsp;view,&nbsp;the&nbsp;'Live'&nbsp;values&nbsp;for&nbsp;the&nbsp;Date&nbsp;2013-10-10&nbsp;represent&nbsp;facilities&nbsp;that&nbsp;were&nbsp;Live&nbsp;on&nbsp;2013-10-10&nbsp;but&nbsp;were&nbsp;not&nbsp;live&nbsp;2013-10-9&nbsp;the&nbsp;day&nbsp;before.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="32" href="#32">32</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="33" href="#33">33</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;&lt;b&gt;Detailed&nbsp;View:&lt;/b&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="34" href="#34">34</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;Detailed&nbsp;view&nbsp;will&nbsp;breakout&nbsp;each&nbsp;column&nbsp;by&nbsp;'Virgin'&nbsp;or&nbsp;'Switch'.&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="35" href="#35">35</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;Virgin&nbsp;-&nbsp;Represents&nbsp;truly&nbsp;new&nbsp;facilities&nbsp;that&nbsp;did&nbsp;not&nbsp;exist&nbsp;in&nbsp;the&nbsp;previous&nbsp;timespan.&nbsp;&nbsp;This&nbsp;is&nbsp;their&nbsp;first&nbsp;state.&nbsp;For&nbsp;example&nbsp;in&nbsp;the&nbsp;monthly&nbsp;view,&nbsp;&nbsp;A&nbsp;Virgin&nbsp;Live&nbsp;facility&nbsp;for&nbsp;2013-10-01&nbsp;did&nbsp;not&nbsp;exist&nbsp;on&nbsp;2013-09-01.&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="36" href="#36">36</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;Switch&nbsp;-&nbsp;Represnts&nbsp;facilities&nbsp;that&nbsp;were&nbsp;in&nbsp;a&nbsp;previous&nbsp;state&nbsp;before&nbsp;and&nbsp;have&nbsp;switched&nbsp;to&nbsp;this&nbsp;new&nbsp;facility&nbsp;state.&nbsp;For&nbsp;example&nbsp;in&nbsp;the&nbsp;monthly&nbsp;view,&nbsp;A&nbsp;Switch&nbsp;Live&nbsp;facility&nbsp;for&nbsp;2013-10-01&nbsp;was&nbsp;in&nbsp;a&nbsp;different&nbsp;state&nbsp;other&nbsp;than&nbsp;Live&nbsp;on&nbsp;2013-09-01.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="37" href="#37">37</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="38" href="#38">38</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;&lt;b&gt;Columns:&lt;/b&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="39" href="#39">39</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;ul&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="40" href="#40">40</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Live&nbsp;-&nbsp;Facilities&nbsp;that&nbsp;are&nbsp;able&nbsp;to&nbsp;be&nbsp;displayed&nbsp;in&nbsp;search&nbsp;results&nbsp;and&nbsp;receive&nbsp;reservations&nbsp;on&nbsp;the&nbsp;AdNetwork.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="41" href="#41">41</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Hidden&nbsp;-&nbsp;Facilities&nbsp;that&nbsp;are&nbsp;unable&nbsp;to&nbsp;be&nbsp;displayed&nbsp;in&nbsp;search&nbsp;results&nbsp;and&nbsp;unable&nbsp;receive&nbsp;reservations&nbsp;because&nbsp;they're&nbsp;facility&nbsp;is&nbsp;hidden&nbsp;or&nbsp;because&nbsp;they&nbsp;do&nbsp;not&nbsp;have&nbsp;live&nbsp;units.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="42" href="#42">42</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;-&nbsp;Hidden&nbsp;facilities&nbsp;that&nbsp;are&nbsp;hidden&nbsp;due&nbsp;to&nbsp;only&nbsp;their&nbsp;facility&nbsp;being&nbsp;off.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="43" href="#43">43</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units&nbsp;-&nbsp;Hidden&nbsp;facilities&nbsp;that&nbsp;are&nbsp;hidden&nbsp;due&nbsp;to&nbsp;only&nbsp;the&nbsp;fact&nbsp;that&nbsp;they&nbsp;do&nbsp;not&nbsp;have&nbsp;live&nbsp;units.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="44" href="#44">44</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units&nbsp;-&nbsp;Hidden&nbsp;facilities&nbsp;that&nbsp;are&nbsp;hidden&nbsp;due&nbsp;to&nbsp;their&nbsp;facility&nbsp;being&nbsp;off&nbsp;AND&nbsp;the&nbsp;fact&nbsp;that&nbsp;they&nbsp;do&nbsp;not&nbsp;have&nbsp;live&nbsp;units.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="45" href="#45">45</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Cancelled&nbsp;-&nbsp;Facilities&nbsp;with&nbsp;an&nbsp;account&nbsp;status&nbsp;of&nbsp;'cancelled'&nbsp;that&nbsp;have&nbsp;been&nbsp;deactivated&nbsp;by&nbsp;SpareFoot.&nbsp;These&nbsp;facilities&nbsp;have&nbsp;'quit'&nbsp;the&nbsp;AdNetwork.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="46" href="#46">46</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Suspended&nbsp;-&nbsp;Facilities&nbsp;with&nbsp;an&nbsp;account&nbsp;status&nbsp;of&nbsp;'suspended'&nbsp;that&nbsp;have&nbsp;been&nbsp;deactivated&nbsp;by&nbsp;SpareFoot.&nbsp;These&nbsp;facilities&nbsp;have&nbsp;been&nbsp;suspended&nbsp;from&nbsp;showing&nbsp;on&nbsp;the&nbsp;AdNetwork&nbsp;due&nbsp;to&nbsp;billing&nbsp;issues.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="47" href="#47">47</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Removed&nbsp;-&nbsp;Facilities&nbsp;with&nbsp;an&nbsp;account&nbsp;status&nbsp;of&nbsp;'Removed'&nbsp;that&nbsp;have&nbsp;been&nbsp;deactivated&nbsp;by&nbsp;SpareFoot.&nbsp;SpareFoot&nbsp;has&nbsp;'fired'&nbsp;these&nbsp;facilities.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="48" href="#48">48</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Sf&nbsp;deactivated&nbsp;-&nbsp;Facilities&nbsp;without&nbsp;an&nbsp;account&nbsp;status&nbsp;of&nbsp;cancelled,&nbsp;removed,&nbsp;or&nbsp;suspended&nbsp;that&nbsp;have&nbsp;been&nbsp;deactivated&nbsp;by&nbsp;SpareFoot.&nbsp;&nbsp;These&nbsp;facilities&nbsp;are&nbsp;mostly&nbsp;integration&nbsp;copies&nbsp;and&nbsp;facilities&nbsp;that&nbsp;have&nbsp;been&nbsp;sold&nbsp;off&nbsp;to&nbsp;another&nbsp;account.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="49" href="#49">49</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Incomplete&nbsp;-&nbsp;Facilities&nbsp;that&nbsp;do&nbsp;not&nbsp;fit&nbsp;in&nbsp;any&nbsp;of&nbsp;the&nbsp;above&nbsp;states&nbsp;and&nbsp;have&nbsp;not&nbsp;received&nbsp;any&nbsp;search&nbsp;impressions.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="50" href="#50">50</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;li&gt;Other&nbsp;-&nbsp;Facilities&nbsp;that&nbsp;do&nbsp;not&nbsp;fit&nbsp;in&nbsp;any&nbsp;of&nbsp;the&nbsp;above&nbsp;states.</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="51" href="#51">51</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;/ul&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="52" href="#52">52</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;Note:&nbsp;'Hidden'&nbsp;is&nbsp;the&nbsp;sum&nbsp;of&nbsp;the&nbsp;three&nbsp;'Hidden&nbsp;Type'&nbsp;columns&nbsp;(Facility&nbsp;off,&nbsp;No&nbsp;live&nbsp;units,&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units).</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="53" href="#53">53</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="54" href="#54">54</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="55" href="#55">55</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&lt;br&gt;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="56" href="#56">56</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&quot;</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="57" href="#57">57</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="58" href="#58">58</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="59" href="#59">59</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">showInProduction</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="60" href="#60">60</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="61" href="#61">61</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">true</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="62" href="#62">62</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="63" href="#63">63</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="64" href="#64">64</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">public</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getInputs</span><span class="keyword">(</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="65" href="#65">65</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="66" href="#66">66</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="67" href="#67">67</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">[</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="68" href="#68">68</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'START_TIME'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">DateInput</span><span class="keyword">(</span><span class="default">'Start&nbsp;Date'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\DateTime</span><span class="keyword">(</span><span class="default">date</span><span class="keyword">(</span><span class="default">'2013-03-01'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="69" href="#69">69</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'END_TIME'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">DateInput</span><span class="keyword">(</span><span class="default">'End&nbsp;Date'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">\DateTime</span><span class="keyword">(</span><span class="default">date</span><span class="keyword">(</span><span class="default">'Y-m-d'</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="70" href="#70">70</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'MONTH_VIEW'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ArraySelectInput</span><span class="keyword">(</span><span class="default">'Month&nbsp;View?'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'1'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'Yes'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'0'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'No'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">false</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="71" href="#71">71</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">'DETAILED_VIEW'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="keyword">new</span><span class="default">&nbsp;</span><span class="default">ArraySelectInput</span><span class="keyword">(</span><span class="default">'Detailed&nbsp;View?'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="keyword">[</span><span class="default">'0'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'No'</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">'1'</span><span class="default">&nbsp;</span><span class="default">=&gt;</span><span class="default">&nbsp;</span><span class="default">'Yes'</span><span class="keyword">]</span><span class="keyword">,</span><span class="default">&nbsp;</span><span class="default">false</span><span class="keyword">)</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="72" href="#72">72</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">,</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="73" href="#73">73</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">]</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="74" href="#74">74</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="75" href="#75">75</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="76" href="#76">76</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">protected</span><span class="default">&nbsp;</span><span class="keyword">function</span><span class="default">&nbsp;</span><span class="default">getSql</span><span class="keyword">(</span><span class="keyword">array</span><span class="default">&nbsp;</span><span class="default">$parameters</span><span class="keyword">)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="77" href="#77">77</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">{</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="78" href="#78">78</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">return</span><span class="default">&nbsp;</span><span class="default">'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="79" href="#79">79</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="80" href="#80">80</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'MONTH_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="81" href="#81">81</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">&quot;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="82" href="#82">82</a></td><td class="col-11 codeLine"><span class="default">--&nbsp;Monthly&nbsp;view</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="83" href="#83">83</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="84" href="#84">84</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="85" href="#85">85</a></td><td class="col-11 codeLine"><span class="default">ffl2.date&nbsp;as&nbsp;Date,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="86" href="#86">86</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Live',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Live',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="87" href="#87">87</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Live',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;as&nbsp;'Virgin&nbsp;live',&nbsp;&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="88" href="#88">88</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Live',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;as&nbsp;'Switch&nbsp;Live',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="89" href="#89">89</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="90" href="#90">90</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="91" href="#91">91</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;in&nbsp;('Facility&nbsp;off',&nbsp;'No&nbsp;live&nbsp;units',&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units')&nbsp;,&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="92" href="#92">92</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;in&nbsp;('Facility&nbsp;off',&nbsp;'No&nbsp;live&nbsp;units',&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units')&nbsp;,&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="93" href="#93">93</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;in&nbsp;('Facility&nbsp;off',&nbsp;'No&nbsp;live&nbsp;units',&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units')&nbsp;,&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="94" href="#94">94</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="95" href="#95">95</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden&nbsp;-&nbsp;Facility&nbsp;off',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="96" href="#96">96</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="97" href="#97">97</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="98" href="#98">98</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="99" href="#99">99</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="100" href="#100">100</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'No&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="101" href="#101">101</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'No&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="102" href="#102">102</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'No&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="103" href="#103">103</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="104" href="#104">104</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="105" href="#105">105</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="106" href="#106">106</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="107" href="#107">107</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="108" href="#108">108</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="109" href="#109">109</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="110" href="#110">110</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Cancelled',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Cancelled',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="111" href="#111">111</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Cancelled',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Cancelled',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="112" href="#112">112</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Cancelled',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Cancelled',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="113" href="#113">113</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="114" href="#114">114</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Suspended',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Suspended',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="115" href="#115">115</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Suspended',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Suspended',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="116" href="#116">116</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Suspended',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Suspended',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="117" href="#117">117</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="118" href="#118">118</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Removed',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Removed',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="119" href="#119">119</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Removed',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Removed',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="120" href="#120">120</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Removed',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Removed',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="121" href="#121">121</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="122" href="#122">122</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="123" href="#123">123</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'SF&nbsp;deactivated',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'SF&nbsp;deactivated',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="124" href="#124">124</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'SF&nbsp;deactivated',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;SF&nbsp;deactivated',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="125" href="#125">125</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'SF&nbsp;deactivated',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;SF&nbsp;deactivated',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="126" href="#126">126</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="127" href="#127">127</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Incomplete',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Incomplete',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="128" href="#128">128</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Incomplete',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Incomplete',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="129" href="#129">129</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Incomplete',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Incomplete',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="130" href="#130">130</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="131" href="#131">131</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'other',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Other'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="132" href="#132">132</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;,count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'other',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Other',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="133" href="#133">133</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'other',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Other'&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="134" href="#134">134</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="135" href="#135">135</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="136" href="#136">136</a></td><td class="col-11 codeLine"><span class="default">db_analytics.facility_flags_log&nbsp;ffl2</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="137" href="#137">137</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="138" href="#138">138</a></td><td class="col-11 codeLine"><span class="default">db_analytics.facility_flags_log&nbsp;ffl</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="139" href="#139">139</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="140" href="#140">140</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(ffl2.listing_avail_id=ffl.listing_avail_id&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="141" href="#141">141</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;ffl.date=&nbsp;date_sub(ffl2.date,&nbsp;interval&nbsp;1&nbsp;month)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="142" href="#142">142</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;ffl.date=&nbsp;date_format(ffl.date,'%Y-%m-01')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="143" href="#143">143</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="144" href="#144">144</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="145" href="#145">145</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="146" href="#146">146</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;ffl2.date&nbsp;=&nbsp;date_format(ffl2.date,'%Y-%m-01')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="147" href="#147">147</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;ffl2.date&nbsp;&gt;&nbsp;'2013-03-01'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="148" href="#148">148</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="149" href="#149">149</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;ffl2.date</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="150" href="#150">150</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="151" href="#151">151</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="152" href="#152">152</a></td><td class="col-11 codeLine"><span class="default">UNION</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="153" href="#153">153</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="154" href="#154">154</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="155" href="#155">155</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="156" href="#156">156</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="157" href="#157">157</a></td><td class="col-11 codeLine"><span class="default">ffl2.date&nbsp;as&nbsp;Date,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="158" href="#158">158</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Live',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Live',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="159" href="#159">159</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Live',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;as&nbsp;'Virgin&nbsp;live',&nbsp;&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="160" href="#160">160</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Live',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;as&nbsp;'Switch&nbsp;Live',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="161" href="#161">161</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="162" href="#162">162</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="163" href="#163">163</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;in&nbsp;('Facility&nbsp;off',&nbsp;'No&nbsp;live&nbsp;units',&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units')&nbsp;,&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="164" href="#164">164</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;in&nbsp;('Facility&nbsp;off',&nbsp;'No&nbsp;live&nbsp;units',&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units')&nbsp;,&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="165" href="#165">165</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;in&nbsp;('Facility&nbsp;off',&nbsp;'No&nbsp;live&nbsp;units',&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units')&nbsp;,&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="166" href="#166">166</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="167" href="#167">167</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden&nbsp;-&nbsp;Facility&nbsp;off',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="168" href="#168">168</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="169" href="#169">169</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="170" href="#170">170</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="171" href="#171">171</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="172" href="#172">172</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'No&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="173" href="#173">173</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'No&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="174" href="#174">174</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'No&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="175" href="#175">175</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="176" href="#176">176</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="177" href="#177">177</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="178" href="#178">178</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="179" href="#179">179</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="180" href="#180">180</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="181" href="#181">181</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="182" href="#182">182</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Cancelled',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Cancelled',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="183" href="#183">183</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Cancelled',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Cancelled',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="184" href="#184">184</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Cancelled',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Cancelled',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="185" href="#185">185</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="186" href="#186">186</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Suspended',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Suspended',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="187" href="#187">187</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Suspended',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Suspended',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="188" href="#188">188</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Suspended',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Suspended',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="189" href="#189">189</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="190" href="#190">190</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Removed',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Removed',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="191" href="#191">191</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Removed',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Removed',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="192" href="#192">192</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Removed',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Removed',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="193" href="#193">193</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="194" href="#194">194</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="195" href="#195">195</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'SF&nbsp;deactivated',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'SF&nbsp;deactivated',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="196" href="#196">196</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'SF&nbsp;deactivated',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;SF&nbsp;deactivated',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="197" href="#197">197</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'SF&nbsp;deactivated',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;SF&nbsp;deactivated',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="198" href="#198">198</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="199" href="#199">199</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Incomplete',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Incomplete',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="200" href="#200">200</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Incomplete',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Incomplete',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="201" href="#201">201</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Incomplete',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Incomplete',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="202" href="#202">202</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="203" href="#203">203</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'other',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Other'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="204" href="#204">204</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;,count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'other',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Other',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="205" href="#205">205</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'other',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Other'&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="206" href="#206">206</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="207" href="#207">207</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="208" href="#208">208</a></td><td class="col-11 codeLine"><span class="default">db_analytics.facility_flags_log&nbsp;ffl2</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="209" href="#209">209</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="210" href="#210">210</a></td><td class="col-11 codeLine"><span class="default">db_analytics.facility_flags_log&nbsp;ffl</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="211" href="#211">211</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="212" href="#212">212</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(ffl2.listing_avail_id=ffl.listing_avail_id&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="213" href="#213">213</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;ffl.date=&nbsp;date_format(now(),'%Y-%m-01')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="214" href="#214">214</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="215" href="#215">215</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="216" href="#216">216</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="217" href="#217">217</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;ffl2.date&nbsp;=&nbsp;date(convert_tz(now(),'+00:00','-05:00'))</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="218" href="#218">218</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;ffl2.date&nbsp;&gt;=&nbsp;:START_TIME</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="219" href="#219">219</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;ffl2.date&nbsp;&lt;=&nbsp;:END_TIME</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="220" href="#220">220</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="221" href="#221">221</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;ffl2.date&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="222" href="#222">222</a></td><td class="col-11 codeLine"><span class="default">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="223" href="#223">223</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="224" href="#224">224</a></td><td class="col-11 codeLine"><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="225" href="#225">225</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="226" href="#226">226</a></td><td class="col-11 codeLine"><span class="keyword">:</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="227" href="#227">227</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="228" href="#228">228</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="229" href="#229">229</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;--&nbsp;daily&nbsp;view</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="230" href="#230">230</a></td><td class="col-11 codeLine"><span class="default">select</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="231" href="#231">231</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="232" href="#232">232</a></td><td class="col-11 codeLine"><span class="default">ffl2.date&nbsp;as&nbsp;Date,</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="233" href="#233">233</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Live',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Live',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="234" href="#234">234</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Live',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;as&nbsp;'Virgin&nbsp;live',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="235" href="#235">235</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Live',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;as&nbsp;'Switch&nbsp;Live',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="236" href="#236">236</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="237" href="#237">237</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="238" href="#238">238</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;in&nbsp;('Facility&nbsp;off',&nbsp;'No&nbsp;live&nbsp;units',&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units')&nbsp;,&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="239" href="#239">239</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;in&nbsp;('Facility&nbsp;off',&nbsp;'No&nbsp;live&nbsp;units',&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units')&nbsp;,&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="240" href="#240">240</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;in&nbsp;('Facility&nbsp;off',&nbsp;'No&nbsp;live&nbsp;units',&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units')&nbsp;,&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="241" href="#241">241</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="242" href="#242">242</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden&nbsp;-&nbsp;Facility&nbsp;off',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="243" href="#243">243</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="244" href="#244">244</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="245" href="#245">245</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="246" href="#246">246</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="247" href="#247">247</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'No&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="248" href="#248">248</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'No&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="249" href="#249">249</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'No&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden&nbsp;-&nbsp;No&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="250" href="#250">250</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="251" href="#251">251</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="252" href="#252">252</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="253" href="#253">253</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="254" href="#254">254</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="255" href="#255">255</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Hidden&nbsp;-&nbsp;Facility&nbsp;off&nbsp;and&nbsp;no&nbsp;live&nbsp;units',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="256" href="#256">256</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="257" href="#257">257</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Cancelled',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Cancelled',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="258" href="#258">258</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Cancelled',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Cancelled',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="259" href="#259">259</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Cancelled',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Cancelled',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="260" href="#260">260</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="261" href="#261">261</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Suspended',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Suspended',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="262" href="#262">262</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Suspended',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Suspended',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="263" href="#263">263</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Suspended',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Suspended',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="264" href="#264">264</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="265" href="#265">265</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Removed',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Removed',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="266" href="#266">266</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Removed',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Removed',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="267" href="#267">267</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Removed',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Removed',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="268" href="#268">268</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="269" href="#269">269</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="270" href="#270">270</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'SF&nbsp;deactivated',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'SF&nbsp;deactivated',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="271" href="#271">271</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'SF&nbsp;deactivated',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;SF&nbsp;deactivated',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="272" href="#272">272</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'SF&nbsp;deactivated',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;SF&nbsp;deactivated',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="273" href="#273">273</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="274" href="#274">274</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Incomplete',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Incomplete',</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="275" href="#275">275</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Incomplete',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Incomplete',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="276" href="#276">276</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'Incomplete',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Incomplete',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="277" href="#277">277</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="278" href="#278">278</a></td><td class="col-11 codeLine"><span class="default">count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code&nbsp;or&nbsp;ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'other',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Other'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="279" href="#279">279</a></td><td class="col-11 codeLine"><span class="default">&quot;</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;,count(if(if(ffl.reason_code&nbsp;is&nbsp;null,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'other',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Virgin&nbsp;Other',&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="280" href="#280">280</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">.</span><span class="keyword">(</span><span class="default">$parameters</span><span class="keyword">[</span><span class="default">'DETAILED_VIEW'</span><span class="keyword">]</span><span class="default">&nbsp;</span><span class="keyword">?</span><span class="default">&nbsp;</span><span class="default">&quot;count(if(if(ffl2.reason_code&lt;&gt;ffl.reason_code,&nbsp;ffl2.reason_code,&nbsp;null)&nbsp;=&nbsp;'other',&nbsp;ffl2.listing_avail_id,&nbsp;null))&nbsp;&nbsp;as&nbsp;'Switch&nbsp;Other'&quot;</span><span class="default">&nbsp;</span><span class="keyword">:</span><span class="default">&nbsp;</span><span class="default">'&nbsp;'</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">&quot;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="281" href="#281">281</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="282" href="#282">282</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="283" href="#283">283</a></td><td class="col-11 codeLine"><span class="default">from&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="284" href="#284">284</a></td><td class="col-11 codeLine"><span class="default">db_analytics.facility_flags_log&nbsp;ffl2</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="285" href="#285">285</a></td><td class="col-11 codeLine"><span class="default">left&nbsp;join</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="286" href="#286">286</a></td><td class="col-11 codeLine"><span class="default">db_analytics.facility_flags_log&nbsp;ffl</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="287" href="#287">287</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="288" href="#288">288</a></td><td class="col-11 codeLine"><span class="default">on&nbsp;(ffl2.listing_avail_id=ffl.listing_avail_id&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="289" href="#289">289</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;and&nbsp;ffl.date=&nbsp;date_sub(ffl2.date,&nbsp;interval&nbsp;1&nbsp;day)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="290" href="#290">290</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;)</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="291" href="#291">291</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="292" href="#292">292</a></td><td class="col-11 codeLine"><span class="default">where&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="293" href="#293">293</a></td><td class="col-11 codeLine"><span class="default">ffl2.date&nbsp;&gt;&nbsp;'2013-10-01'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="294" href="#294">294</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;ffl2.date&nbsp;not&nbsp;in&nbsp;('2013-10-07')</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="295" href="#295">295</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;ffl2.date&nbsp;&gt;=&nbsp;:START_TIME</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="296" href="#296">296</a></td><td class="col-11 codeLine"><span class="default">and&nbsp;ffl2.date&nbsp;&lt;=&nbsp;:END_TIME</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="297" href="#297">297</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;&nbsp;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="298" href="#298">298</a></td><td class="col-11 codeLine"><span class="default">group&nbsp;by&nbsp;ffl2.date</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="299" href="#299">299</a></td><td class="col-11 codeLine"></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="300" href="#300">300</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="301" href="#301">301</a></td><td class="col-11 codeLine"><span class="default">;&nbsp;&quot;</span><span class="keyword">)</span><span class="keyword">.</span><span class="default">'</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="302" href="#302">302</a></td><td class="col-11 codeLine"></td></tr>
    <tr class="danger d-flex"><td  class="col-1 text-right"><a id="303" href="#303">303</a></td><td class="col-11 codeLine"><span class="default">'</span><span class="keyword">;</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="304" href="#304">304</a></td><td class="col-11 codeLine"><span class="default">&nbsp;&nbsp;&nbsp;&nbsp;</span><span class="keyword">}</span></td></tr>
    <tr class=" d-flex"><td  class="col-1 text-right"><a id="305" href="#305">305</a></td><td class="col-11 codeLine"><span class="keyword">}</span></td></tr>

</tbody>
</table>


   <footer>
    <hr/>
    <h4>Legend</h4>
    <p><span class="legend covered-by-small-tests">Covered by small (and larger) tests</span><span class="legend covered-by-medium-tests">Covered by medium (and large) tests</span><span class="legend covered-by-large-tests">Covered by large tests (and tests of unknown size)</span><span class="legend not-covered">Not covered</span><span class="legend not-coverable">Not coverable</span></p>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
    <a title="Back to the top" id="toplink" href="#">
        <svg xmlns="http://www.w3.org/2000/svg" width="12" height="16" viewBox="0 0 12 16"><path fill-rule="evenodd" d="M12 11L6 5l-6 6h12z"/></svg>
    </a>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/popper.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/bootstrap.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/file.js?v=10.1.16" type="text/javascript"></script>
 </body>
</html>

<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickRep/Input</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../index.html">QuickRep</a></li>
         <li class="breadcrumb-item"><a href="index.html">Input</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ArraySelectInput.php.html#5">Sparefoot\PitaService\QuickRep\Input\ArraySelectInput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CheckboxGroupInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\CheckboxGroupInput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\DateInput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EntityIteratorSelectInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FileInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\FileInput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PasswordInput.php.html#5">Sparefoot\PitaService\QuickRep\Input\PasswordInput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuerySelectInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\QuerySelectInput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelectInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\SelectInput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TextAreaInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\TextAreaInput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TextInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\TextInput</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="EntityIteratorSelectInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ArraySelectInput.php.html#5">Sparefoot\PitaService\QuickRep\Input\ArraySelectInput</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="CheckboxGroupInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\CheckboxGroupInput</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="DateInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\DateInput</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="FileInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\FileInput</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TextAreaInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\TextAreaInput</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="TextInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\TextInput</a></td><td class="text-right">20</td></tr>
       <tr><td><a href="QuerySelectInput.php.html#7">Sparefoot\PitaService\QuickRep\Input\QuerySelectInput</a></td><td class="text-right">12</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ArraySelectInput.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Input\ArraySelectInput::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ArraySelectInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\ArraySelectInput::render">render</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CheckboxGroupInput.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Input\CheckboxGroupInput::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="CheckboxGroupInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\CheckboxGroupInput::render">render</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateInput.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Input\DateInput::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DateInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\DateInput::render">render</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EntityIteratorSelectInput.php.html#14"><abbr title="Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EntityIteratorSelectInput.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput::render">render</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EntityIteratorSelectInput.php.html#59"><abbr title="Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput::renderOptionsOnly">renderOptionsOnly</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EntityIteratorSelectInput.php.html#74"><abbr title="Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput::populateDataOptions">populateDataOptions</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FileInput.php.html#9"><abbr title="Sparefoot\PitaService\QuickRep\Input\FileInput::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FileInput.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Input\FileInput::render">render</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PasswordInput.php.html#7"><abbr title="Sparefoot\PitaService\QuickRep\Input\PasswordInput::render">render</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuerySelectInput.php.html#30"><abbr title="Sparefoot\PitaService\QuickRep\Input\QuerySelectInput::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="QuerySelectInput.php.html#51"><abbr title="Sparefoot\PitaService\QuickRep\Input\QuerySelectInput::_getResults">_getResults</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SelectInput.php.html#12"><abbr title="Sparefoot\PitaService\QuickRep\Input\SelectInput::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TextAreaInput.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Input\TextAreaInput::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TextAreaInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\TextAreaInput::render">render</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TextInput.php.html#11"><abbr title="Sparefoot\PitaService\QuickRep\Input\TextInput::__construct">__construct</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TextInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\TextInput::render">render</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ArraySelectInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\ArraySelectInput::render">render</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="CheckboxGroupInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\CheckboxGroupInput::render">render</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DateInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\DateInput::render">render</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="EntityIteratorSelectInput.php.html#34"><abbr title="Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput::render">render</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="EntityIteratorSelectInput.php.html#59"><abbr title="Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput::renderOptionsOnly">renderOptionsOnly</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="FileInput.php.html#19"><abbr title="Sparefoot\PitaService\QuickRep\Input\FileInput::render">render</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TextAreaInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\TextAreaInput::render">render</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TextInput.php.html#23"><abbr title="Sparefoot\PitaService\QuickRep\Input\TextInput::render">render</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="EntityIteratorSelectInput.php.html#74"><abbr title="Sparefoot\PitaService\QuickRep\Input\EntityIteratorSelectInput::populateDataOptions">populateDataOptions</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="QuerySelectInput.php.html#51"><abbr title="Sparefoot\PitaService\QuickRep\Input\QuerySelectInput::_getResults">_getResults</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([10,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([20,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,4,"<a href=\"ArraySelectInput.php.html#5\">Sparefoot\\PitaService\\QuickRep\\Input\\ArraySelectInput<\/a>"],[0,4,"<a href=\"CheckboxGroupInput.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Input\\CheckboxGroupInput<\/a>"],[0,4,"<a href=\"DateInput.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Input\\DateInput<\/a>"],[0,9,"<a href=\"EntityIteratorSelectInput.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Input\\EntityIteratorSelectInput<\/a>"],[0,4,"<a href=\"FileInput.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Input\\FileInput<\/a>"],[0,1,"<a href=\"PasswordInput.php.html#5\">Sparefoot\\PitaService\\QuickRep\\Input\\PasswordInput<\/a>"],[0,3,"<a href=\"QuerySelectInput.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Input\\QuerySelectInput<\/a>"],[0,1,"<a href=\"SelectInput.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Input\\SelectInput<\/a>"],[0,4,"<a href=\"TextAreaInput.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Input\\TextAreaInput<\/a>"],[0,4,"<a href=\"TextInput.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Input\\TextInput<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ArraySelectInput.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Input\\ArraySelectInput::__construct<\/a>"],[0,3,"<a href=\"ArraySelectInput.php.html#23\">Sparefoot\\PitaService\\QuickRep\\Input\\ArraySelectInput::render<\/a>"],[0,1,"<a href=\"CheckboxGroupInput.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Input\\CheckboxGroupInput::__construct<\/a>"],[0,3,"<a href=\"CheckboxGroupInput.php.html#23\">Sparefoot\\PitaService\\QuickRep\\Input\\CheckboxGroupInput::render<\/a>"],[0,1,"<a href=\"DateInput.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Input\\DateInput::__construct<\/a>"],[0,3,"<a href=\"DateInput.php.html#23\">Sparefoot\\PitaService\\QuickRep\\Input\\DateInput::render<\/a>"],[0,1,"<a href=\"EntityIteratorSelectInput.php.html#14\">Sparefoot\\PitaService\\QuickRep\\Input\\EntityIteratorSelectInput::__construct<\/a>"],[0,3,"<a href=\"EntityIteratorSelectInput.php.html#34\">Sparefoot\\PitaService\\QuickRep\\Input\\EntityIteratorSelectInput::render<\/a>"],[0,3,"<a href=\"EntityIteratorSelectInput.php.html#59\">Sparefoot\\PitaService\\QuickRep\\Input\\EntityIteratorSelectInput::renderOptionsOnly<\/a>"],[0,2,"<a href=\"EntityIteratorSelectInput.php.html#74\">Sparefoot\\PitaService\\QuickRep\\Input\\EntityIteratorSelectInput::populateDataOptions<\/a>"],[0,1,"<a href=\"FileInput.php.html#9\">Sparefoot\\PitaService\\QuickRep\\Input\\FileInput::__construct<\/a>"],[0,3,"<a href=\"FileInput.php.html#19\">Sparefoot\\PitaService\\QuickRep\\Input\\FileInput::render<\/a>"],[0,1,"<a href=\"PasswordInput.php.html#7\">Sparefoot\\PitaService\\QuickRep\\Input\\PasswordInput::render<\/a>"],[0,1,"<a href=\"QuerySelectInput.php.html#30\">Sparefoot\\PitaService\\QuickRep\\Input\\QuerySelectInput::__construct<\/a>"],[0,2,"<a href=\"QuerySelectInput.php.html#51\">Sparefoot\\PitaService\\QuickRep\\Input\\QuerySelectInput::_getResults<\/a>"],[0,1,"<a href=\"SelectInput.php.html#12\">Sparefoot\\PitaService\\QuickRep\\Input\\SelectInput::__construct<\/a>"],[0,1,"<a href=\"TextAreaInput.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Input\\TextAreaInput::__construct<\/a>"],[0,3,"<a href=\"TextAreaInput.php.html#23\">Sparefoot\\PitaService\\QuickRep\\Input\\TextAreaInput::render<\/a>"],[0,1,"<a href=\"TextInput.php.html#11\">Sparefoot\\PitaService\\QuickRep\\Input\\TextInput::__construct<\/a>"],[0,3,"<a href=\"TextInput.php.html#23\">Sparefoot\\PitaService\\QuickRep\\Input\\TextInput::render<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

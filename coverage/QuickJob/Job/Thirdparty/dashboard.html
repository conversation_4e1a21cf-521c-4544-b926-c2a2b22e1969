<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickJob/Job/Thirdparty</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickJob</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Job</a></li>
         <li class="breadcrumb-item"><a href="index.html">Thirdparty</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="StorTrakFacilityImport.php.html#9">Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakFacilityImport</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakImport.php.html#9">Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakUnitImport.php.html#9">Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakUnitImport</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestQuickrepAlert.php.html#8">Sparefoot\PitaService\QuickJob\Job\Thirdparty\TestQuickrepAlert</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#8">Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="StorTrakImport.php.html#9">Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport</a></td><td class="text-right">756</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#8">Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader</a></td><td class="text-right">462</td></tr>
       <tr><td><a href="StorTrakFacilityImport.php.html#9">Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakFacilityImport</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="StorTrakUnitImport.php.html#9">Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakUnitImport</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="TestQuickrepAlert.php.html#8">Sparefoot\PitaService\QuickJob\Job\Thirdparty\TestQuickrepAlert</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="StorTrakFacilityImport.php.html#11"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakFacilityImport::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakFacilityImport.php.html#16"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakFacilityImport::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakFacilityImport.php.html#21"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakFacilityImport::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakFacilityImport.php.html#26"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakFacilityImport::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakFacilityImport.php.html#42"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakFacilityImport::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakImport.php.html#11"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakImport.php.html#16"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakImport.php.html#21"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakImport.php.html#26"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakImport.php.html#35"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakImport.php.html#64"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::sync">sync</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakImport.php.html#92"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::upload">upload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakUnitImport.php.html#11"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakUnitImport::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakUnitImport.php.html#16"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakUnitImport::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakUnitImport.php.html#21"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakUnitImport::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakUnitImport.php.html#26"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakUnitImport::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="StorTrakUnitImport.php.html#41"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakUnitImport::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestQuickrepAlert.php.html#10"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\TestQuickrepAlert::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestQuickrepAlert.php.html#15"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\TestQuickrepAlert::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestQuickrepAlert.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\TestQuickrepAlert::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestQuickrepAlert.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\TestQuickrepAlert::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="TestQuickrepAlert.php.html#38"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\TestQuickrepAlert::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::getHeaders">getHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#48"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#53"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#58"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#65"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#72"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::buildTicketFromCsvArray">buildTicketFromCsvArray</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#96"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="StorTrakImport.php.html#92"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::upload">upload</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="StorTrakFacilityImport.php.html#42"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakFacilityImport::execute">execute</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="StorTrakImport.php.html#35"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::execute">execute</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#72"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::buildTicketFromCsvArray">buildTicketFromCsvArray</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="StorTrakUnitImport.php.html#41"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakUnitImport::execute">execute</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::getHeaders">getHeaders</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="ZendeskBulkTicketUploader.php.html#96"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\ZendeskBulkTicketUploader::execute">execute</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="StorTrakImport.php.html#64"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\StorTrakImport::sync">sync</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="TestQuickrepAlert.php.html#38"><abbr title="Sparefoot\PitaService\QuickJob\Job\Thirdparty\TestQuickrepAlert::execute">execute</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([29,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,11,"<a href=\"StorTrakFacilityImport.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakFacilityImport<\/a>"],[0,27,"<a href=\"StorTrakImport.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakImport<\/a>"],[0,10,"<a href=\"StorTrakUnitImport.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakUnitImport<\/a>"],[0,6,"<a href=\"TestQuickrepAlert.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\TestQuickrepAlert<\/a>"],[0,21,"<a href=\"ZendeskBulkTicketUploader.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\ZendeskBulkTicketUploader<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"StorTrakFacilityImport.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakFacilityImport::getCategory<\/a>"],[0,1,"<a href=\"StorTrakFacilityImport.php.html#16\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakFacilityImport::getName<\/a>"],[0,1,"<a href=\"StorTrakFacilityImport.php.html#21\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakFacilityImport::getDescription<\/a>"],[0,1,"<a href=\"StorTrakFacilityImport.php.html#26\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakFacilityImport::getInputs<\/a>"],[0,7,"<a href=\"StorTrakFacilityImport.php.html#42\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakFacilityImport::execute<\/a>"],[0,1,"<a href=\"StorTrakImport.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakImport::getCategory<\/a>"],[0,1,"<a href=\"StorTrakImport.php.html#16\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakImport::getName<\/a>"],[0,1,"<a href=\"StorTrakImport.php.html#21\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakImport::getDescription<\/a>"],[0,1,"<a href=\"StorTrakImport.php.html#26\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakImport::getInputs<\/a>"],[0,7,"<a href=\"StorTrakImport.php.html#35\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakImport::execute<\/a>"],[0,3,"<a href=\"StorTrakImport.php.html#64\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakImport::sync<\/a>"],[0,13,"<a href=\"StorTrakImport.php.html#92\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakImport::upload<\/a>"],[0,1,"<a href=\"StorTrakUnitImport.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakUnitImport::getCategory<\/a>"],[0,1,"<a href=\"StorTrakUnitImport.php.html#16\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakUnitImport::getName<\/a>"],[0,1,"<a href=\"StorTrakUnitImport.php.html#21\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakUnitImport::getDescription<\/a>"],[0,1,"<a href=\"StorTrakUnitImport.php.html#26\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakUnitImport::getInputs<\/a>"],[0,6,"<a href=\"StorTrakUnitImport.php.html#41\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\StorTrakUnitImport::execute<\/a>"],[0,1,"<a href=\"TestQuickrepAlert.php.html#10\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\TestQuickrepAlert::getCategory<\/a>"],[0,1,"<a href=\"TestQuickrepAlert.php.html#15\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\TestQuickrepAlert::getName<\/a>"],[0,1,"<a href=\"TestQuickrepAlert.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\TestQuickrepAlert::getDescription<\/a>"],[0,1,"<a href=\"TestQuickrepAlert.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\TestQuickrepAlert::getInputs<\/a>"],[0,2,"<a href=\"TestQuickrepAlert.php.html#38\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\TestQuickrepAlert::execute<\/a>"],[0,5,"<a href=\"ZendeskBulkTicketUploader.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\ZendeskBulkTicketUploader::getHeaders<\/a>"],[0,1,"<a href=\"ZendeskBulkTicketUploader.php.html#48\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\ZendeskBulkTicketUploader::getCategory<\/a>"],[0,1,"<a href=\"ZendeskBulkTicketUploader.php.html#53\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\ZendeskBulkTicketUploader::getName<\/a>"],[0,1,"<a href=\"ZendeskBulkTicketUploader.php.html#58\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\ZendeskBulkTicketUploader::getDescription<\/a>"],[0,1,"<a href=\"ZendeskBulkTicketUploader.php.html#65\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\ZendeskBulkTicketUploader::getInputs<\/a>"],[0,7,"<a href=\"ZendeskBulkTicketUploader.php.html#72\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\ZendeskBulkTicketUploader::buildTicketFromCsvArray<\/a>"],[0,5,"<a href=\"ZendeskBulkTicketUploader.php.html#96\">Sparefoot\\PitaService\\QuickJob\\Job\\Thirdparty\\ZendeskBulkTicketUploader::execute<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

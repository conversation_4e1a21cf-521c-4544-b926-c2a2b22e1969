<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickJob/Job/Inventory</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickJob</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Job</a></li>
         <li class="breadcrumb-item"><a href="index.html">Inventory</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AddStorageExpressFacility.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\AddStorageExpressFacility</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyReservationWindowRules.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyReservationWindowRules</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#9">Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnqueueOmnomSync.php.html#10">Sparefoot\PitaService\QuickJob\Job\Inventory\EnqueueOmnomSync</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MapStringsToSpecials.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\MapStringsToSpecials</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#10">Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="EnqueueOmnomSync.php.html#10">Sparefoot\PitaService\QuickJob\Job\Inventory\EnqueueOmnomSync</a></td><td class="text-right">600</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities</a></td><td class="text-right">552</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#9">Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings</a></td><td class="text-right">420</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#10">Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule</a></td><td class="text-right">380</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="MapStringsToSpecials.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\MapStringsToSpecials</a></td><td class="text-right">132</td></tr>
       <tr><td><a href="AddStorageExpressFacility.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\AddStorageExpressFacility</a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ApplyReservationWindowRules.php.html#8">Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyReservationWindowRules</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AddStorageExpressFacility.php.html#12"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\AddStorageExpressFacility::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddStorageExpressFacility.php.html#17"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\AddStorageExpressFacility::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddStorageExpressFacility.php.html#22"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\AddStorageExpressFacility::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddStorageExpressFacility.php.html#29"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\AddStorageExpressFacility::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddStorageExpressFacility.php.html#41"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\AddStorageExpressFacility::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#12"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#17"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#22"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#28"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#37"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#97"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures::_getCell">_getCell</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#12"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#17"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#22"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#28"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#37"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#79"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes::_getCell">_getCell</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyReservationWindowRules.php.html#10"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyReservationWindowRules::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyReservationWindowRules.php.html#15"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyReservationWindowRules::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyReservationWindowRules.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyReservationWindowRules::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyReservationWindowRules.php.html#35"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyReservationWindowRules::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyReservationWindowRules.php.html#42"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyReservationWindowRules::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#13"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#18"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#23"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#29"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#43"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#108"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings::_getCell">_getCell</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#15"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#31"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#40"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities::isDownload">isDownload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#45"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#136"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities::_getCell">_getCell</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnqueueOmnomSync.php.html#14"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\EnqueueOmnomSync::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnqueueOmnomSync.php.html#19"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\EnqueueOmnomSync::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnqueueOmnomSync.php.html#24"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\EnqueueOmnomSync::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnqueueOmnomSync.php.html#29"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\EnqueueOmnomSync::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="EnqueueOmnomSync.php.html#105"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\EnqueueOmnomSync::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MapStringsToSpecials.php.html#12"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\MapStringsToSpecials::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MapStringsToSpecials.php.html#17"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\MapStringsToSpecials::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MapStringsToSpecials.php.html#22"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\MapStringsToSpecials::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MapStringsToSpecials.php.html#31"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\MapStringsToSpecials::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MapStringsToSpecials.php.html#74"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\MapStringsToSpecials::_getCell">_getCell</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#12"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#17"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#22"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#40"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#103"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#134"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::setAndApplyResWindowRule">setAndApplyResWindowRule</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#147"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::buildResWindowRule">buildResWindowRule</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="EnqueueOmnomSync.php.html#105"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\EnqueueOmnomSync::execute">execute</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#45"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities::execute">execute</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#43"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings::execute">execute</abbr></a></td><td class="text-right">182</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#37"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures::execute">execute</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#37"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes::execute">execute</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#103"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::execute">execute</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="MapStringsToSpecials.php.html#31"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\MapStringsToSpecials::execute">execute</abbr></a></td><td class="text-right">30</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#40"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::getInputs">getInputs</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="SetReservationWindowRule.php.html#147"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\SetReservationWindowRule::buildResWindowRule">buildResWindowRule</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ApplyCustomClosures.php.html#97"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyCustomClosures::_getCell">_getCell</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApplyFacilityAttributes.php.html#79"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyFacilityAttributes::_getCell">_getCell</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApplySpecialStrings.php.html#108"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplySpecialStrings::_getCell">_getCell</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="DedupeDirectoryFacilities.php.html#136"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\DedupeDirectoryFacilities::_getCell">_getCell</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MapStringsToSpecials.php.html#74"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\MapStringsToSpecials::_getCell">_getCell</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="AddStorageExpressFacility.php.html#41"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\AddStorageExpressFacility::execute">execute</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApplyReservationWindowRules.php.html#42"><abbr title="Sparefoot\PitaService\QuickJob\Job\Inventory\ApplyReservationWindowRules::execute">execute</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([9,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([52,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,6,"<a href=\"AddStorageExpressFacility.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\AddStorageExpressFacility<\/a>"],[0,18,"<a href=\"ApplyCustomClosures.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyCustomClosures<\/a>"],[0,16,"<a href=\"ApplyFacilityAttributes.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyFacilityAttributes<\/a>"],[0,6,"<a href=\"ApplyReservationWindowRules.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyReservationWindowRules<\/a>"],[0,20,"<a href=\"ApplySpecialStrings.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplySpecialStrings<\/a>"],[0,23,"<a href=\"DedupeDirectoryFacilities.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\DedupeDirectoryFacilities<\/a>"],[0,24,"<a href=\"EnqueueOmnomSync.php.html#10\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\EnqueueOmnomSync<\/a>"],[0,11,"<a href=\"MapStringsToSpecials.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\MapStringsToSpecials<\/a>"],[0,19,"<a href=\"SetReservationWindowRule.php.html#10\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\SetReservationWindowRule<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AddStorageExpressFacility.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\AddStorageExpressFacility::getCategory<\/a>"],[0,1,"<a href=\"AddStorageExpressFacility.php.html#17\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\AddStorageExpressFacility::getName<\/a>"],[0,1,"<a href=\"AddStorageExpressFacility.php.html#22\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\AddStorageExpressFacility::getDescription<\/a>"],[0,1,"<a href=\"AddStorageExpressFacility.php.html#29\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\AddStorageExpressFacility::getInputs<\/a>"],[0,2,"<a href=\"AddStorageExpressFacility.php.html#41\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\AddStorageExpressFacility::execute<\/a>"],[0,1,"<a href=\"ApplyCustomClosures.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyCustomClosures::getCategory<\/a>"],[0,1,"<a href=\"ApplyCustomClosures.php.html#17\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyCustomClosures::getName<\/a>"],[0,1,"<a href=\"ApplyCustomClosures.php.html#22\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyCustomClosures::getDescription<\/a>"],[0,1,"<a href=\"ApplyCustomClosures.php.html#28\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyCustomClosures::getInputs<\/a>"],[0,11,"<a href=\"ApplyCustomClosures.php.html#37\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyCustomClosures::execute<\/a>"],[0,3,"<a href=\"ApplyCustomClosures.php.html#97\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyCustomClosures::_getCell<\/a>"],[0,1,"<a href=\"ApplyFacilityAttributes.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyFacilityAttributes::getCategory<\/a>"],[0,1,"<a href=\"ApplyFacilityAttributes.php.html#17\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyFacilityAttributes::getName<\/a>"],[0,1,"<a href=\"ApplyFacilityAttributes.php.html#22\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyFacilityAttributes::getDescription<\/a>"],[0,1,"<a href=\"ApplyFacilityAttributes.php.html#28\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyFacilityAttributes::getInputs<\/a>"],[0,9,"<a href=\"ApplyFacilityAttributes.php.html#37\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyFacilityAttributes::execute<\/a>"],[0,3,"<a href=\"ApplyFacilityAttributes.php.html#79\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyFacilityAttributes::_getCell<\/a>"],[0,1,"<a href=\"ApplyReservationWindowRules.php.html#10\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyReservationWindowRules::getCategory<\/a>"],[0,1,"<a href=\"ApplyReservationWindowRules.php.html#15\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyReservationWindowRules::getName<\/a>"],[0,1,"<a href=\"ApplyReservationWindowRules.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyReservationWindowRules::getInputs<\/a>"],[0,1,"<a href=\"ApplyReservationWindowRules.php.html#35\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyReservationWindowRules::getDescription<\/a>"],[0,2,"<a href=\"ApplyReservationWindowRules.php.html#42\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplyReservationWindowRules::execute<\/a>"],[0,1,"<a href=\"ApplySpecialStrings.php.html#13\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplySpecialStrings::getCategory<\/a>"],[0,1,"<a href=\"ApplySpecialStrings.php.html#18\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplySpecialStrings::getName<\/a>"],[0,1,"<a href=\"ApplySpecialStrings.php.html#23\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplySpecialStrings::getDescription<\/a>"],[0,1,"<a href=\"ApplySpecialStrings.php.html#29\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplySpecialStrings::getInputs<\/a>"],[0,13,"<a href=\"ApplySpecialStrings.php.html#43\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplySpecialStrings::execute<\/a>"],[0,3,"<a href=\"ApplySpecialStrings.php.html#108\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\ApplySpecialStrings::_getCell<\/a>"],[0,1,"<a href=\"DedupeDirectoryFacilities.php.html#15\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\DedupeDirectoryFacilities::getCategory<\/a>"],[0,1,"<a href=\"DedupeDirectoryFacilities.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\DedupeDirectoryFacilities::getName<\/a>"],[0,1,"<a href=\"DedupeDirectoryFacilities.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\DedupeDirectoryFacilities::getDescription<\/a>"],[0,1,"<a href=\"DedupeDirectoryFacilities.php.html#31\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\DedupeDirectoryFacilities::getInputs<\/a>"],[0,1,"<a href=\"DedupeDirectoryFacilities.php.html#40\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\DedupeDirectoryFacilities::isDownload<\/a>"],[0,15,"<a href=\"DedupeDirectoryFacilities.php.html#45\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\DedupeDirectoryFacilities::execute<\/a>"],[0,3,"<a href=\"DedupeDirectoryFacilities.php.html#136\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\DedupeDirectoryFacilities::_getCell<\/a>"],[0,1,"<a href=\"EnqueueOmnomSync.php.html#14\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\EnqueueOmnomSync::getCategory<\/a>"],[0,1,"<a href=\"EnqueueOmnomSync.php.html#19\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\EnqueueOmnomSync::getName<\/a>"],[0,1,"<a href=\"EnqueueOmnomSync.php.html#24\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\EnqueueOmnomSync::getDescription<\/a>"],[0,1,"<a href=\"EnqueueOmnomSync.php.html#29\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\EnqueueOmnomSync::getInputs<\/a>"],[0,20,"<a href=\"EnqueueOmnomSync.php.html#105\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\EnqueueOmnomSync::execute<\/a>"],[0,1,"<a href=\"MapStringsToSpecials.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\MapStringsToSpecials::getCategory<\/a>"],[0,1,"<a href=\"MapStringsToSpecials.php.html#17\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\MapStringsToSpecials::getName<\/a>"],[0,1,"<a href=\"MapStringsToSpecials.php.html#22\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\MapStringsToSpecials::getInputs<\/a>"],[0,5,"<a href=\"MapStringsToSpecials.php.html#31\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\MapStringsToSpecials::execute<\/a>"],[0,3,"<a href=\"MapStringsToSpecials.php.html#74\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\MapStringsToSpecials::_getCell<\/a>"],[0,1,"<a href=\"SetReservationWindowRule.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\SetReservationWindowRule::getCategory<\/a>"],[0,1,"<a href=\"SetReservationWindowRule.php.html#17\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\SetReservationWindowRule::getName<\/a>"],[0,1,"<a href=\"SetReservationWindowRule.php.html#22\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\SetReservationWindowRule::getDescription<\/a>"],[0,4,"<a href=\"SetReservationWindowRule.php.html#40\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\SetReservationWindowRule::getInputs<\/a>"],[0,7,"<a href=\"SetReservationWindowRule.php.html#103\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\SetReservationWindowRule::execute<\/a>"],[0,1,"<a href=\"SetReservationWindowRule.php.html#134\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\SetReservationWindowRule::setAndApplyResWindowRule<\/a>"],[0,4,"<a href=\"SetReservationWindowRule.php.html#147\">Sparefoot\\PitaService\\QuickJob\\Job\\Inventory\\SetReservationWindowRule::buildResWindowRule<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

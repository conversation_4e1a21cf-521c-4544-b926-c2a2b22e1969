<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickJob/Job/Bookings</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickJob</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Job</a></li>
         <li class="breadcrumb-item"><a href="index.html">Bookings</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AddBookings.php.html#8">Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ChangePendingBookingBidType.php.html#8">Sparefoot\PitaService\QuickJob\Job\Bookings\ChangePendingBookingBidType</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FixMissingUserIdsForOfflineBookings.php.html#7">Sparefoot\PitaService\QuickJob\Job\Bookings\FixMissingUserIdsForOfflineBookings</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeBooking.php.html#13">Sparefoot\PitaService\QuickJob\Job\Bookings\MakeBooking</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBookingsToNextStatement.php.html#9">Sparefoot\PitaService\QuickJob\Job\Bookings\PushBookingsToNextStatement</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignPreviouslyAssignedBookings.php.html#9">Sparefoot\PitaService\QuickJob\Job\Bookings\UnassignPreviouslyAssignedBookings</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AddBookings.php.html#8">Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings</a></td><td class="text-right">342</td></tr>
       <tr><td><a href="UnassignPreviouslyAssignedBookings.php.html#9">Sparefoot\PitaService\QuickJob\Job\Bookings\UnassignPreviouslyAssignedBookings</a></td><td class="text-right">272</td></tr>
       <tr><td><a href="PushBookingsToNextStatement.php.html#9">Sparefoot\PitaService\QuickJob\Job\Bookings\PushBookingsToNextStatement</a></td><td class="text-right">182</td></tr>
       <tr><td><a href="FixMissingUserIdsForOfflineBookings.php.html#7">Sparefoot\PitaService\QuickJob\Job\Bookings\FixMissingUserIdsForOfflineBookings</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ChangePendingBookingBidType.php.html#8">Sparefoot\PitaService\QuickJob\Job\Bookings\ChangePendingBookingBidType</a></td><td class="text-right">72</td></tr>
       <tr><td><a href="MakeBooking.php.html#13">Sparefoot\PitaService\QuickJob\Job\Bookings\MakeBooking</a></td><td class="text-right">42</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AddBookings.php.html#12"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddBookings.php.html#17"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddBookings.php.html#22"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddBookings.php.html#40"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddBookings.php.html#47"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="AddBookings.php.html#154"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings::_getCell">_getCell</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ChangePendingBookingBidType.php.html#10"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\ChangePendingBookingBidType::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ChangePendingBookingBidType.php.html#15"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\ChangePendingBookingBidType::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ChangePendingBookingBidType.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\ChangePendingBookingBidType::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ChangePendingBookingBidType.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\ChangePendingBookingBidType::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ChangePendingBookingBidType.php.html#32"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\ChangePendingBookingBidType::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FixMissingUserIdsForOfflineBookings.php.html#11"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\FixMissingUserIdsForOfflineBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FixMissingUserIdsForOfflineBookings.php.html#16"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\FixMissingUserIdsForOfflineBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FixMissingUserIdsForOfflineBookings.php.html#21"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\FixMissingUserIdsForOfflineBookings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="FixMissingUserIdsForOfflineBookings.php.html#27"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\FixMissingUserIdsForOfflineBookings::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeBooking.php.html#15"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\MakeBooking::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeBooking.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\MakeBooking::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeBooking.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\MakeBooking::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeBooking.php.html#32"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\MakeBooking::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MakeBooking.php.html#116"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\MakeBooking::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBookingsToNextStatement.php.html#13"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\PushBookingsToNextStatement::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBookingsToNextStatement.php.html#18"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\PushBookingsToNextStatement::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBookingsToNextStatement.php.html#23"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\PushBookingsToNextStatement::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBookingsToNextStatement.php.html#29"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\PushBookingsToNextStatement::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="PushBookingsToNextStatement.php.html#43"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\PushBookingsToNextStatement::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignPreviouslyAssignedBookings.php.html#11"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\UnassignPreviouslyAssignedBookings::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignPreviouslyAssignedBookings.php.html#16"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\UnassignPreviouslyAssignedBookings::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignPreviouslyAssignedBookings.php.html#21"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\UnassignPreviouslyAssignedBookings::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignPreviouslyAssignedBookings.php.html#26"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\UnassignPreviouslyAssignedBookings::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignPreviouslyAssignedBookings.php.html#46"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\UnassignPreviouslyAssignedBookings::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="UnassignPreviouslyAssignedBookings.php.html#149"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\UnassignPreviouslyAssignedBookings::log">log</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="AddBookings.php.html#47"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings::execute">execute</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="UnassignPreviouslyAssignedBookings.php.html#46"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\UnassignPreviouslyAssignedBookings::execute">execute</abbr></a></td><td class="text-right">132</td></tr>
       <tr><td><a href="PushBookingsToNextStatement.php.html#43"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\PushBookingsToNextStatement::execute">execute</abbr></a></td><td class="text-right">90</td></tr>
       <tr><td><a href="FixMissingUserIdsForOfflineBookings.php.html#27"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\FixMissingUserIdsForOfflineBookings::execute">execute</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ChangePendingBookingBidType.php.html#32"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\ChangePendingBookingBidType::execute">execute</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="AddBookings.php.html#154"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\AddBookings::_getCell">_getCell</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="MakeBooking.php.html#116"><abbr title="Sparefoot\PitaService\QuickJob\Job\Bookings\MakeBooking::execute">execute</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([6,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([31,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,18,"<a href=\"AddBookings.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\AddBookings<\/a>"],[0,8,"<a href=\"ChangePendingBookingBidType.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\ChangePendingBookingBidType<\/a>"],[0,10,"<a href=\"FixMissingUserIdsForOfflineBookings.php.html#7\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\FixMissingUserIdsForOfflineBookings<\/a>"],[0,6,"<a href=\"MakeBooking.php.html#13\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\MakeBooking<\/a>"],[0,13,"<a href=\"PushBookingsToNextStatement.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\PushBookingsToNextStatement<\/a>"],[0,16,"<a href=\"UnassignPreviouslyAssignedBookings.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\UnassignPreviouslyAssignedBookings<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"AddBookings.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\AddBookings::getCategory<\/a>"],[0,1,"<a href=\"AddBookings.php.html#17\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\AddBookings::getName<\/a>"],[0,1,"<a href=\"AddBookings.php.html#22\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\AddBookings::getDescription<\/a>"],[0,1,"<a href=\"AddBookings.php.html#40\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\AddBookings::getInputs<\/a>"],[0,11,"<a href=\"AddBookings.php.html#47\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\AddBookings::execute<\/a>"],[0,3,"<a href=\"AddBookings.php.html#154\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\AddBookings::_getCell<\/a>"],[0,1,"<a href=\"ChangePendingBookingBidType.php.html#10\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\ChangePendingBookingBidType::getCategory<\/a>"],[0,1,"<a href=\"ChangePendingBookingBidType.php.html#15\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\ChangePendingBookingBidType::getName<\/a>"],[0,1,"<a href=\"ChangePendingBookingBidType.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\ChangePendingBookingBidType::getDescription<\/a>"],[0,1,"<a href=\"ChangePendingBookingBidType.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\ChangePendingBookingBidType::getInputs<\/a>"],[0,4,"<a href=\"ChangePendingBookingBidType.php.html#32\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\ChangePendingBookingBidType::execute<\/a>"],[0,1,"<a href=\"FixMissingUserIdsForOfflineBookings.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\FixMissingUserIdsForOfflineBookings::getCategory<\/a>"],[0,1,"<a href=\"FixMissingUserIdsForOfflineBookings.php.html#16\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\FixMissingUserIdsForOfflineBookings::getName<\/a>"],[0,1,"<a href=\"FixMissingUserIdsForOfflineBookings.php.html#21\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\FixMissingUserIdsForOfflineBookings::getInputs<\/a>"],[0,7,"<a href=\"FixMissingUserIdsForOfflineBookings.php.html#27\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\FixMissingUserIdsForOfflineBookings::execute<\/a>"],[0,1,"<a href=\"MakeBooking.php.html#15\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\MakeBooking::getCategory<\/a>"],[0,1,"<a href=\"MakeBooking.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\MakeBooking::getName<\/a>"],[0,1,"<a href=\"MakeBooking.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\MakeBooking::getDescription<\/a>"],[0,1,"<a href=\"MakeBooking.php.html#32\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\MakeBooking::getInputs<\/a>"],[0,2,"<a href=\"MakeBooking.php.html#116\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\MakeBooking::execute<\/a>"],[0,1,"<a href=\"PushBookingsToNextStatement.php.html#13\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\PushBookingsToNextStatement::getCategory<\/a>"],[0,1,"<a href=\"PushBookingsToNextStatement.php.html#18\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\PushBookingsToNextStatement::getName<\/a>"],[0,1,"<a href=\"PushBookingsToNextStatement.php.html#23\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\PushBookingsToNextStatement::getDescription<\/a>"],[0,1,"<a href=\"PushBookingsToNextStatement.php.html#29\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\PushBookingsToNextStatement::getInputs<\/a>"],[0,9,"<a href=\"PushBookingsToNextStatement.php.html#43\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\PushBookingsToNextStatement::execute<\/a>"],[0,1,"<a href=\"UnassignPreviouslyAssignedBookings.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\UnassignPreviouslyAssignedBookings::getCategory<\/a>"],[0,1,"<a href=\"UnassignPreviouslyAssignedBookings.php.html#16\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\UnassignPreviouslyAssignedBookings::getName<\/a>"],[0,1,"<a href=\"UnassignPreviouslyAssignedBookings.php.html#21\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\UnassignPreviouslyAssignedBookings::getDescription<\/a>"],[0,1,"<a href=\"UnassignPreviouslyAssignedBookings.php.html#26\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\UnassignPreviouslyAssignedBookings::getInputs<\/a>"],[0,11,"<a href=\"UnassignPreviouslyAssignedBookings.php.html#46\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\UnassignPreviouslyAssignedBookings::execute<\/a>"],[0,1,"<a href=\"UnassignPreviouslyAssignedBookings.php.html#149\">Sparefoot\\PitaService\\QuickJob\\Job\\Bookings\\UnassignPreviouslyAssignedBookings::log<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

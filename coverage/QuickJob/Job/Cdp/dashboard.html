<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickJob/Job/Cdp</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">QuickJob</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Job</a></li>
         <li class="breadcrumb-item"><a href="index.html">Cdp</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ApplyJackieOutput.php.html#9">Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyJackieOutput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyMoveIns.php.html#9">Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyMoveIns</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#10">Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearBadUserIds.php.html#7">Sparefoot\PitaService\QuickJob\Job\Cdp\ClearBadUserIds</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns2.php.html#8">Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns2</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReverseCDP.php.html#10">Sparefoot\PitaService\QuickJob\Job\Cdp\ReverseCDP</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnAPI.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnAPI</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnMoveInsFile.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnMoveInsFile</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#9">Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveInsSiteLink.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#12">Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns2.php.html#12">Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportMoveIns2.php.html#8">Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns2</a></td><td class="text-right">3192</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#9">Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns</a></td><td class="text-right">2756</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#10">Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput</a></td><td class="text-right">2256</td></tr>
       <tr><td><a href="ApplyMoveIns.php.html#9">Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyMoveIns</a></td><td class="text-right">1190</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#12">Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns</a></td><td class="text-right">1190</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="deprecated/ImportMoveInsSiteLink.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink</a></td><td class="text-right">812</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns2.php.html#12">Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2</a></td><td class="text-right">702</td></tr>
       <tr><td><a href="ApplyJackieOutput.php.html#9">Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyJackieOutput</a></td><td class="text-right">650</td></tr>
       <tr><td><a href="ReverseCDP.php.html#10">Sparefoot\PitaService\QuickJob\Job\Cdp\ReverseCDP</a></td><td class="text-right">110</td></tr>
       <tr><td><a href="RunCdpOnAPI.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnAPI</a></td><td class="text-right">90</td></tr>
       <tr><td><a href="ClearBadUserIds.php.html#7">Sparefoot\PitaService\QuickJob\Job\Cdp\ClearBadUserIds</a></td><td class="text-right">30</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ApplyJackieOutput.php.html#13"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyJackieOutput::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyJackieOutput.php.html#18"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyJackieOutput::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyJackieOutput.php.html#23"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyJackieOutput::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyJackieOutput.php.html#33"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyJackieOutput::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyMoveIns.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyMoveIns::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyMoveIns.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyMoveIns::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyMoveIns.php.html#30"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyMoveIns::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyMoveIns.php.html#44"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyMoveIns::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#27"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#32"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#42"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#64"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getStatementBatchSelector">getStatementBatchSelector</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#82"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#103"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::init">init</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#113"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::loadCsv">loadCsv</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#120"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getCsvHeaders">getCsvHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#131"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::validateCsvInput">validateCsvInput</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#159"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::resetAutoStates">resetAutoStates</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#184"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::cdpMatch">cdpMatch</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#273"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getMovedInFacility">getMovedInFacility</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#290"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getBooking">getBooking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#363"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::wasPreviouslyUnbilled">wasPreviouslyUnbilled</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#399"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::disputeBookings">disputeBookings</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#439"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::outputResults">outputResults</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearBadUserIds.php.html#9"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ClearBadUserIds::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearBadUserIds.php.html#14"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ClearBadUserIds::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearBadUserIds.php.html#19"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ClearBadUserIds::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ClearBadUserIds.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ClearBadUserIds::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns2.php.html#24"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns2::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns2.php.html#29"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns2::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns2.php.html#34"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns2::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns2.php.html#46"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns2::isDownload">isDownload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns2.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns2::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns2.php.html#264"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns2::outputHeaders">outputHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReverseCDP.php.html#14"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ReverseCDP::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReverseCDP.php.html#19"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ReverseCDP::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReverseCDP.php.html#24"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ReverseCDP::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ReverseCDP.php.html#54"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ReverseCDP::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnAPI.php.html#15"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnAPI::showInProduction">showInProduction</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnAPI.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnAPI::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnAPI.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnAPI::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnAPI.php.html#30"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnAPI::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnAPI.php.html#105"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnAPI::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnMoveInsFile.php.html#15"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnMoveInsFile::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnMoveInsFile.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnMoveInsFile::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnMoveInsFile.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnMoveInsFile::getDescription">getDescription</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnMoveInsFile.php.html#33"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnMoveInsFile::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="RunCdpOnMoveInsFile.php.html#117"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnMoveInsFile::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#11"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::isDeprecated">isDeprecated</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#27"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#32"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#37"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#52"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::isDownload">isDownload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#57"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#207"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::outputRecord">outputRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#242"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::outputHeaders">outputHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#17"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#22"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#27"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#127"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::processLine">processLine</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveInsSiteLink.php.html#15"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveInsSiteLink.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveInsSiteLink.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/ImportMoveInsSiteLink.php.html#59"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#23"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#28"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#33"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#346"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::outputBooking">outputBooking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#358"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::mapBooking">mapBooking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns2.php.html#16"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns2.php.html#21"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns2.php.html#26"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns2.php.html#54"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportMoveIns2.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns2::execute">execute</abbr></a></td><td class="text-right">2652</td></tr>
       <tr><td><a href="ApplyMoveIns.php.html#44"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyMoveIns::execute">execute</abbr></a></td><td class="text-right">992</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::execute">execute</abbr></a></td><td class="text-right">702</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#207"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::outputRecord">outputRecord</abbr></a></td><td class="text-right">600</td></tr>
       <tr><td><a href="ApplyJackieOutput.php.html#33"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyJackieOutput::execute">execute</abbr></a></td><td class="text-right">506</td></tr>
       <tr><td><a href="deprecated/ExportMoveIns.php.html#57"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::execute">execute</abbr></a></td><td class="text-right">506</td></tr>
       <tr><td><a href="deprecated/ImportMoveInsSiteLink.php.html#59"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::execute">execute</abbr></a></td><td class="text-right">506</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns2.php.html#54"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::execute">execute</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#127"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::processLine">processLine</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::execute">execute</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#184"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::cdpMatch">cdpMatch</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#290"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getBooking">getBooking</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ReverseCDP.php.html#54"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ReverseCDP::execute">execute</abbr></a></td><td class="text-right">56</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#131"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::validateCsvInput">validateCsvInput</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#363"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::wasPreviouslyUnbilled">wasPreviouslyUnbilled</abbr></a></td><td class="text-right">42</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#399"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::disputeBookings">disputeBookings</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="deprecated/ImportMoveIns.php.html#27"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::getInputs">getInputs</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="deprecated/ImportMoveInsSiteLink.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::getInputs">getInputs</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns.php.html#358"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::mapBooking">mapBooking</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="deprecated/MatchMoveIns2.php.html#26"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::getInputs">getInputs</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#64"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getStatementBatchSelector">getStatementBatchSelector</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#82"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::execute">execute</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RunCdpOnAPI.php.html#30"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnAPI::getInputs">getInputs</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="RunCdpOnAPI.php.html#105"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\RunCdpOnAPI::execute">execute</abbr></a></td><td class="text-right">12</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#159"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::resetAutoStates">resetAutoStates</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ApplyNewJackieOutput.php.html#273"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ApplyNewJackieOutput::getMovedInFacility">getMovedInFacility</abbr></a></td><td class="text-right">6</td></tr>
       <tr><td><a href="ClearBadUserIds.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ClearBadUserIds::execute">execute</abbr></a></td><td class="text-right">6</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([13,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([75,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,25,"<a href=\"ApplyJackieOutput.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyJackieOutput<\/a>"],[0,34,"<a href=\"ApplyMoveIns.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyMoveIns<\/a>"],[0,47,"<a href=\"ApplyNewJackieOutput.php.html#10\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput<\/a>"],[0,5,"<a href=\"ClearBadUserIds.php.html#7\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ClearBadUserIds<\/a>"],[0,56,"<a href=\"ExportMoveIns2.php.html#8\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns2<\/a>"],[0,10,"<a href=\"ReverseCDP.php.html#10\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ReverseCDP<\/a>"],[0,9,"<a href=\"RunCdpOnAPI.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnAPI<\/a>"],[0,5,"<a href=\"RunCdpOnMoveInsFile.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnMoveInsFile<\/a>"],[0,52,"<a href=\"deprecated\/ExportMoveIns.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns<\/a>"],[0,31,"<a href=\"deprecated\/ImportMoveIns.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns<\/a>"],[0,28,"<a href=\"deprecated\/ImportMoveInsSiteLink.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink<\/a>"],[0,34,"<a href=\"deprecated\/MatchMoveIns.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns<\/a>"],[0,26,"<a href=\"deprecated\/MatchMoveIns2.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ApplyJackieOutput.php.html#13\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyJackieOutput::getCategory<\/a>"],[0,1,"<a href=\"ApplyJackieOutput.php.html#18\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyJackieOutput::getName<\/a>"],[0,1,"<a href=\"ApplyJackieOutput.php.html#23\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyJackieOutput::getInputs<\/a>"],[0,22,"<a href=\"ApplyJackieOutput.php.html#33\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyJackieOutput::execute<\/a>"],[0,1,"<a href=\"ApplyMoveIns.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyMoveIns::getCategory<\/a>"],[0,1,"<a href=\"ApplyMoveIns.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyMoveIns::getName<\/a>"],[0,1,"<a href=\"ApplyMoveIns.php.html#30\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyMoveIns::getInputs<\/a>"],[0,31,"<a href=\"ApplyMoveIns.php.html#44\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyMoveIns::execute<\/a>"],[0,1,"<a href=\"ApplyNewJackieOutput.php.html#27\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::getCategory<\/a>"],[0,1,"<a href=\"ApplyNewJackieOutput.php.html#32\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::getName<\/a>"],[0,1,"<a href=\"ApplyNewJackieOutput.php.html#42\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::getInputs<\/a>"],[0,3,"<a href=\"ApplyNewJackieOutput.php.html#64\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::getStatementBatchSelector<\/a>"],[0,3,"<a href=\"ApplyNewJackieOutput.php.html#82\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::execute<\/a>"],[0,1,"<a href=\"ApplyNewJackieOutput.php.html#103\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::init<\/a>"],[0,1,"<a href=\"ApplyNewJackieOutput.php.html#113\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::loadCsv<\/a>"],[0,1,"<a href=\"ApplyNewJackieOutput.php.html#120\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::getCsvHeaders<\/a>"],[0,6,"<a href=\"ApplyNewJackieOutput.php.html#131\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::validateCsvInput<\/a>"],[0,2,"<a href=\"ApplyNewJackieOutput.php.html#159\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::resetAutoStates<\/a>"],[0,7,"<a href=\"ApplyNewJackieOutput.php.html#184\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::cdpMatch<\/a>"],[0,2,"<a href=\"ApplyNewJackieOutput.php.html#273\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::getMovedInFacility<\/a>"],[0,7,"<a href=\"ApplyNewJackieOutput.php.html#290\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::getBooking<\/a>"],[0,6,"<a href=\"ApplyNewJackieOutput.php.html#363\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::wasPreviouslyUnbilled<\/a>"],[0,4,"<a href=\"ApplyNewJackieOutput.php.html#399\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::disputeBookings<\/a>"],[0,1,"<a href=\"ApplyNewJackieOutput.php.html#439\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ApplyNewJackieOutput::outputResults<\/a>"],[0,1,"<a href=\"ClearBadUserIds.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ClearBadUserIds::getCategory<\/a>"],[0,1,"<a href=\"ClearBadUserIds.php.html#14\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ClearBadUserIds::getName<\/a>"],[0,1,"<a href=\"ClearBadUserIds.php.html#19\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ClearBadUserIds::getInputs<\/a>"],[0,2,"<a href=\"ClearBadUserIds.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ClearBadUserIds::execute<\/a>"],[0,1,"<a href=\"ExportMoveIns2.php.html#24\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns2::getCategory<\/a>"],[0,1,"<a href=\"ExportMoveIns2.php.html#29\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns2::getName<\/a>"],[0,1,"<a href=\"ExportMoveIns2.php.html#34\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns2::getInputs<\/a>"],[0,1,"<a href=\"ExportMoveIns2.php.html#46\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns2::isDownload<\/a>"],[0,51,"<a href=\"ExportMoveIns2.php.html#51\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns2::execute<\/a>"],[0,1,"<a href=\"ExportMoveIns2.php.html#264\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns2::outputHeaders<\/a>"],[0,1,"<a href=\"ReverseCDP.php.html#14\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ReverseCDP::getCategory<\/a>"],[0,1,"<a href=\"ReverseCDP.php.html#19\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ReverseCDP::getName<\/a>"],[0,1,"<a href=\"ReverseCDP.php.html#24\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ReverseCDP::getInputs<\/a>"],[0,7,"<a href=\"ReverseCDP.php.html#54\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ReverseCDP::execute<\/a>"],[0,1,"<a href=\"RunCdpOnAPI.php.html#15\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnAPI::showInProduction<\/a>"],[0,1,"<a href=\"RunCdpOnAPI.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnAPI::getCategory<\/a>"],[0,1,"<a href=\"RunCdpOnAPI.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnAPI::getName<\/a>"],[0,3,"<a href=\"RunCdpOnAPI.php.html#30\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnAPI::getInputs<\/a>"],[0,3,"<a href=\"RunCdpOnAPI.php.html#105\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnAPI::execute<\/a>"],[0,1,"<a href=\"RunCdpOnMoveInsFile.php.html#15\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnMoveInsFile::getCategory<\/a>"],[0,1,"<a href=\"RunCdpOnMoveInsFile.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnMoveInsFile::getName<\/a>"],[0,1,"<a href=\"RunCdpOnMoveInsFile.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnMoveInsFile::getDescription<\/a>"],[0,1,"<a href=\"RunCdpOnMoveInsFile.php.html#33\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnMoveInsFile::getInputs<\/a>"],[0,1,"<a href=\"RunCdpOnMoveInsFile.php.html#117\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\RunCdpOnMoveInsFile::execute<\/a>"],[0,1,"<a href=\"deprecated\/ExportMoveIns.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::isDeprecated<\/a>"],[0,1,"<a href=\"deprecated\/ExportMoveIns.php.html#27\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::getCategory<\/a>"],[0,1,"<a href=\"deprecated\/ExportMoveIns.php.html#32\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::getName<\/a>"],[0,1,"<a href=\"deprecated\/ExportMoveIns.php.html#37\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::getInputs<\/a>"],[0,1,"<a href=\"deprecated\/ExportMoveIns.php.html#52\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::isDownload<\/a>"],[0,22,"<a href=\"deprecated\/ExportMoveIns.php.html#57\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::execute<\/a>"],[0,24,"<a href=\"deprecated\/ExportMoveIns.php.html#207\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::outputRecord<\/a>"],[0,1,"<a href=\"deprecated\/ExportMoveIns.php.html#242\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::outputHeaders<\/a>"],[0,1,"<a href=\"deprecated\/ImportMoveIns.php.html#17\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::getCategory<\/a>"],[0,1,"<a href=\"deprecated\/ImportMoveIns.php.html#22\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::getName<\/a>"],[0,4,"<a href=\"deprecated\/ImportMoveIns.php.html#27\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::getInputs<\/a>"],[0,10,"<a href=\"deprecated\/ImportMoveIns.php.html#51\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::execute<\/a>"],[0,15,"<a href=\"deprecated\/ImportMoveIns.php.html#127\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::processLine<\/a>"],[0,1,"<a href=\"deprecated\/ImportMoveInsSiteLink.php.html#15\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink::getCategory<\/a>"],[0,1,"<a href=\"deprecated\/ImportMoveInsSiteLink.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink::getName<\/a>"],[0,4,"<a href=\"deprecated\/ImportMoveInsSiteLink.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink::getInputs<\/a>"],[0,22,"<a href=\"deprecated\/ImportMoveInsSiteLink.php.html#59\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink::execute<\/a>"],[0,1,"<a href=\"deprecated\/MatchMoveIns.php.html#23\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::getCategory<\/a>"],[0,1,"<a href=\"deprecated\/MatchMoveIns.php.html#28\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::getName<\/a>"],[0,1,"<a href=\"deprecated\/MatchMoveIns.php.html#33\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::getInputs<\/a>"],[0,26,"<a href=\"deprecated\/MatchMoveIns.php.html#51\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::execute<\/a>"],[0,1,"<a href=\"deprecated\/MatchMoveIns.php.html#346\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::outputBooking<\/a>"],[0,4,"<a href=\"deprecated\/MatchMoveIns.php.html#358\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::mapBooking<\/a>"],[0,1,"<a href=\"deprecated\/MatchMoveIns2.php.html#16\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2::getCategory<\/a>"],[0,1,"<a href=\"deprecated\/MatchMoveIns2.php.html#21\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2::getName<\/a>"],[0,4,"<a href=\"deprecated\/MatchMoveIns2.php.html#26\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2::getInputs<\/a>"],[0,20,"<a href=\"deprecated\/MatchMoveIns2.php.html#54\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2::execute<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

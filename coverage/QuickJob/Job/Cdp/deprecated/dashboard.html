<!DOCTYPE html>
<html lang="en">
 <head>
  <meta charset="UTF-8">
  <title>Dashboard for /var/www/service/src/QuickJob/Job/Cdp/deprecated</title>
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <link href="../../../../_css/bootstrap.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/nv.d3.min.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/style.css?v=10.1.16" rel="stylesheet" type="text/css">
  <link href="../../../../_css/custom.css" rel="stylesheet" type="text/css">
 </head>
 <body>
  <header>
   <div class="container-fluid">
    <div class="row">
     <div class="col-md-12">
      <nav aria-label="breadcrumb">
       <ol class="breadcrumb">
         <li class="breadcrumb-item"><a href="../../../../index.html">/var/www/service/src</a></li>
         <li class="breadcrumb-item"><a href="../../../index.html">QuickJob</a></li>
         <li class="breadcrumb-item"><a href="../../index.html">Job</a></li>
         <li class="breadcrumb-item"><a href="../index.html">Cdp</a></li>
         <li class="breadcrumb-item"><a href="index.html">deprecated</a></li>
         <li class="breadcrumb-item active">(Dashboard)</li>

       </ol>
      </nav>
     </div>
    </div>
   </div>
  </header>
  <div class="container-fluid">
   <div class="row">
    <div class="col-md-12">
     <h2>Classes</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="classCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="classComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportMoveIns.php.html#9">Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveInsSiteLink.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns.php.html#12">Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns</a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns2.php.html#12">Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2</a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Class</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportMoveIns.php.html#9">Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns</a></td><td class="text-right">2756</td></tr>
       <tr><td><a href="MatchMoveIns.php.html#12">Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns</a></td><td class="text-right">1190</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns</a></td><td class="text-right">992</td></tr>
       <tr><td><a href="ImportMoveInsSiteLink.php.html#11">Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink</a></td><td class="text-right">812</td></tr>
       <tr><td><a href="MatchMoveIns2.php.html#12">Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2</a></td><td class="text-right">702</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-12">
     <h2>Methods</h2>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Coverage Distribution</h3>
     <div id="methodCoverageDistribution" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Complexity</h3>
     <div id="methodComplexity" style="height: 300px;">
       <svg></svg>
     </div>
    </div>
   </div>
   <div class="row">
    <div class="col-md-6">
     <h3>Insufficient Coverage</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right">Coverage</th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="ExportMoveIns.php.html#11"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::isDeprecated">isDeprecated</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns.php.html#27"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns.php.html#32"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns.php.html#37"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns.php.html#52"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::isDownload">isDownload</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns.php.html#57"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns.php.html#207"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::outputRecord">outputRecord</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ExportMoveIns.php.html#242"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::outputHeaders">outputHeaders</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#17"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#22"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#27"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#127"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::processLine">processLine</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveInsSiteLink.php.html#15"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveInsSiteLink.php.html#20"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveInsSiteLink.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="ImportMoveInsSiteLink.php.html#59"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns.php.html#23"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns.php.html#28"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns.php.html#33"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns.php.html#346"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::outputBooking">outputBooking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns.php.html#358"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::mapBooking">mapBooking</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns2.php.html#16"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::getCategory">getCategory</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns2.php.html#21"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::getName">getName</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns2.php.html#26"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::getInputs">getInputs</abbr></a></td><td class="text-right">0%</td></tr>
       <tr><td><a href="MatchMoveIns2.php.html#54"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::execute">execute</abbr></a></td><td class="text-right">0%</td></tr>

       </tbody>
      </table>
     </div>
    </div>
    <div class="col-md-6">
     <h3>Project Risks</h3>
     <div class="scrollbox">
      <table class="table">
       <thead>
        <tr>
         <th>Method</th>
         <th class="text-right"><abbr title="Change Risk Anti-Patterns (CRAP) Index">CRAP</abbr></th>
        </tr>
       </thead>
       <tbody>
       <tr><td><a href="MatchMoveIns.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::execute">execute</abbr></a></td><td class="text-right">702</td></tr>
       <tr><td><a href="ExportMoveIns.php.html#207"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::outputRecord">outputRecord</abbr></a></td><td class="text-right">600</td></tr>
       <tr><td><a href="ExportMoveIns.php.html#57"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ExportMoveIns::execute">execute</abbr></a></td><td class="text-right">506</td></tr>
       <tr><td><a href="ImportMoveInsSiteLink.php.html#59"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::execute">execute</abbr></a></td><td class="text-right">506</td></tr>
       <tr><td><a href="MatchMoveIns2.php.html#54"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::execute">execute</abbr></a></td><td class="text-right">420</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#127"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::processLine">processLine</abbr></a></td><td class="text-right">240</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#51"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::execute">execute</abbr></a></td><td class="text-right">110</td></tr>
       <tr><td><a href="ImportMoveIns.php.html#27"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveIns::getInputs">getInputs</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="ImportMoveInsSiteLink.php.html#25"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\ImportMoveInsSiteLink::getInputs">getInputs</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MatchMoveIns.php.html#358"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns::mapBooking">mapBooking</abbr></a></td><td class="text-right">20</td></tr>
       <tr><td><a href="MatchMoveIns2.php.html#26"><abbr title="Sparefoot\PitaService\QuickJob\Job\Cdp\MatchMoveIns2::getInputs">getInputs</abbr></a></td><td class="text-right">20</td></tr>

       </tbody>
      </table>
     </div>
    </div>
   </div>
   <footer>
    <hr/>
    <p>
     <small>Generated by <a href="https://github.com/sebastianbergmann/php-code-coverage" target="_top">php-code-coverage 10.1.16</a> using <a href="https://www.php.net/" target="_top">PHP 8.2.27</a> and <a href="https://phpunit.de/">PHPUnit 10.5.46</a> at Wed Jun 4 2:09:10 CDT 2025.</small>
    </p>
   </footer>
  </div>
  <script src="../../../../_js/jquery.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script src="../../../../_js/nv.d3.min.js?v=10.1.16" type="text/javascript"></script>
  <script type="text/javascript">
$(document).ready(function() {
  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#classCoverageDistribution svg')
      .datum(getCoverageDistributionData([5,0,0,0,0,0,0,0,0,0,0,0], "Class Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.multiBarChart();
    chart.tooltips(false)
      .showControls(false)
      .showLegend(false)
      .reduceXTicks(false)
      .staggerLabels(true)
      .yAxis.tickFormat(d3.format('d'));

    d3.select('#methodCoverageDistribution svg')
      .datum(getCoverageDistributionData([27,0,0,0,0,0,0,0,0,0,0,0], "Method Coverage"))
      .transition().duration(500).call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getCoverageDistributionData(data, label) {
    var labels = [
      '0%',
      '0-10%',
      '10-20%',
      '20-30%',
      '30-40%',
      '40-50%',
      '50-60%',
      '60-70%',
      '70-80%',
      '80-90%',
      '90-100%',
      '100%'
    ];
    var values = [];
    $.each(labels, function(key) {
      values.push({x: labels[key], y: data[key]});
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Cyclomatic Complexity');

    d3.select('#classComplexity svg')
      .datum(getComplexityData([[0,52,"<a href=\"ExportMoveIns.php.html#9\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns<\/a>"],[0,31,"<a href=\"ImportMoveIns.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns<\/a>"],[0,28,"<a href=\"ImportMoveInsSiteLink.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink<\/a>"],[0,34,"<a href=\"MatchMoveIns.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns<\/a>"],[0,26,"<a href=\"MatchMoveIns2.php.html#12\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2<\/a>"]], 'Class Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  nv.addGraph(function() {
    var chart = nv.models.scatterChart()
      .showDistX(true)
      .showDistY(true)
      .showLegend(false)
      .forceX([0, 100]);
    chart.tooltipContent(function(graph) {
      return '<p>' + graph.point.class + '</p>';
    });

    chart.xAxis.axisLabel('Code Coverage (in percent)');
    chart.yAxis.axisLabel('Method Complexity');

    d3.select('#methodComplexity svg')
      .datum(getComplexityData([[0,1,"<a href=\"ExportMoveIns.php.html#11\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::isDeprecated<\/a>"],[0,1,"<a href=\"ExportMoveIns.php.html#27\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::getCategory<\/a>"],[0,1,"<a href=\"ExportMoveIns.php.html#32\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::getName<\/a>"],[0,1,"<a href=\"ExportMoveIns.php.html#37\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::getInputs<\/a>"],[0,1,"<a href=\"ExportMoveIns.php.html#52\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::isDownload<\/a>"],[0,22,"<a href=\"ExportMoveIns.php.html#57\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::execute<\/a>"],[0,24,"<a href=\"ExportMoveIns.php.html#207\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::outputRecord<\/a>"],[0,1,"<a href=\"ExportMoveIns.php.html#242\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ExportMoveIns::outputHeaders<\/a>"],[0,1,"<a href=\"ImportMoveIns.php.html#17\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::getCategory<\/a>"],[0,1,"<a href=\"ImportMoveIns.php.html#22\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::getName<\/a>"],[0,4,"<a href=\"ImportMoveIns.php.html#27\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::getInputs<\/a>"],[0,10,"<a href=\"ImportMoveIns.php.html#51\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::execute<\/a>"],[0,15,"<a href=\"ImportMoveIns.php.html#127\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveIns::processLine<\/a>"],[0,1,"<a href=\"ImportMoveInsSiteLink.php.html#15\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink::getCategory<\/a>"],[0,1,"<a href=\"ImportMoveInsSiteLink.php.html#20\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink::getName<\/a>"],[0,4,"<a href=\"ImportMoveInsSiteLink.php.html#25\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink::getInputs<\/a>"],[0,22,"<a href=\"ImportMoveInsSiteLink.php.html#59\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\ImportMoveInsSiteLink::execute<\/a>"],[0,1,"<a href=\"MatchMoveIns.php.html#23\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::getCategory<\/a>"],[0,1,"<a href=\"MatchMoveIns.php.html#28\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::getName<\/a>"],[0,1,"<a href=\"MatchMoveIns.php.html#33\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::getInputs<\/a>"],[0,26,"<a href=\"MatchMoveIns.php.html#51\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::execute<\/a>"],[0,1,"<a href=\"MatchMoveIns.php.html#346\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::outputBooking<\/a>"],[0,4,"<a href=\"MatchMoveIns.php.html#358\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns::mapBooking<\/a>"],[0,1,"<a href=\"MatchMoveIns2.php.html#16\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2::getCategory<\/a>"],[0,1,"<a href=\"MatchMoveIns2.php.html#21\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2::getName<\/a>"],[0,4,"<a href=\"MatchMoveIns2.php.html#26\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2::getInputs<\/a>"],[0,20,"<a href=\"MatchMoveIns2.php.html#54\">Sparefoot\\PitaService\\QuickJob\\Job\\Cdp\\MatchMoveIns2::execute<\/a>"]], 'Method Complexity'))
      .transition()
      .duration(500)
      .call(chart);

    nv.utils.windowResize(chart.update);

    return chart;
  });

  function getComplexityData(data, label) {
    var values = [];
    $.each(data, function(key) {
      var value = Math.round(data[key][0]*100) / 100;
      values.push({
        x: value,
        y: data[key][1],
        class: data[key][2],
        size: 0.05,
        shape: 'diamond'
      });
    });

    return [
      {
        key: label,
        values: values,
        color: "#4572A7"
      }
    ];
  }
});
  </script>
 </body>
</html>

{% extends 'login-base.html.twig' %}

{% block content %}
    {% if view.error %}
        <p class="alert alert-error">{{ view.error }}</p>
    {% endif %}

    <script>
        function displayNotification() {
            this.document.location.href = '/login/forgotpassword?email=' + document.getElementById("username").value;
            alert("IT has been notified that " + document.getElementById("username").value + " forgot his password.");
        }
    </script>    

    <div class="grid_6 container">
        <div class="col-md-4"></div>
        <div class="col-md-4">
            <h3>Sign In</h3>
            <form id="login_form" name="login_form" method="post" action="{{ path('login_check') }}">
                <input type="hidden" name="_csrf_token" value="{{ csrf_token('authenticate') }}">
                
                <label for="username">Email Address</label><br />
                <input type="text"   id="username" name="_username" value="{{ view.last_username }}" class="form-control" /><br />

                <label for="password">Password</label><br />
                <input type="password"   id="password" name="_password" class="form-control" /><br />

                <div class="form-inline">
                    <input id="login_button" name="login_button" type="submit" class="btn btn-primary" value="Sign In" />&nbsp;&nbsp;
                    <label for="remember_me" class="checkbox">
                        <input id="remember_me" name="_remember_me" type="checkbox" checked="checked"> Stay signed in
                    </label>
                </div>
                <br />
                <p><a id="frown" onclick="displayNotification()" style="cursor:pointer;">Forgot your password?</a></p>
            </form>
        </div>
        <div class="col-md-4"></div>
    </div>
{% endblock %}
